"""
Клавиатуры для dev-режима суперпользователя.

Выбор ролей и управление виртуальными правами.
"""
from aiogram.types import ReplyKeyboardMarkup, KeyboardButton
from utils.dev_session import get_role_display_name, get_role_description


def get_dev_role_selection_keyboard() -> ReplyKeyboardMarkup:
    """
    Клавиатура выбора роли для суперпользователя

    Returns:
        Клавиатура с кнопками выбора роли
    """
    keyboard = ReplyKeyboardMarkup(
        keyboard=[
            [KeyboardButton(text="🧱 Рабочий")],
            [KeyboardButton(text="📋 Директор")],
            [KeyboardButton(text="👑 Администратор")],
            [KeyboardButton(text="❌ Отмена")]
        ],
        resize_keyboard=True,
        input_field_placeholder="Выберите роль для тестирования"
    )

    return keyboard


def get_dev_role_confirmation_keyboard(role: str) -> ReplyKeyboardMarkup:
    """
    Клавиатура подтверждения выбора роли

    Args:
        role: Выбранная роль

    Returns:
        Клавиатура подтверждения
    """
    keyboard = ReplyKeyboardMarkup(
        keyboard=[
            [KeyboardButton(text="✅ Подтвердить")],
            [KeyboardButton(text="🔄 Выбрать другую роль")],
            [KeyboardButton(text="❌ Отмена")]
        ],
        resize_keyboard=True,
        input_field_placeholder="Подтвердите выбор роли"
    )

    return keyboard


def get_dev_role_info_text(role: str) -> str:
    """
    Возвращает информационный текст о выбранной роли
    
    Args:
        role: Роль
        
    Returns:
        Текст с описанием роли
    """
    role_name = get_role_display_name(role)
    description = get_role_description(role)
    
    text = f"🧪 <b>DEV MODE - Выбор роли</b>\n\n"
    text += f"Выбрана роль: <b>{role_name}</b>\n\n"
    text += f"<i>Возможности:</i>\n{description}\n\n"
    text += f"⚠️ <i>Это тестовый режим с виртуальными правами</i>"
    
    return text


def get_dev_success_text(role: str) -> str:
    """
    Возвращает текст успешного выбора роли
    
    Args:
        role: Выбранная роль
        
    Returns:
        Текст успеха
    """
    role_name = get_role_display_name(role)
    
    text = f"✅ <b>DEV MODE активирован</b>\n\n"
    text += f"Активная роль: <b>{role_name}</b>\n\n"
    text += f"🧪 Вы можете тестировать все функции этой роли\n"
    text += f"📊 Используются виртуальные права и фиктивные данные\n\n"
    text += f"Для смены роли отправьте /start снова"
    
    return text
