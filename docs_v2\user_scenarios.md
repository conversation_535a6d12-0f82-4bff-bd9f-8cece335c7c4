# 🎭 ПОЛЬЗОВАТЕЛЬСКИЕ СЦЕНАРИИ WORKLOG MVP v2.0

## 🎯 Общие принципы

### Временная система ролей (MVP)
В версии MVP для упрощения тестирования используется ручной выбор роли после команды `/start`. После успешного тестирования будет внедрена система токен-ссылок.

## 🚀 Базовый сценарий: Первый запуск

### `/start` - Запуск бота и выбор роли

```mermaid
sequenceDiagram
    participant U as 👤 Пользователь
    participant B as 🤖 Бот
    participant DB as 🗄️ База данных
    
    U->>B: /start
    B->>DB: Проверка существования пользователя
    alt Новый пользователь
        B->>U: Приветствие + выбор роли
        Note over U,B: [👑 Админ] [👨‍💼 Директор] [👷 Рабочий]
        U->>B: Выбирает роль
        B->>DB: Создаёт пользователя с выбранной ролью
        B->>U: Показывает меню роли
    else Существующий пользователь
        B->>U: Приветствие + главное меню роли
    end
```

#### Детальный сценарий

**Шаг 1: Приветствие**
```
🎉 Добро пожаловать в WorkLog MVP!

Система учёта рабочего времени для строительных компаний.

Для начала работы выберите вашу роль:
```

**Inline-клавиатура:**
```
[👑 Администратор] [👨‍💼 Директор]
[👷 Рабочий]
```

**Шаг 2: После выбора роли**
```
✅ Роль выбрана: {роль}

Добро пожаловать в систему! Теперь вы можете использовать все функции, доступные для вашей роли.
```

**Переход к главному меню соответствующей роли**

---

## 👷 СЦЕНАРИИ ДЛЯ РАБОЧЕГО

### Главное меню рабочего

**Текст:**
```
👷 Меню рабочего

Выберите действие:
```

**Inline-клавиатура:**
```
[➕ Добавить работу] [📋 Мои работы]
[🏗️ Мои проекты] [📝 Заметки]
[🛠️ Запросы] [👤 Профиль]
```

### `/addwork` - Добавление записи о работе

```mermaid
stateDiagram-v2
    [*] --> SelectProject: /addwork
    SelectProject --> SelectWorkType: Проект выбран
    SelectWorkType --> EnterDescription: Тип выбран
    EnterDescription --> EnterQuantity: Описание введено
    EnterQuantity --> ConfirmEntry: Количество введено
    ConfirmEntry --> [*]: Подтверждено
    ConfirmEntry --> SelectProject: Отменено
```

#### Детальный сценарий

**Шаг 1: Выбор проекта**
```
🏗️ Выберите проект для записи о работе:
```

**Inline-клавиатура (динамическая):**
```
[🏗️ Проект "Дом на Ленина"] [🏗️ Проект "Офис центр"]
[🔙 Назад]
```

**Шаг 2: Выбор типа работы**
```
⚙️ Выберите тип работы:

Проект: Дом на Ленина
```

**Inline-клавиатура:**
```
[⏰ Почасовая работа] [📦 Сдельная работа]
[🔙 Назад]
```

**Шаг 3: Ввод описания**
```
📝 Введите описание выполненной работы:

Проект: Дом на Ленина
Тип: Почасовая работа

Пример: "Кладка кирпича на 2 этаже"
```

**Inline-клавиатура:**
```
[❌ Отменить]
```

**Шаг 4: Ввод количества**
```
🔢 Введите количество часов:

Проект: Дом на Ленина
Тип: Почасовая работа
Описание: Кладка кирпича на 2 этаже

Введите число (например: 8 или 8.5):
```

**Inline-клавиатура:**
```
[🔙 Назад] [❌ Отменить]
```

**Шаг 5: Подтверждение**
```
✅ Подтвердите запись о работе:

📅 Дата: 27.06.2025
🏗️ Проект: Дом на Ленина
⚙️ Тип: Почасовая работа (500₽/час)
📝 Описание: Кладка кирпича на 2 этаже
⏰ Количество: 8.0 часов
💰 Сумма: 4,000₽
```

**Inline-клавиатура:**
```
[✅ Подтвердить] [✏️ Редактировать]
[❌ Отменить]
```

**Результат:**
```
🎉 Запись о работе успешно добавлена!

📊 Ваша статистика за сегодня:
• Записей: 3
• Часов: 24.0
• Сумма: 12,000₽
```

### `/list` - Просмотр записей о работе

#### Сценарий

**Шаг 1: Выбор фильтра**
```
📋 Мои записи о работе

Выберите период для просмотра:
```

**Inline-клавиатура:**
```
[📅 Сегодня] [📅 Эта неделя]
[📅 Этот месяц] [📅 Выбрать период]
[🏗️ По проекту]
```

**Шаг 2: Список записей**
```
📋 Записи за сегодня (27.06.2025)

1️⃣ 🏗️ Дом на Ленина
   ⏰ 8.0 ч • 💰 4,000₽
   📝 Кладка кирпича на 2 этаже

2️⃣ 🏗️ Офис центр  
   ⏰ 6.0 ч • 💰 3,600₽
   📝 Монтаж гипсокартона

📊 Итого: 14.0 часов • 7,600₽
```

**Inline-клавиатура:**
```
[✏️ Редактировать] [🗑️ Удалить]
[📊 Статистика] [🔙 Назад]
```

### `/edit` - Редактирование записи

#### Сценарий

**Шаг 1: Выбор записи**
```
✏️ Редактирование записей

Выберите запись для редактирования:
```

**Inline-клавиатура (динамическая):**
```
[1️⃣ Дом на Ленина • 8ч • 4,000₽]
[2️⃣ Офис центр • 6ч • 3,600₽]
[🔙 Назад]
```

**Шаг 2: Выбор действия**
```
✏️ Редактирование записи

🏗️ Проект: Дом на Ленина
📝 Описание: Кладка кирпича на 2 этаже
⏰ Количество: 8.0 часов
💰 Сумма: 4,000₽

Что хотите изменить?
```

**Inline-клавиатура:**
```
[📅 Дату] [📝 Описание]
[🔢 Количество] [⚙️ Тип работы]
[💾 Сохранить] [🔙 Назад]
```

### `/delete` - Удаление записи

#### Сценарий

**Шаг 1: Выбор записи**
```
🗑️ Удаление записей

⚠️ Внимание! Удаление записи нельзя отменить.

Выберите запись для удаления:
```

**Шаг 2: Подтверждение**
```
⚠️ Подтвердите удаление

🏗️ Проект: Дом на Ленина
📝 Описание: Кладка кирпича на 2 этаже
⏰ Количество: 8.0 часов
💰 Сумма: 4,000₽

Вы уверены, что хотите удалить эту запись?
```

**Inline-клавиатура:**
```
[🗑️ Да, удалить] [❌ Отменить]
```

### `/report` - Личный отчёт

#### Сценарий

**Шаг 1: Выбор периода**
```
📊 Создание отчёта

Выберите период для отчёта:
```

**Inline-клавиатура:**
```
[📅 Сегодня] [📅 Эта неделя]
[📅 Этот месяц] [📅 Выбрать период]
```

**Шаг 2: Отчёт**
```
📊 Отчёт за неделю (21.06 - 27.06.2025)

👷 Рабочий: Иван Петров

📈 Общая статистика:
• Рабочих дней: 5
• Общее время: 40.0 часов
• Общая сумма: 20,000₽
• Средняя ставка: 500₽/час

🏗️ По проектам:
• Дом на Ленина: 24ч • 12,000₽
• Офис центр: 16ч • 8,000₽

📅 По дням:
• 21.06: 8ч • 4,000₽
• 22.06: 8ч • 4,000₽
• 23.06: 8ч • 4,000₽
• 24.06: 8ч • 4,000₽
• 25.06: 8ч • 4,000₽
```

**Inline-клавиатура:**
```
[📄 Экспорт PDF] [📊 Экспорт Excel]
[🔙 Назад]
```

### `/notes` - Заметки

#### Сценарий добавления заметки

**Шаг 1: Ввод заметки**
```
📝 Добавление заметки

Введите текст заметки:

Пример: "Завтра не забыть взять дрель"
```

**Шаг 2: Подтверждение**
```
✅ Заметка добавлена!

📝 "Завтра не забыть взять дрель"
📅 27.06.2025 15:30
```

#### Просмотр заметок

```
📝 Мои заметки

1️⃣ 📅 27.06.2025 15:30
   "Завтра не забыть взять дрель"

2️⃣ 📅 26.06.2025 09:15
   "Закончить кладку до обеда"

3️⃣ 📅 25.06.2025 17:45
   "Заказать материалы на следующую неделю"
```

**Reply-клавиатура:**
```
[➕ Добавить заметку] [🗑️ Удалить]
[🔙 Назад]
```

### `/needs` - Запросы потребностей

#### Сценарий создания запроса

**Шаг 1: Ввод запроса**
```
🛠️ Запрос потребностей

Опишите, что вам нужно для работы:

Пример: "Нужен перфоратор на завтра"
```

**Шаг 2: Подтверждение**
```
✅ Запрос отправлен директору!

🛠️ "Нужен перфоратор на завтра"
📅 27.06.2025 15:30
📋 Статус: Ожидает рассмотрения
```

#### Просмотр запросов

```
🛠️ Мои запросы

1️⃣ 📅 27.06.2025 15:30
   "Нужен перфоратор на завтра"
   📋 Статус: ⏳ Ожидает рассмотрения

2️⃣ 📅 26.06.2025 09:15  
   "Дополнительные саморезы"
   📋 Статус: ✅ Выполнено

3️⃣ 📅 25.06.2025 17:45
   "Защитные очки"
   📋 Статус: ❌ Отклонено
   💬 Комментарий: "Есть на складе"
```

**Inline-клавиатура:**
```
[➕ Новый запрос] [🔄 Обновить]
[🔙 Назад]
```

---

## 👨‍💼 СЦЕНАРИИ ДЛЯ ДИРЕКТОРА

### Главное меню директора

**Текст:**
```
👨‍💼 Меню директора

Выберите действие:
```

**Inline-клавиатура:**
```
[🏗️ Управление проектами] [👷 Управление рабочими]
[📊 Отчёты] [🛠️ Запросы рабочих]
[📋 Категории работ] [👤 Профиль]
```

### `/newproject` - Создание проекта

```mermaid
stateDiagram-v2
    [*] --> EnterName: /newproject
    EnterName --> EnterAddress: Название введено
    EnterAddress --> SetRates: Адрес введён
    SetRates --> AddWorkTypes: Ставки установлены
    AddWorkTypes --> ConfirmProject: Типы добавлены
    ConfirmProject --> [*]: Подтверждено
    AddWorkTypes --> AddWorkTypes: Добавить ещё тип
```

#### Детальный сценарий

**Шаг 1: Ввод названия**
```
🏗️ Создание нового проекта

Введите название проекта:

Пример: "Жилой комплекс Солнечный"
```

**Шаг 2: Ввод адреса**
```
📍 Введите адрес проекта:

Проект: Жилой комплекс Солнечный

Пример: "ул. Солнечная, д. 15"
```

**Inline-клавиатура:**
```
[⏭️ Пропустить] [🔙 Назад]
```

**Шаг 3: Установка базовых ставок**
```
💰 Установите базовые ставки для проекта:

Проект: Жилой комплекс Солнечный
Адрес: ул. Солнечная, д. 15

Введите почасовую ставку (₽/час):
```

**Шаг 4: Сдельная ставка**
```
💰 Введите базовую сдельную ставку (₽/единица):

Проект: Жилой комплекс Солнечный
Почасовая ставка: 600₽/час
```

**Шаг 5: Добавление типов работ**
```
⚙️ Добавление типов работ

Проект: Жилой комплекс Солнечный

Текущие типы работ:
1️⃣ Кладка кирпича • 600₽/м²
2️⃣ Штукатурка • 400₽/м²

Хотите добавить ещё один тип работы?
```

**Inline-клавиатура:**
```
[➕ Добавить тип] [✅ Завершить]
[🔙 Назад]
```

**Шаг 6: Подтверждение**
```
✅ Подтвердите создание проекта:

🏗️ Название: Жилой комплекс Солнечный
📍 Адрес: ул. Солнечная, д. 15
💰 Почасовая ставка: 600₽/час
💰 Сдельная ставка: 100₽/ед

⚙️ Типы работ:
• Кладка кирпича: 600₽/м²
• Штукатурка: 400₽/м²
• Монтаж: 500₽/м²

👷 Назначенные рабочие: 0
```

**Inline-клавиатура:**
```
[✅ Создать проект] [✏️ Редактировать]
[❌ Отменить]
```

### Управление рабочими

#### Просмотр рабочих

```
👷 Управление рабочими

📊 Статистика:
• Всего рабочих: 12
• Активных: 10
• На проектах: 8

👥 Список рабочих:

1️⃣ 👷 Иван Петров
   📱 @ivan_petrov
   🏗️ Проект: Жилой комплекс
   📊 За месяц: 160ч • 80,000₽

2️⃣ 👷 Сергей Иванов  
   📱 @sergey_ivanov
   🏗️ Проект: Офисный центр
   📊 За месяц: 140ч • 70,000₽
```

**Inline-клавиатура:**
```
[👤 Подробнее] [🏗️ Назначить на проект]
[📊 Статистика] [➕ Пригласить рабочего]
[🔙 Назад]
```

#### Создание токена приглашения

```
📨 Создание приглашения для рабочего

Введите описание токена:

Пример: "Для нового каменщика"
```

**Результат:**
```
✅ Токен создан!

🎫 Токен: WORKER_ABC123XYZ
📝 Описание: Для нового каменщика
⏰ Действует: 24 часа
🔗 Ссылка: t.me/WorkLogBot?start=WORKER_ABC123XYZ

Отправьте эту ссылку рабочему для регистрации.
```

### Отчёты директора

#### Выбор типа отчёта

```
📊 Создание отчётов

Выберите тип отчёта:
```

**Inline-клавиатура:**
```
[🏗️ По проектам] [👷 По рабочим]
[📅 По периодам] [💰 Финансовый]
[📈 Сводный отчёт]
```

#### Отчёт по проектам

```
📊 Отчёт по проектам за месяц

🏢 Компания: ООО "СтройМастер"
📅 Период: Июнь 2025

📈 Общая статистика:
• Активных проектов: 3
• Общее время: 1,200 часов
• Общая сумма: 600,000₽
• Средняя ставка: 500₽/час

🏗️ Детализация по проектам:

1️⃣ Жилой комплекс Солнечный
   👷 Рабочих: 5
   ⏰ Время: 600ч
   💰 Сумма: 300,000₽
   📊 Прогресс: 75%

2️⃣ Офисный центр "Бизнес"
   👷 Рабочих: 3  
   ⏰ Время: 400ч
   💰 Сумма: 200,000₽
   📊 Прогресс: 50%

3️⃣ Частный дом на Ленина
   👷 Рабочих: 2
   ⏰ Время: 200ч
   💰 Сумма: 100,000₽
   📊 Прогресс: 90%
```

**Inline-клавиатура:**
```
[📄 Экспорт PDF] [📊 Экспорт Excel]
[📧 Отправить на email] [🔙 Назад]
```

---

## 👑 СЦЕНАРИИ ДЛЯ АДМИНИСТРАТОРА

### Главное меню администратора

**Текст:**
```
👑 Меню администратора

Выберите действие:
```

**Inline-клавиатура:**
```
[🏢 Управление компаниями] [👥 Управление пользователями]
[📊 Системные отчёты] [⚙️ Настройки системы]
[🔧 Техническая информация] [👤 Профиль]
```

### Управление компаниями

```
🏢 Управление компаниями

📊 Статистика:
• Всего компаний: 15
• Активных: 12
• Пользователей: 156

🏢 Список компаний:

1️⃣ 🏢 ООО "СтройМастер"
   👥 Пользователей: 25
   🏗️ Проектов: 8
   📊 За месяц: 2,400ч • 1,200,000₽

2️⃣ 🏢 ИП Петров С.И.
   👥 Пользователей: 5
   🏗️ Проектов: 2  
   📊 За месяц: 400ч • 200,000₽
```

**Inline-клавиатура:**
```
[👁️ Подробнее] [✏️ Редактировать]
[➕ Добавить компанию] [📊 Статистика]
[🔙 Назад]
```

### Системные отчёты

```
📊 Системные отчёты

📈 Общая статистика системы:
• Всего пользователей: 156
• Активных за месяц: 142
• Записей о работе: 12,450
• Общее время: 24,900 часов
• Общая сумма: 12,450,000₽

🏢 Топ компаний по активности:
1. ООО "СтройМастер" - 2,400ч
2. ООО "БетонСтрой" - 1,800ч  
3. ИП Сидоров - 1,200ч

👷 Средняя активность рабочего:
• Часов в день: 8.2
• Дней в месяц: 22.5
• Записей в день: 3.1
```

**Inline-клавиатура:**
```
[📄 Полный отчёт] [📊 Экспорт данных]
[📧 Отправить отчёт] [🔙 Назад]
```

---

## 🔄 Общие принципы навигации

### Стандартные кнопки

- **🔙 Назад** - возврат к предыдущему экрану
- **🏠 Главное меню** - возврат в главное меню роли
- **❌ Отменить** - отмена текущей операции
- **✅ Подтвердить** - подтверждение действия
- **💾 Сохранить** - сохранение изменений

### Обработка ошибок

При возникновении ошибок пользователь получает понятное сообщение:

```
❌ Произошла ошибка

{Описание ошибки}

Попробуйте ещё раз или обратитесь к администратору.
```

**Inline-клавиатура:**
```
[🔄 Попробовать снова] [🏠 Главное меню]
```

### Подтверждение критических действий

Для важных действий (удаление, изменение ставок) всегда требуется подтверждение:

```
⚠️ Подтвердите действие

{Описание действия}

Это действие нельзя отменить.
```

**Inline-клавиатура:**
```
[✅ Да, подтверждаю] [❌ Отменить]
```
