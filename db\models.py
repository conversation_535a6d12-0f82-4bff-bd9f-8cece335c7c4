"""
Модели данных для Worklog Bot v2.0 (Финляндия)

SQLAlchemy 2.0 модели с финляндской локализацией.
"""
from datetime import datetime
from decimal import Decimal
from typing import Optional, List
from sqlalchemy import (
    BigInteger, Integer, String, Boolean, DateTime, Date, 
    Numeric, JSON, ForeignKey, UniqueConstraint
)
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func

from .database import Base


class User(Base):
    """Пользователи системы"""
    __tablename__ = "users"

    user_id: Mapped[int] = mapped_column(BigInteger, primary_key=True)
    display_name: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    active_project_id: Mapped[Optional[int]] = mapped_column(
        Integer, ForeignKey("projects.project_id"), nullable=True
    )
    active_company_id: Mapped[Optional[int]] = mapped_column(
        Integer, ForeignKey("companies.id"), nullable=True
    )
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), default=func.now(), server_default=func.now()
    )
    updated_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), default=func.now(), server_default=func.now(), onupdate=func.now(), nullable=True
    )
    
    # Relationships
    active_project: Mapped[Optional["Project"]] = relationship(
        "Project", foreign_keys=[active_project_id]
    )
    active_company: Mapped[Optional["Company"]] = relationship(
        "Company", foreign_keys=[active_company_id]
    )
    company_roles: Mapped[List["UserCompanyRole"]] = relationship(
        "UserCompanyRole", back_populates="user"
    )
    work_entries: Mapped[List["WorkEntry"]] = relationship(
        "WorkEntry", back_populates="user"
    )


class Company(Base):
    """Компании (финские форматы)"""
    __tablename__ = "companies"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    name: Mapped[str] = mapped_column(String(200), unique=True, nullable=False)
    business_id: Mapped[Optional[str]] = mapped_column(String(20))  # Y-tunnus
    address: Mapped[Optional[str]] = mapped_column(String(300))
    is_deleted: Mapped[bool] = mapped_column(Boolean, default=False)
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), default=func.now(), server_default=func.now()
    )
    updated_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), default=func.now(), server_default=func.now(), onupdate=func.now(), nullable=True
    )
    
    # Relationships
    user_roles: Mapped[List["UserCompanyRole"]] = relationship(
        "UserCompanyRole", back_populates="company"
    )
    projects: Mapped[List["Project"]] = relationship(
        "Project", back_populates="company"
    )
    tokens: Mapped[List["Token"]] = relationship(
        "Token", back_populates="company"
    )


class UserCompanyRole(Base):
    """Роли пользователей в компаниях"""
    __tablename__ = "user_company_roles"

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    user_id: Mapped[int] = mapped_column(
        BigInteger, ForeignKey("users.user_id"), nullable=False
    )
    company_id: Mapped[int] = mapped_column(
        Integer, ForeignKey("companies.id"), nullable=False
    )
    role: Mapped[str] = mapped_column(String(20), nullable=False)  # admin, director, worker
    permissions: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True, default=dict)
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), default=func.now(), server_default=func.now()
    )
    
    # Relationships
    user: Mapped["User"] = relationship("User", back_populates="company_roles")
    company: Mapped["Company"] = relationship("Company", back_populates="user_roles")
    
    # Уникальность: один пользователь - одна роль в компании
    __table_args__ = (
        UniqueConstraint('user_id', 'company_id', name='unique_user_company'),
    )


class Project(Base):
    """Проекты (финские адреса)"""
    __tablename__ = "projects"

    project_id: Mapped[int] = mapped_column(Integer, primary_key=True)
    created_by: Mapped[int] = mapped_column(BigInteger, ForeignKey("users.user_id"), nullable=False)
    name: Mapped[str] = mapped_column(String(100), nullable=False)
    address: Mapped[Optional[str]] = mapped_column(String(300))
    company_id: Mapped[int] = mapped_column(
        Integer, ForeignKey("companies.id"), nullable=False
    )
    is_deleted: Mapped[bool] = mapped_column(Boolean, default=False)
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), default=func.now(), server_default=func.now()
    )
    updated_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), default=func.now(), server_default=func.now(), onupdate=func.now(), nullable=True
    )
    
    # Relationships
    company: Mapped["Company"] = relationship("Company", back_populates="projects")
    work_types: Mapped[List["WorkType"]] = relationship(
        "WorkType", back_populates="project"
    )


class WorkType(Base):
    """Типы работ (евро)"""
    __tablename__ = "work_types"

    work_type_id: Mapped[int] = mapped_column(Integer, primary_key=True)
    name: Mapped[str] = mapped_column(String(100), nullable=False)
    unit: Mapped[str] = mapped_column(String(20), nullable=False)  # час, м², шт
    rate_type: Mapped[str] = mapped_column(String(20), nullable=False)  # fixed, per_unit
    value: Mapped[Decimal] = mapped_column(Numeric(10, 2), nullable=False)  # Ставка в евро
    hourly_rate: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 2), nullable=True)  # Почасовая ставка
    project_id: Mapped[int] = mapped_column(
        Integer, ForeignKey("projects.project_id"), nullable=False
    )
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), default=func.now(), server_default=func.now()
    )
    updated_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), default=func.now(), server_default=func.now(), onupdate=func.now(), nullable=True
    )

    # Дополнительные поля для совместимости с сервисами
    id: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)  # Алиас для work_type_id
    company_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey("companies.id"), nullable=True)  # Связь с компанией
    rate: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 2), nullable=True)  # Алиас для hourly_rate/value
    rate_type: Mapped[Optional[str]] = mapped_column(String(20), nullable=True, default="per_unit")  # Тип ставки из examples
    value: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 2), nullable=True)  # Алиас для hourly_rate
    is_deleted: Mapped[bool] = mapped_column(Boolean, default=False, nullable=True)

    # Relationships
    project: Mapped["Project"] = relationship("Project", back_populates="work_types")
    work_entries: Mapped[List["WorkEntry"]] = relationship(
        "WorkEntry", back_populates="work_type"
    )
    company: Mapped[Optional["Company"]] = relationship("Company")


class WorkEntry(Base):
    """Записи о работе (евро)"""
    __tablename__ = "work_entries"

    entry_id: Mapped[int] = mapped_column(Integer, primary_key=True)
    user_id: Mapped[int] = mapped_column(
        BigInteger, ForeignKey("users.user_id"), nullable=False
    )
    work_type_id: Mapped[int] = mapped_column(
        Integer, ForeignKey("work_types.work_type_id"), nullable=False
    )
    project_id: Mapped[int] = mapped_column(
        Integer, ForeignKey("projects.project_id"), nullable=False
    )
    company_id: Mapped[int] = mapped_column(
        Integer, ForeignKey("companies.id"), nullable=False
    )
    date: Mapped[datetime] = mapped_column(Date, nullable=False)
    description: Mapped[str] = mapped_column(String(500), nullable=False)
    quantity: Mapped[Decimal] = mapped_column(Numeric(10, 2), nullable=False)
    calculated_amount: Mapped[Decimal] = mapped_column(Numeric(10, 2), nullable=False)
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), default=func.now(), server_default=func.now()
    )
    updated_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), default=func.now(), server_default=func.now(), onupdate=func.now(), nullable=True
    )

    # Дополнительные поля для совместимости с сервисами
    id: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)  # Алиас для entry_id
    work_date: Mapped[Optional[datetime]] = mapped_column(Date, nullable=True)  # Алиас для date
    hours: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 2), nullable=True)  # Алиас для quantity
    total_amount: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 2), nullable=True)  # Алиас для calculated_amount
    sum_total: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 2), nullable=True)  # Алиас для calculated_amount (из examples)
    created_by: Mapped[Optional[int]] = mapped_column(BigInteger, nullable=True)  # Алиас для user_id (из examples)
    is_deleted: Mapped[bool] = mapped_column(Boolean, default=False, nullable=True)

    # Relationships
    user: Mapped["User"] = relationship("User", back_populates="work_entries")
    work_type: Mapped["WorkType"] = relationship("WorkType", back_populates="work_entries")
    project: Mapped["Project"] = relationship("Project")
    company: Mapped["Company"] = relationship("Company")


class Token(Base):
    """Токены регистрации"""
    __tablename__ = "tokens"

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    token: Mapped[str] = mapped_column(String(64), unique=True, nullable=False)
    role: Mapped[str] = mapped_column(String(20), nullable=False)  # director, worker
    company_id: Mapped[Optional[int]] = mapped_column(
        Integer, ForeignKey("companies.id"), nullable=True
    )
    created_by_user_id: Mapped[int] = mapped_column(BigInteger, nullable=False)
    used_by_user_id: Mapped[Optional[int]] = mapped_column(BigInteger, nullable=True)
    expires_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False)
    is_used: Mapped[bool] = mapped_column(Boolean, default=False)
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), default=func.now(), server_default=func.now()
    )

    # Relationships
    company: Mapped[Optional["Company"]] = relationship("Company", back_populates="tokens")
