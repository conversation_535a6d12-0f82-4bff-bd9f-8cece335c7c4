"""Fix schema issues for services compatibility

Revision ID: fix_schema_issues
Revises: 
Create Date: 2025-06-30 21:35:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'fix_schema_issues'
down_revision = '1fd597e95001'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Исправление схемы для совместимости с сервисами"""
    
    # 1. Добавляем недостающие поля в work_entries
    print("🔧 Добавляем поля в work_entries...")
    
    # Добавляем work_date как алиас для date
    op.execute("ALTER TABLE work_entries ADD COLUMN IF NOT EXISTS work_date DATE")
    op.execute("UPDATE work_entries SET work_date = date WHERE work_date IS NULL")
    
    # Добавляем hours как алиас для quantity  
    op.execute("ALTER TABLE work_entries ADD COLUMN IF NOT EXISTS hours NUMERIC")
    op.execute("UPDATE work_entries SET hours = quantity WHERE hours IS NULL")
    
    # Добавляем total_amount как алиас для calculated_amount
    op.execute("ALTER TABLE work_entries ADD COLUMN IF NOT EXISTS total_amount NUMERIC")
    op.execute("UPDATE work_entries SET total_amount = calculated_amount WHERE total_amount IS NULL")
    
    # Добавляем is_deleted
    op.execute("ALTER TABLE work_entries ADD COLUMN IF NOT EXISTS is_deleted BOOLEAN DEFAULT FALSE")
    
    # Добавляем id как алиас для entry_id
    op.execute("ALTER TABLE work_entries ADD COLUMN IF NOT EXISTS id INTEGER")
    op.execute("UPDATE work_entries SET id = entry_id WHERE id IS NULL")
    
    # 2. Исправляем work_types
    print("🔧 Исправляем work_types...")
    
    # Добавляем company_id
    op.execute("ALTER TABLE work_types ADD COLUMN IF NOT EXISTS company_id INTEGER")
    
    # Заполняем company_id из связанных проектов
    op.execute("""
        UPDATE work_types 
        SET company_id = (
            SELECT company_id 
            FROM projects 
            WHERE projects.project_id = work_types.project_id
        )
        WHERE company_id IS NULL
    """)
    
    # Добавляем rate как алиас для hourly_rate
    op.execute("ALTER TABLE work_types ADD COLUMN IF NOT EXISTS rate NUMERIC")
    op.execute("UPDATE work_types SET rate = COALESCE(hourly_rate, value) WHERE rate IS NULL")
    
    # Добавляем is_deleted
    op.execute("ALTER TABLE work_types ADD COLUMN IF NOT EXISTS is_deleted BOOLEAN DEFAULT FALSE")
    
    # Добавляем id как алиас для work_type_id
    op.execute("ALTER TABLE work_types ADD COLUMN IF NOT EXISTS id INTEGER")
    op.execute("UPDATE work_types SET id = work_type_id WHERE id IS NULL")
    
    print("✅ Схема исправлена!")


def downgrade() -> None:
    """Откат изменений"""
    
    # Удаляем добавленные поля из work_entries
    op.execute("ALTER TABLE work_entries DROP COLUMN IF EXISTS work_date")
    op.execute("ALTER TABLE work_entries DROP COLUMN IF EXISTS hours")
    op.execute("ALTER TABLE work_entries DROP COLUMN IF EXISTS total_amount")
    op.execute("ALTER TABLE work_entries DROP COLUMN IF EXISTS is_deleted")
    op.execute("ALTER TABLE work_entries DROP COLUMN IF EXISTS id")
    
    # Удаляем добавленные поля из work_types
    op.execute("ALTER TABLE work_types DROP COLUMN IF EXISTS company_id")
    op.execute("ALTER TABLE work_types DROP COLUMN IF EXISTS rate")
    op.execute("ALTER TABLE work_types DROP COLUMN IF EXISTS is_deleted")
    op.execute("ALTER TABLE work_types DROP COLUMN IF EXISTS id")
    
    print("✅ Откат выполнен!")
