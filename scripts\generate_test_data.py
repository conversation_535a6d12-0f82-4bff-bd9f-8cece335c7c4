"""
Скрипт для генерации тестовых данных

Создает тестовые компании, пользователей, проекты, типы работ и записи работ
для полноценного тестирования всех функций системы.
"""
import asyncio
import sys
import os
from datetime import datetime, date, timedelta
from pathlib import Path

# Добавляем корневую директорию в путь
sys.path.append(str(Path(__file__).parent.parent))

from db.database import async_session, init_db
from db.models import User, Company, UserCompanyRole, Project, WorkType, WorkEntry
from services.company_service import CompanyService
from services.work_type_service import WorkTypeService
from sqlalchemy import select


class TestDataGenerator:
    """Генератор тестовых данных"""

    def __init__(self):
        self.companies = []
        self.users = []
        self.projects = []
        self.work_types = []

    async def generate_all_data(self):
        """Генерация всех тестовых данных"""
        print("🚀 Начинаем генерацию тестовых данных...")
        
        await self.create_companies()
        await self.create_users()
        await self.create_projects()
        await self.create_work_types()
        await self.create_work_entries()
        
        print("✅ Генерация тестовых данных завершена!")
        await self.print_summary()

    async def create_companies(self):
        """Создание тестовых компаний"""
        print("📊 Создание компаний...")
        
        companies_data = [
            {
                'name': 'ТестСтрой ОУ',
                'business_id': '12345678',
                'address': 'Хельсинки, Финляндия'
            },
            {
                'name': 'Северная Стройка',
                'business_id': '87654321',
                'address': 'Тампере, Финляндия'
            },
            {
                'name': 'Финская Отделка',
                'business_id': '11223344',
                'address': 'Турку, Финляндия'
            }
        ]
        
        async with async_session() as session:
            for company_data in companies_data:
                # Проверяем, существует ли компания
                existing = await session.execute(
                    select(Company).where(Company.name == company_data['name'])
                )
                if existing.scalar_one_or_none():
                    print(f"  ⚠️ Компания '{company_data['name']}' уже существует")
                    continue
                
                company = Company(**company_data)
                session.add(company)
                await session.flush()
                self.companies.append(company)
                print(f"  ✅ Создана компания: {company.name}")
            
            await session.commit()

    async def create_users(self):
        """Создание тестовых пользователей"""
        print("👥 Создание пользователей...")
        
        users_data = [
            {
                'user_id': *********,
                'display_name': 'Иван Иванов',
                'role': 'director'
            },
            {
                'user_id': *********,
                'display_name': 'Петр Петров',
                'role': 'worker'
            },
            {
                'user_id': *********,
                'display_name': 'Анна Сидорова',
                'role': 'worker'
            },
            {
                'user_id': *********,
                'display_name': 'Михаил Козлов',
                'role': 'worker'
            },
            {
                'user_id': *********,
                'display_name': 'Елена Морозова',
                'role': 'director'
            }
        ]

        async with async_session() as session:
            for user_data in users_data:
                # Проверяем, существует ли пользователь
                existing = await session.execute(
                    select(User).where(User.user_id == user_data['user_id'])
                )
                if existing.scalar_one_or_none():
                    print(f"  ⚠️ Пользователь {user_data['display_name']} уже существует")
                    continue

                user = User(
                    user_id=user_data['user_id'],
                    display_name=user_data['display_name']
                )
                session.add(user)
                await session.flush()
                
                # Назначаем роли в компаниях
                for i, company in enumerate(self.companies):
                    if i < 2 or user_data['role'] == 'director':  # Директора во всех компаниях
                        role = UserCompanyRole(
                            user_id=user.user_id,
                            company_id=company.id,
                            role=user_data['role']
                        )
                        session.add(role)
                
                self.users.append(user)
                print(f"  ✅ Создан пользователь: {user.display_name} ({user_data['role']})")
            
            await session.commit()

    async def create_projects(self):
        """Создание тестовых проектов"""
        print("🏗️ Создание проектов...")
        
        projects_data = [
            {
                'name': 'Жилой комплекс "Северный"',
                'address': 'ул. Маннергейма, 15, Хельсинки'
            },
            {
                'name': 'Офисный центр "Финляндия"',
                'address': 'пр. Рунеберга, 25, Хельсинки'
            },
            {
                'name': 'Торговый центр "Балтика"',
                'address': 'ул. Алексантеринкату, 10, Тампере'
            },
            {
                'name': 'Частный дом в Эспоо',
                'address': 'ул. Тапиола, 5, Эспоо'
            }
        ]
        
        async with async_session() as session:
            for company in self.companies:
                for project_data in projects_data:
                    # Проверяем, существует ли проект
                    existing = await session.execute(
                        select(Project).where(
                            Project.name == project_data['name'],
                            Project.company_id == company.id
                        )
                    )
                    if existing.scalar_one_or_none():
                        continue
                    
                    # Находим директора компании
                    director_role = await session.execute(
                        select(UserCompanyRole).where(
                            UserCompanyRole.company_id == company.id,
                            UserCompanyRole.role == 'director'
                        )
                    )
                    director = director_role.scalar_one_or_none()
                    
                    if director:
                        project = Project(
                            company_id=company.id,
                            created_by=director.user_id,
                            name=project_data['name'],
                            address=project_data['address']
                        )
                        session.add(project)
                        await session.flush()
                        self.projects.append(project)
                        print(f"  ✅ Создан проект: {project.name}")
            
            await session.commit()

    async def create_work_types(self):
        """Создание тестовых типов работ"""
        print("🛠️ Создание типов работ...")
        
        work_types_data = [
            {'name': 'Монтаж', 'unit': 'час', 'rate': 25.50},
            {'name': 'Демонтаж', 'unit': 'м²', 'rate': 20.00},
            {'name': 'Покраска', 'unit': 'м²', 'rate': 15.75},
            {'name': 'Электромонтаж', 'unit': 'точка', 'rate': 35.00},
            {'name': 'Сантехника', 'unit': 'час', 'rate': 30.00},
            {'name': 'Укладка плитки', 'unit': 'м²', 'rate': 28.50},
            {'name': 'Штукатурка', 'unit': 'м²', 'rate': 18.00},
            {'name': 'Установка окон', 'unit': 'шт', 'rate': 150.00}
        ]
        
        for company in self.companies:
            for work_type_data in work_types_data:
                # Находим директора компании
                async with async_session() as session:
                    director_role = await session.execute(
                        select(UserCompanyRole).where(
                            UserCompanyRole.company_id == company.id,
                            UserCompanyRole.role == 'director'
                        )
                    )
                    director = director_role.scalar_one_or_none()
                
                if director:
                    result = await WorkTypeService.create_work_type(
                        company_id=company.id,
                        name=work_type_data['name'],
                        unit=work_type_data['unit'],
                        rate=work_type_data['rate'],
                        created_by=director.user_id
                    )
                    if result:
                        self.work_types.append(result)
                        print(f"  ✅ Создан тип работы: {work_type_data['name']} ({company.name})")

    async def create_work_entries(self):
        """Создание тестовых записей работ"""
        print("📝 Создание записей работ...")
        
        # Получаем все типы работ из базы
        async with async_session() as session:
            work_types_result = await session.execute(select(WorkType))
            all_work_types = work_types_result.scalars().all()
        
        # Создаем записи за последние 30 дней
        start_date = date.today() - timedelta(days=30)
        
        entries_created = 0
        for company in self.companies:
            # Получаем рабочих компании
            async with async_session() as session:
                workers_result = await session.execute(
                    select(UserCompanyRole).where(
                        UserCompanyRole.company_id == company.id,
                        UserCompanyRole.role == 'worker'
                    )
                )
                workers = workers_result.scalars().all()
                
                # Получаем проекты компании
                projects_result = await session.execute(
                    select(Project).where(Project.company_id == company.id)
                )
                company_projects = projects_result.scalars().all()
                
                # Получаем типы работ компании
                work_types_result = await session.execute(
                    select(WorkType).where(WorkType.company_id == company.id)
                )
                company_work_types = work_types_result.scalars().all()
                
                # Создаем записи
                for day_offset in range(30):
                    work_date = start_date + timedelta(days=day_offset)
                    
                    # Пропускаем выходные (суббота=5, воскресенье=6)
                    if work_date.weekday() >= 5:
                        continue
                    
                    for worker in workers[:2]:  # Берем первых 2 рабочих
                        if company_projects and company_work_types:
                            # Создаем 1-2 записи в день для каждого рабочего
                            import random
                            entries_count = random.randint(1, 2)
                            
                            for _ in range(entries_count):
                                project = random.choice(company_projects)
                                work_type = random.choice(company_work_types)
                                hours = round(random.uniform(4.0, 8.0), 1)
                                
                                entry = WorkEntry(
                                    user_id=worker.user_id,
                                    company_id=company.id,
                                    project_id=project.project_id,
                                    work_type_id=work_type.id,
                                    work_date=work_date,
                                    hours=hours,
                                    hourly_rate=work_type.rate,
                                    total_amount=hours * work_type.rate,
                                    description=f"Работы по проекту {project.name}"
                                )
                                session.add(entry)
                                entries_created += 1
                
                await session.commit()
        
        print(f"  ✅ Создано записей работ: {entries_created}")

    async def print_summary(self):
        """Вывод сводки созданных данных"""
        print("\n📊 СВОДКА СОЗДАННЫХ ДАННЫХ:")
        
        async with async_session() as session:
            # Компании
            companies_count = await session.execute(select(Company))
            print(f"🏢 Компаний: {len(companies_count.scalars().all())}")
            
            # Пользователи
            users_count = await session.execute(select(User))
            print(f"👥 Пользователей: {len(users_count.scalars().all())}")
            
            # Проекты
            projects_count = await session.execute(select(Project))
            print(f"🏗️ Проектов: {len(projects_count.scalars().all())}")
            
            # Типы работ
            work_types_count = await session.execute(select(WorkType))
            print(f"🛠️ Типов работ: {len(work_types_count.scalars().all())}")
            
            # Записи работ
            work_entries_count = await session.execute(select(WorkEntry))
            print(f"📝 Записей работ: {len(work_entries_count.scalars().all())}")


async def main():
    """Главная функция"""
    print("🔧 Инициализация базы данных...")
    await init_db()
    
    generator = TestDataGenerator()
    await generator.generate_all_data()


if __name__ == "__main__":
    asyncio.run(main())
