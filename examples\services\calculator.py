"""
Сервис расчетов для Worklog Bot.

Функции:
- calculate_sum() - расчет суммы за работу
- calculate_rate() - расчет ставки
- validate_numeric_input() - валидация числовых значений

Типы ставок:
- 'fixed' - фиксированная ставка за всю работу
- 'per_unit' - ставка за единицу (количество * ставка)
"""
from typing import Union
from decimal import Decimal, ROUND_HALF_UP


def calculate_sum(work_type, quantity: Union[float, int]) -> float:
    """
    Рассчитывает общую сумму за выполненную работу.
    
    Args:
        work_type: Объект WorkType с полями rate_type и value
        quantity: Количество выполненной работы
        
    Returns:
        Рассчитанная сумма
        
    Raises:
        ValueError: При некорректных входных данных
    """
    if not work_type:
        raise ValueError("Тип работы не может быть None")
    
    if quantity <= 0:
        raise ValueError("Количество должно быть положительным числом")
    
    # Получаем тип ставки и значение
    rate_type = getattr(work_type, 'rate_type', 'per_unit')
    rate_value = float(getattr(work_type, 'value', 0))
    
    if rate_value < 0:
        raise ValueError("Ставка не может быть отрицательной")
    
    # Расчет в зависимости от типа ставки
    if rate_type == 'fixed':
        # Фиксированная ставка - не зависит от количества
        total_sum = rate_value
    elif rate_type == 'per_unit':
        # Ставка за единицу - умножаем на количество
        total_sum = rate_value * float(quantity)
    else:
        # По умолчанию используем ставку за единицу
        total_sum = rate_value * float(quantity)
    
    # Округляем до 2 знаков после запятой
    return round(total_sum, 2)


def calculate_rate(
    total_sum: Union[float, int], 
    quantity: Union[float, int],
    rate_type: str = 'per_unit'
) -> float:
    """
    Рассчитывает ставку на основе общей суммы и количества.
    
    Args:
        total_sum: Общая сумма
        quantity: Количество
        rate_type: Тип ставки ('fixed' или 'per_unit')
        
    Returns:
        Рассчитанная ставка
        
    Raises:
        ValueError: При некорректных входных данных
    """
    if total_sum < 0:
        raise ValueError("Общая сумма не может быть отрицательной")
    
    if quantity <= 0:
        raise ValueError("Количество должно быть положительным числом")
    
    if rate_type == 'fixed':
        # Для фиксированной ставки возвращаем общую сумму
        return round(float(total_sum), 2)
    elif rate_type == 'per_unit':
        # Для ставки за единицу делим сумму на количество
        rate = float(total_sum) / float(quantity)
        return round(rate, 2)
    else:
        raise ValueError(f"Неизвестный тип ставки: {rate_type}")


def validate_numeric_input(value: str, field_name: str = "значение") -> float:
    """
    Валидирует и преобразует строковое значение в число.
    
    Args:
        value: Строковое значение для валидации
        field_name: Название поля для сообщений об ошибках
        
    Returns:
        Преобразованное число
        
    Raises:
        ValueError: При некорректном формате числа
    """
    if not value or not value.strip():
        raise ValueError(f"{field_name} не может быть пустым")
    
    # Заменяем запятую на точку для поддержки русского формата
    clean_value = value.replace(",", ".").strip()
    
    try:
        number = float(clean_value)
    except ValueError:
        raise ValueError(f"{field_name} должно быть числом")
    
    if number < 0:
        raise ValueError(f"{field_name} не может быть отрицательным")
    
    return number


def validate_positive_number(value: Union[str, float, int], field_name: str = "значение") -> float:
    """
    Валидирует положительное число.
    
    Args:
        value: Значение для валидации
        field_name: Название поля для сообщений об ошибках
        
    Returns:
        Валидированное число
        
    Raises:
        ValueError: При некорректном значении
    """
    if isinstance(value, str):
        number = validate_numeric_input(value, field_name)
    else:
        number = float(value)
    
    if number <= 0:
        raise ValueError(f"{field_name} должно быть положительным числом")
    
    return number


def format_currency(amount: Union[float, int], currency: str = "руб.") -> str:
    """
    Форматирует сумму в валютном формате.
    
    Args:
        amount: Сумма для форматирования
        currency: Валюта (по умолчанию "руб.")
        
    Returns:
        Отформатированная строка
    """
    # Округляем до 2 знаков
    rounded_amount = round(float(amount), 2)
    
    # Форматируем с разделителями тысяч
    formatted = f"{rounded_amount:,.2f}".replace(",", " ")
    
    return f"{formatted} {currency}"


def format_quantity(quantity: Union[float, int], unit: str = "") -> str:
    """
    Форматирует количество с единицей измерения.
    
    Args:
        quantity: Количество для форматирования
        unit: Единица измерения
        
    Returns:
        Отформатированная строка
    """
    # Убираем лишние нули после запятой
    if float(quantity).is_integer():
        formatted = str(int(quantity))
    else:
        formatted = f"{quantity:.2f}".rstrip('0').rstrip('.')
    
    if unit:
        return f"{formatted} {unit}"
    return formatted


def calculate_work_statistics(entries: list) -> dict:
    """
    Рассчитывает статистику по записям о работе.
    
    Args:
        entries: Список записей о работе
        
    Returns:
        Словарь со статистикой
    """
    if not entries:
        return {
            "total_entries": 0,
            "total_sum": 0.0,
            "total_quantity": 0.0,
            "average_sum_per_entry": 0.0,
            "work_types_count": 0
        }
    
    total_sum = 0.0
    total_quantity = 0.0
    work_types = set()
    
    for entry in entries:
        # Получаем сумму
        entry_sum = getattr(entry, 'sum_total', 0) or 0
        total_sum += float(entry_sum)
        
        # Получаем количество
        entry_quantity = getattr(entry, 'quantity', 0) or 0
        total_quantity += float(entry_quantity)
        
        # Получаем тип работы
        work_type = getattr(entry, 'work_type', None)
        if work_type:
            work_type_name = getattr(work_type, 'name', '')
            if work_type_name:
                work_types.add(work_type_name)
    
    total_entries = len(entries)
    average_sum = total_sum / total_entries if total_entries > 0 else 0.0
    
    return {
        "total_entries": total_entries,
        "total_sum": round(total_sum, 2),
        "total_quantity": round(total_quantity, 2),
        "average_sum_per_entry": round(average_sum, 2),
        "work_types_count": len(work_types)
    }


def calculate_project_totals(work_types: list) -> dict:
    """
    Рассчитывает общие показатели проекта по типам работ.
    
    Args:
        work_types: Список типов работ с записями
        
    Returns:
        Словарь с общими показателями
    """
    total_work_types = len(work_types)
    total_entries = 0
    total_sum = 0.0
    
    for work_type in work_types:
        entries = getattr(work_type, 'work_entries', [])
        total_entries += len(entries)
        
        for entry in entries:
            entry_sum = getattr(entry, 'sum_total', 0) or 0
            total_sum += float(entry_sum)
    
    return {
        "total_work_types": total_work_types,
        "total_entries": total_entries,
        "total_sum": round(total_sum, 2),
        "average_sum_per_work_type": round(total_sum / total_work_types, 2) if total_work_types > 0 else 0.0
    }


# Константы для валидации
MIN_QUANTITY = 0.01
MAX_QUANTITY = 999999.99
MIN_RATE = 0.01
MAX_RATE = 999999.99

def validate_work_entry_data(quantity: float, rate: float) -> tuple[bool, str]:
    """
    Валидирует данные записи о работе.
    
    Args:
        quantity: Количество работы
        rate: Ставка
        
    Returns:
        Кортеж (валидно, сообщение об ошибке)
    """
    if quantity < MIN_QUANTITY:
        return False, f"Количество должно быть не менее {MIN_QUANTITY}"
    
    if quantity > MAX_QUANTITY:
        return False, f"Количество должно быть не более {MAX_QUANTITY}"
    
    if rate < MIN_RATE:
        return False, f"Ставка должна быть не менее {MIN_RATE}"
    
    if rate > MAX_RATE:
        return False, f"Ставка должна быть не более {MAX_RATE}"
    
    return True, ""


# Примеры использования:
"""
# Расчет суммы
work_type = WorkType(rate_type='per_unit', value=1000.0)
total_sum = calculate_sum(work_type, 8.5)  # 8500.0

# Расчет ставки
rate = calculate_rate(8500.0, 8.5, 'per_unit')  # 1000.0

# Валидация числового ввода
try:
    quantity = validate_numeric_input("8,5", "количество")  # 8.5
except ValueError as e:
    print(f"Ошибка: {e}")

# Форматирование валюты
formatted = format_currency(8500.0)  # "8 500.00 руб."

# Форматирование количества
formatted_qty = format_quantity(8.5, "час")  # "8.5 час"

# Статистика по записям
stats = calculate_work_statistics(entries_list)
print(f"Всего записей: {stats['total_entries']}")
print(f"Общая сумма: {stats['total_sum']}")

# Валидация данных записи
is_valid, error_msg = validate_work_entry_data(8.5, 1000.0)
if not is_valid:
    print(f"Ошибка валидации: {error_msg}")
"""
