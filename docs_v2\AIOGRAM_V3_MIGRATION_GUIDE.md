# 🔄 Руководство по миграции на aiogram v3.x

## 🎯 Обзор изменений

Этот документ описывает ключевые изменения при переходе с aiogram v2.x на v3.x для проекта Worklog Bot.

## 📋 Основные изменения

### 1. Импорты

**aiogram v2.x:**
```python
from aiogram import types, Dispatcher
from aiogram.dispatcher import FSMContext
from aiogram.dispatcher.filters.state import State, StatesGroup
```

**aiogram v3.x:**
```python
from aiogram import types, Router, F
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup
from aiogram.filters import Command
```

### 2. FSM состояния

**aiogram v2.x:**
```python
# Установка состояния
await AddWorkStates.waiting_for_date_choice.set()

# Завершение состояния
await state.finish()
```

**aiogram v3.x:**
```python
# Установка состояния
await state.set_state(AddWorkStates.waiting_for_date_choice)

# Завершение состояния
await state.clear()
```

### 3. Регистрация обработчиков

**aiogram v2.x:**
```python
def register_handlers(dp: Dispatcher):
    dp.register_message_handler(cmd_start, commands=["start"])
    dp.register_message_handler(process_text, state=SomeState.waiting)
```

**aiogram v3.x:**
```python
router = Router()

@router.message(Command("start"))
async def cmd_start(message: types.Message):
    pass

@router.message(SomeState.waiting)
async def process_text(message: types.Message, state: FSMContext):
    pass

def register_handlers(dp):
    dp.include_router(router)
```

### 4. Callback Query фильтры

**aiogram v2.x:**
```python
@dp.callback_query_handler(lambda c: c.data.startswith("action:"))
async def process_callback(callback: types.CallbackQuery):
    pass
```

**aiogram v3.x:**
```python
@router.callback_query(F.data.startswith("action:"))
async def process_callback(callback: types.CallbackQuery):
    pass
```

## 🔧 Практические примеры

### Пример 1: Команда /addwork

**aiogram v2.x:**
```python
from aiogram import types, Dispatcher
from aiogram.dispatcher import FSMContext
from aiogram.dispatcher.filters.state import State, StatesGroup

class AddWorkStates(StatesGroup):
    waiting_for_date = State()

async def cmd_add_work(message: types.Message, state: FSMContext):
    await AddWorkStates.waiting_for_date.set()
    await message.answer("Выберите дату")

async def process_date(message: types.Message, state: FSMContext):
    await state.finish()
    await message.answer("Дата сохранена")

def register_handlers(dp: Dispatcher):
    dp.register_message_handler(cmd_add_work, commands=["addwork"])
    dp.register_message_handler(process_date, state=AddWorkStates.waiting_for_date)
```

**aiogram v3.x:**
```python
from aiogram import types, Router
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup
from aiogram.filters import Command

router = Router()

class AddWorkStates(StatesGroup):
    waiting_for_date = State()

@router.message(Command("addwork"))
async def cmd_add_work(message: types.Message, state: FSMContext):
    await state.set_state(AddWorkStates.waiting_for_date)
    await message.answer("Выберите дату")

@router.message(AddWorkStates.waiting_for_date)
async def process_date(message: types.Message, state: FSMContext):
    await state.clear()
    await message.answer("Дата сохранена")

def register_handlers(dp):
    dp.include_router(router)
```

### Пример 2: Inline клавиатуры с callback

**aiogram v2.x:**
```python
@dp.callback_query_handler(lambda c: c.data == "confirm")
async def process_confirm(callback: types.CallbackQuery):
    await callback.answer("Подтверждено!")
    await callback.message.edit_text("Операция выполнена")
```

**aiogram v3.x:**
```python
@router.callback_query(F.data == "confirm")
async def process_confirm(callback: types.CallbackQuery):
    await callback.answer("Подтверждено!")
    await callback.message.edit_text("Операция выполнена")
```

## 📁 Обновленная структура файлов

### handlers/addwork.py
```python
from aiogram import Router
from aiogram.filters import Command
from aiogram.fsm.context import FSMContext

router = Router()

@router.message(Command("addwork"))
async def cmd_add_work(message: types.Message, state: FSMContext):
    # Логика команды
    pass

def register_handlers(dp):
    dp.include_router(router)
```

### main.py
```python
from aiogram import Bot, Dispatcher
from aiogram.fsm.storage.memory import MemoryStorage

from handlers import addwork, edit, delete

async def main():
    bot = Bot(token=TOKEN)
    storage = MemoryStorage()
    dp = Dispatcher(storage=storage)
    
    # Регистрируем роутеры
    addwork.register_handlers(dp)
    edit.register_handlers(dp)
    delete.register_handlers(dp)
    
    await dp.start_polling(bot)
```

## ⚠️ Важные замечания

1. **Middleware**: Система middleware изменилась, нужно адаптировать RBAC middleware
2. **Storage**: Интерфейс storage остался совместимым
3. **Фильтры**: Новая система фильтров более гибкая
4. **Роутеры**: Позволяют лучше организовать код

## 🔄 План миграции

1. Обновить импорты во всех файлах
2. Заменить `state.finish()` на `state.clear()`
3. Заменить `State.set()` на `state.set_state(State)`
4. Переписать регистрацию обработчиков на роутеры
5. Обновить callback query фильтры
6. Протестировать все команды

## 📚 Дополнительные ресурсы

- [Официальная документация aiogram v3.x](https://docs.aiogram.dev/en/latest/)
- [Руководство по миграции](https://docs.aiogram.dev/en/latest/migration_2_to_3.html)
