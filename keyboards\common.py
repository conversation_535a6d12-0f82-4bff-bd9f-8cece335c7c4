"""
Общие Reply-клавиатуры для Worklog Bot.

Включает:
- create_main_menu() - адаптивное главное меню
- create_back_keyboard() - кнопка "Назад"
- create_cancel_keyboard() - кнопка "Отменить"
- create_yes_no_keyboard() - подтверждение действий
"""
from aiogram.types import ReplyKeyboardMarkup, KeyboardButton, ReplyKeyboardRemove
from typing import List, Dict, Optional


def create_main_menu(user_permissions: Dict[str, bool] = None) -> ReplyKeyboardMarkup:
    """
    Создает адаптивное главное меню в зависимости от прав пользователя.
    
    Args:
        user_permissions: Словарь с правами пользователя
    
    Returns:
        Reply-клавиатура главного меню
    """
    if user_permissions is None:
        user_permissions = {}
    
    keyboard = []
    
    # Кнопки для работы с записями (если есть права)
    if user_permissions.get("can_add_work", False):
        keyboard.append([
            KeyboardButton(text="➕ Добавить работу"),
            KeyboardButton(text="📋 Мои записи")
        ])
        keyboard.append([
            KeyboardButton(text="✏️ Редактировать"),
            KeyboardButton(text="🗑️ Удалить")
        ])
    
    # Кнопки для отчетов
    if user_permissions.get("can_view_reports", False):
        keyboard.append([
            KeyboardButton(text="📊 Отчеты"),
            KeyboardButton(text="📤 Экспорт")
        ])
    
    # Кнопки для управления (директор)
    if user_permissions.get("can_manage_workers", False):
        keyboard.append([
            KeyboardButton(text="👷 Управление рабочими"),
            KeyboardButton(text="🏗️ Проекты")
        ])
    
    # Кнопки для админа
    if user_permissions.get("is_admin", False):
        keyboard.append([
            KeyboardButton(text="👑 Админ панель")
        ])
    
    # Общие кнопки
    keyboard.append([
        KeyboardButton(text="👤 Профиль"),
        KeyboardButton(text="ℹ️ Помощь")
    ])
    
    return ReplyKeyboardMarkup(
        keyboard=keyboard,
        resize_keyboard=True,
        input_field_placeholder="Выберите действие"
    )


def create_back_keyboard() -> ReplyKeyboardMarkup:
    """Создает клавиатуру с кнопкой 'Назад'"""
    return ReplyKeyboardMarkup(
        keyboard=[[KeyboardButton(text="🔙 Назад")]],
        resize_keyboard=True
    )


def create_cancel_keyboard() -> ReplyKeyboardMarkup:
    """Создает клавиатуру с кнопкой 'Отменить'"""
    return ReplyKeyboardMarkup(
        keyboard=[[KeyboardButton(text="❌ Отменить")]],
        resize_keyboard=True
    )


def create_yes_no_keyboard() -> ReplyKeyboardMarkup:
    """Создает клавиатуру подтверждения Да/Нет"""
    return ReplyKeyboardMarkup(
        keyboard=[
            [
                KeyboardButton(text="✅ Да"),
                KeyboardButton(text="❌ Нет")
            ]
        ],
        resize_keyboard=True
    )


def create_back_cancel_keyboard() -> ReplyKeyboardMarkup:
    """Создает клавиатуру с кнопками 'Назад' и 'Отменить'"""
    return ReplyKeyboardMarkup(
        keyboard=[
            [
                KeyboardButton(text="🔙 Назад"),
                KeyboardButton(text="❌ Отменить")
            ]
        ],
        resize_keyboard=True
    )


def remove_keyboard() -> ReplyKeyboardRemove:
    """Убирает клавиатуру"""
    return ReplyKeyboardRemove()


def create_navigation_keyboard(has_prev: bool = False, has_next: bool = False) -> ReplyKeyboardMarkup:
    """
    Создает клавиатуру навигации по спискам.
    
    Args:
        has_prev: Есть ли предыдущие элементы
        has_next: Есть ли следующие элементы
    """
    keyboard = []
    
    # Кнопки навигации
    nav_buttons = []
    if has_prev:
        nav_buttons.append(KeyboardButton(text="⬅️ Предыдущие"))
    if has_next:
        nav_buttons.append(KeyboardButton(text="➡️ Следующие"))
    
    if nav_buttons:
        keyboard.append(nav_buttons)
    
    # Дополнительные кнопки
    keyboard.append([
        KeyboardButton(text="🔍 Поиск"),
        KeyboardButton(text="🔄 Обновить")
    ])
    
    keyboard.append([KeyboardButton(text="🔙 Назад")])
    
    return ReplyKeyboardMarkup(
        keyboard=keyboard,
        resize_keyboard=True
    )


def create_filter_keyboard() -> ReplyKeyboardMarkup:
    """Создает клавиатуру фильтров"""
    return ReplyKeyboardMarkup(
        keyboard=[
            [
                KeyboardButton(text="📅 Сегодня"),
                KeyboardButton(text="📅 Вчера")
            ],
            [
                KeyboardButton(text="📅 Эта неделя"),
                KeyboardButton(text="📅 Этот месяц")
            ],
            [KeyboardButton(text="📅 Выбрать период")],
            [KeyboardButton(text="🔙 Назад")]
        ],
        resize_keyboard=True
    )


def create_sort_keyboard() -> ReplyKeyboardMarkup:
    """Создает клавиатуру сортировки"""
    return ReplyKeyboardMarkup(
        keyboard=[
            [
                KeyboardButton(text="📅 По дате"),
                KeyboardButton(text="💰 По сумме")
            ],
            [
                KeyboardButton(text="🏗️ По проекту"),
                KeyboardButton(text="⏰ По времени")
            ],
            [KeyboardButton(text="🔙 Назад")]
        ],
        resize_keyboard=True
    )
