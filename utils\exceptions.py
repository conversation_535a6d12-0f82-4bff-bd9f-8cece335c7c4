"""
Пользовательские исключения для Worklog Bot.

Определяет специфичные исключения для различных ошибок в приложении.
"""


class WorklogError(Exception):
    """Базовое исключение для всех ошибок Worklog Bot."""
    pass


class ValidationError(WorklogError):
    """Исключение для ошибок валидации данных."""
    pass


class NotFoundError(WorklogError):
    """Исключение для случаев, когда запрашиваемый ресурс не найден."""
    pass


class PermissionError(WorklogError):
    """Исключение для ошибок прав доступа."""
    pass


class AuthenticationError(WorklogError):
    """Исключение для ошибок аутентификации."""
    pass


class DatabaseError(WorklogError):
    """Исключение для ошибок базы данных."""
    pass


class ExportError(WorklogError):
    """Исключение для ошибок экспорта данных."""
    pass


class ImportError(WorklogError):
    """Исключение для ошибок импорта данных."""
    pass


class ConfigurationError(WorklogError):
    """Исключение для ошибок конфигурации."""
    pass


class BusinessLogicError(WorklogError):
    """Исключение для ошибок бизнес-логики."""
    pass
