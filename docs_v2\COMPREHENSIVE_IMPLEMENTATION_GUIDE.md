# 🚀 ПОЛНОЕ РУКОВОДСТВО ПО РЕАЛИЗАЦИИ WORKLOG BOT v2.0 (ФИНЛЯНДИЯ)

## 📋 ОБЗОР ПРОЕКТА

### Цель проекта
Telegram-бот для учета рабочего времени строительных компаний в Финляндии с поддержкой:
- **Токен-системы регистрации** - безопасная регистрация через ссылки
- **Ролевой модели доступа (RBAC)** - администратор, директор, рабочий
- **Финляндской локализации** - евро, финские адреса, местные стандарты
- **Мультикомпанийности** - изоляция данных по компаниям
- **Полного цикла учета** - от добавления записей до экспорта отчетов

### Технологический стек
- **Python 3.11+**
- **aiogram v3.x** (обновлено с v2.x)
- **SQLAlchemy 2.0** с async поддержкой
- **PostgreSQL** для всех сред (разработка и продакшен)
- **Redis** для FSM storage
- **Docker** для деплоя

### ⚠️ Важно: Только PostgreSQL
На основе опыта MVP выявлено, что SQLite создает серьезные проблемы при масштабировании и миграция на PostgreSQL в процессе разработки крайне сложна. Поэтому с самого начала используется только PostgreSQL.

## 🎭 СИСТЕМА РОЛЕЙ И ДОСТУПА

### Роли пользователей
1. **👑 Администратор** - глобальное управление системой
2. **👨‍💼 Директор** - управление компанией и рабочими
3. **👷 Рабочий** - ведение учета рабочего времени

### Токен-система регистрации
- **Одноразовые токены** с истечением срока (24 часа)
- **Автоматическое назначение роли** при регистрации
- **Привязка к компании** через токен
- **Безопасность** - невозможность регистрации без токена

### Гибридная система для разработки
- **Админ может выбирать роль** через /start в режиме разработки
- **Полный доступ** к функциям выбранной роли
- **Переменная DEVELOPMENT_MODE** включает/выключает функцию
- **Решает проблему тестирования** всех ролей без создания токенов

## 🏗️ АРХИТЕКТУРА СИСТЕМЫ

### Структура проекта
```
worklog_bot/
├── bot/
│   ├── handlers/           # Обработчики команд
│   │   ├── admin/         # Команды администратора
│   │   ├── director/      # Команды директора
│   │   ├── worker/        # Команды рабочего
│   │   └── common/        # Общие команды
│   ├── middlewares/       # Middleware (RBAC, логирование)
│   ├── services/          # Бизнес-логика
│   ├── models/           # SQLAlchemy модели
│   ├── keyboards/        # Inline клавиатуры
│   └── utils/            # Утилиты
├── examples/             # Примеры кода (aiogram v2.x → v3.x)
├── docs_v2/             # Документация
└── tests/               # Тесты
```

### Ключевые компоненты
- **AuthService** - управление аутентификацией и авторизацией
- **TokenService** - генерация и валидация токенов
- **CompanyService** - управление компаниями
- **ProjectService** - управление проектами
- **WorkEntryService** - учет рабочего времени
- **ReportService** - генерация отчетов
- **ExportService** - экспорт в Excel/PDF

## 📊 МОДЕЛИ ДАННЫХ

### Основные таблицы
```sql
-- Пользователи
users (
    user_id: BigInteger PK,
    display_name: String(100),
    active_project_id: Integer FK,
    active_company_id: Integer FK,
    created_at: DateTime
)

-- Компании (финские форматы)
companies (
    id: Integer PK,
    name: String(200) UNIQUE,      -- "Rakennus Virtanen Oy"
    business_id: String(20),       -- Y-tunnus
    address: String(300),          -- "Mannerheimintie 15, 00100 Helsinki"
    is_deleted: Boolean DEFAULT FALSE
)

-- Роли пользователей в компаниях
user_company_roles (
    id: Integer PK,
    user_id: BigInteger FK,
    company_id: Integer FK,
    role: String,                  -- 'admin', 'director', 'worker'
    permissions: JSON
)

-- Проекты
projects (
    project_id: Integer PK,
    name: String(100),             -- "Asuntokohde Kallio"
    address: String(300),          -- "Kielotie 15, 02150 Espoo"
    company_id: Integer FK,
    is_deleted: Boolean DEFAULT FALSE
)

-- Типы работ (евро)
work_types (
    work_type_id: Integer PK,
    name: String(100),             -- "Кладочные работы"
    unit: String(20),              -- "час", "м²", "шт"
    rate_type: String(20),         -- "fixed", "per_unit"
    value: Numeric(10,2),          -- Ставка в евро
    project_id: Integer FK
)

-- Записи о работе (евро)
work_entries (
    entry_id: Integer PK,
    user_id: BigInteger FK,
    work_type_id: Integer FK,
    date: Date,
    description: String(500),
    quantity: Numeric(10,2),
    calculated_amount: Numeric(10,2),  -- Сумма в евро
    created_at: DateTime
)

-- Токены регистрации
tokens (
    id: Integer PK,
    token: String(64) UNIQUE,
    role: String,                  -- 'director', 'worker'
    company_id: Integer FK,
    created_by_user_id: BigInteger FK,
    expires_at: DateTime,
    used: Boolean DEFAULT FALSE
)
```

## 🎯 ОСНОВНЫЕ КОМАНДЫ ПО РОЛЯМ

### 👑 Администратор
```
📍 Вы — администратор
[➕ Добавить пользователя]
[📋 Список директоров и компаний]
[👷 Список рабочих]
[🗂 Удалённые компании]
[ℹ️ Инфо]
```

### 👨‍💼 Директор
```
🏢 Управление компаниями
👷 Управление рабочими
📊 Просмотр отчётов
📁 Экспорт / Импорт данных
🛠️ Редактировать типы работ
ℹ️ Инфо
```

### 👷 Рабочий
```
📝 Добавить работу
📋 Мои записи
📊 Мой отчёт
🏗️ Выбрать проект
➕ Новый проект
✏️ Редактировать проект
📤 Экспорт данных
ℹ️ Инфо
```

## 🔧 КЛЮЧЕВЫЕ FSM СЦЕНАРИИ

### 📝 Добавление работы (7 шагов)
1. `waiting_for_date_choice` - выбор даты
2. `waiting_for_date_input` - ввод даты
3. `waiting_for_work_type` - выбор типа работы
4. `waiting_for_description` - описание
5. `waiting_for_quantity` - количество
6. `confirming_entry` - подтверждение
7. **Сохранение** в БД

### ➕ Создание проекта (8 шагов)
1. `creating_project_name` - название
2. `creating_project_address` - адрес
3. `choosing_copy_source` - копирование типов работ
4. `adding_work_type_name` - название типа
5. `adding_work_type_unit` - единица измерения
6. `adding_work_type_rate_type` - тип ставки
7. `adding_work_type_value` - значение ставки
8. `confirming_add_another_work_type` - добавить еще

### 📤 Экспорт данных (6 шагов)
1. `choosing_format` - Excel/PDF
2. `choosing_project` - выбор проекта
3. `choosing_period` - период
4. `entering_date_range` - диапазон дат
5. `choosing_columns` - столбцы (Excel)
6. `confirming_export` - генерация

## 🌍 ФИНЛЯНДСКАЯ ЛОКАЛИЗАЦИЯ

### Особенности
- **Валюта**: € (евро) с форматом "1 234,56 €"
- **Даты**: ДД.ММ.ГГГГ (европейский стандарт)
- **Компании**: "Rakennus Virtanen Oy", "BuildMaster Helsinki Ab"
- **Адреса**: "Mannerheimintie 15, 00100 Helsinki"
- **Проекты**: "Asuntokohde Kallio", "Toimistoremontti Espoo"

### Примеры ставок (2024-2025)
- **Общестроительные работы**: 25-35 €/час
- **Электромонтаж**: 30-45 €/час
- **Сантехника**: 28-40 €/час
- **Малярные работы**: 22-32 €/час

## 🔄 МИГРАЦИЯ С AIOGRAM v2.x НА v3.x

### Ключевые изменения
```python
# v2.x → v3.x
from aiogram.dispatcher import FSMContext → from aiogram.fsm.context import FSMContext
from aiogram.dispatcher.filters.state import State → from aiogram.fsm.state import State
await state.finish() → await state.clear()
await State.set() → await state.set_state(State)

# Регистрация обработчиков
# v2.x
dp.register_message_handler(handler, commands=["start"])

# v3.x
router = Router()
@router.message(Command("start"))
async def handler(...):
    pass
```

## 💻 ПРАКТИЧЕСКИЕ ПРИМЕРЫ РЕАЛИЗАЦИИ

### Пример 1: Обработчик добавления работы (aiogram v3.x)
```python
from aiogram import Router, types, F
from aiogram.fsm.context import FSMContext
from aiogram.filters import Command
from states import AddWorkStates
from services import WorkEntryService, AuthService

router = Router()

@router.message(Command("addwork"))
@require_permission("can_add_work")
async def cmd_add_work(message: types.Message, state: FSMContext):
    """Начало сценария добавления работы"""
    user_id = message.from_user.id
    await state.clear()

    # Проверка активного проекта
    user = await UserDAO.get_by_id(user_id)
    if not user or not user.active_project_id:
        await message.answer(
            "❌ У вас нет активного проекта. Выберите проект через меню.",
            reply_markup=create_main_menu()
        )
        return

    # Клавиатура выбора даты
    keyboard = InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="📅 Сегодня", callback_data="date_today")],
        [InlineKeyboardButton(text="📅 Ввести дату", callback_data="date_custom")],
        [InlineKeyboardButton(text="🔙 Отмена", callback_data="cancel")]
    ])

    await message.answer("Выберите дату работы:", reply_markup=keyboard)
    await state.set_state(AddWorkStates.waiting_for_date_choice)

@router.callback_query(AddWorkStates.waiting_for_date_choice)
async def process_date_choice(callback: types.CallbackQuery, state: FSMContext):
    """Обработка выбора даты"""
    if callback.data == "date_today":
        # Устанавливаем сегодняшнюю дату
        today = datetime.now().date()
        await state.update_data(work_date=today)
        await show_work_types(callback, state)
    elif callback.data == "date_custom":
        await callback.message.edit_text(
            "Введите дату в формате ДД.ММ.ГГГГ (например: 28.06.2024):"
        )
        await state.set_state(AddWorkStates.waiting_for_date_input)
    else:  # cancel
        await callback.message.edit_text("❌ Операция отменена")
        await state.clear()
```

### Пример 2: Токен-система регистрации
```python
class TokenService:
    @staticmethod
    async def generate_registration_token(
        role: str,
        company_id: int,
        created_by_user_id: int
    ) -> str:
        """Генерация токена регистрации"""
        token = secrets.token_urlsafe(32)
        expires_at = datetime.now() + timedelta(hours=24)

        async with async_session() as session:
            token_obj = Token(
                token=token,
                role=role,
                company_id=company_id,
                created_by_user_id=created_by_user_id,
                expires_at=expires_at
            )
            session.add(token_obj)
            await session.commit()

        return token
```

## 🔧 НАСТРОЙКА ОКРУЖЕНИЯ

### requirements.txt
```
aiogram==3.4.1
sqlalchemy==2.0.25
asyncpg==0.29.0
redis==5.0.1
python-dotenv==1.0.0
openpyxl==3.1.2
reportlab==4.0.8
Pillow==10.1.0
```

### .env файл
```
# Telegram Bot
BOT_TOKEN=your_bot_token_here
ADMIN_ID=*********

# Database (только PostgreSQL)
DATABASE_URL=postgresql+asyncpg://user:password@localhost/worklog_db
# Для Docker: DATABASE_URL=postgresql+asyncpg://postgres:password@db:5432/worklog

# Redis (для FSM)
REDIS_URL=redis://localhost:6379/0

# Режим разработки (гибридная система ролей)
DEVELOPMENT_MODE=true

# Финляндская локализация
DEFAULT_CURRENCY=EUR
DEFAULT_TIMEZONE=Europe/Helsinki
```

## 📚 ССЫЛКИ НА ДОКУМЕНТАЦИЮ

### Основные документы
1. **role_admin.md** - Финальная спецификация роли администратора (НЕ РЕДАКТИРОВАТЬ)
2. **role_director.md** - Финальная спецификация роли директора (НЕ РЕДАКТИРОВАТЬ)
3. **role_worker.md** - Финальная спецификация роли рабочего (НЕ РЕДАКТИРОВАТЬ)
4. **TECHNICAL_SPECIFICATION.md** - Техническая спецификация (обновлена)
5. **role_commands.md** - Команды по ролям (обновлен)
6. **AIOGRAM_V3_MIGRATION_GUIDE.md** - Руководство по миграции
7. **FINLAND_LOCALIZATION_EXAMPLES.md** - Примеры финляндской локализации

### Система для ИИ-агентов
8. **agent_prompt.md** - Стартовый документ с инструкциями для ИИ-агентов
9. **context_memory.md** - Система сохранения контекста последней работы
10. **CHANGELOG.md** - Система ведения изменений
11. **checkpoint_system.md** - Система отслеживания прогресса разработки
12. **agent_checklist.md** - Чек-лист для добавления новых команд и функций

### Примеры кода
- **examples/handlers/** - Обработчики команд (адаптированы под v3.x)
- **examples/states.py** - FSM состояния (обновлены)
- **examples/IMPLEMENTATION_GUIDE.md** - Руководство по реализации

## ✅ ЧЕКЛИСТ РЕАЛИЗАЦИИ

### Этап 1: Базовая настройка
- [ ] Настройка окружения (Python 3.11+, PostgreSQL, Redis)
- [ ] Установка зависимостей (aiogram v3.x, SQLAlchemy 2.0)
- [ ] Создание структуры проекта
- [ ] Настройка Docker контейнеров
- [ ] Настройка переменной DEVELOPMENT_MODE

### Этап 2: Модели данных
- [ ] Создание SQLAlchemy моделей
- [ ] Настройка миграций Alembic
- [ ] Создание DAO классов
- [ ] Тестирование подключения к БД

### Этап 3: Аутентификация и авторизация
- [ ] Реализация TokenService
- [ ] Создание AuthService
- [ ] Настройка RBAC middleware
- [ ] Реализация гибридной системы ролей
- [ ] Тестирование токен-системы

### Этап 4: Основные команды
- [ ] Команды администратора
- [ ] Команды директора
- [ ] Команды рабочего
- [ ] FSM сценарии
- [ ] Тестирование с гибридной системой

### Этап 5: Дополнительные функции
- [ ] Система отчетов
- [ ] Экспорт в Excel/PDF
- [ ] Финляндская локализация
- [ ] Обработка ошибок

### Этап 6: Тестирование и деплой
- [ ] Unit тесты
- [ ] Интеграционные тесты
- [ ] Нагрузочное тестирование
- [ ] Деплой в продакшен

## 🤖 РАБОТА С ИИ-АГЕНТАМИ

### Перед началом работы
1. **Прочитать `agent_prompt.md`** - понять правила работы с проектом
2. **Изучить `context_memory.md`** - понять текущее состояние
3. **Проверить `CHANGELOG.md`** - понять последние изменения
4. **Использовать `agent_checklist.md`** - при добавлении функций

### Система checkpoint'ов
- **Отслеживание прогресса** через `checkpoint_system.md`
- **9 основных этапов** от документации до деплоя
- **Четкие критерии готовности** для каждого этапа
- **Координация между агентами**

### Документирование работы
- **Обновлять `CHANGELOG.md`** при каждом изменении
- **Обновлять `context_memory.md`** при завершении работы
- **Следовать чек-листам** для обеспечения качества

---

*Это руководство содержит всю необходимую информацию для полной реализации Worklog Bot v2.0 с финляндской локализацией.*
