"""
Тесты для RBAC middleware и декораторов прав доступа
"""
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from aiogram.types import Message, CallbackQuery, User, Chat

from middleware.rbac_middleware import RBACMiddleware, require_permission
from services.auth_service import AuthService


class TestRBACMiddleware:
    """Тесты для RBACMiddleware"""
    
    @pytest.fixture
    def middleware(self):
        """Создание экземпляра middleware"""
        return RBACMiddleware()
    
    @pytest.fixture
    def mock_message(self):
        """Мок сообщения"""
        message = MagicMock()
        # Настраиваем isinstance для Message
        message.__class__ = Message
        message.from_user.id = *********
        return message

    @pytest.fixture
    def mock_callback(self):
        """Мок callback query"""
        callback = MagicMock()
        # Настраиваем isinstance для CallbackQuery
        callback.__class__ = CallbackQuery
        callback.from_user.id = *********
        return callback
    
    @pytest.mark.asyncio
    async def test_middleware_adds_user_data_for_message(self, middleware, mock_message):
        """Тест добавления данных пользователя для сообщения"""
        # Мокаем AuthService
        mock_user_data = {
            'user_id': *********,
            'display_name': 'Test User',
            'active_company_id': 1
        }
        mock_permissions = {
            'can_manage_workers': True,
            'can_generate_worker_tokens': True
        }

        with patch.object(AuthService, 'get_user', return_value=mock_user_data), \
             patch.object(AuthService, 'get_user_role', return_value='director'), \
             patch.object(AuthService, 'get_user_permissions', return_value=mock_permissions):

            # Мокаем handler
            handler = AsyncMock()
            data = {}

            # Вызываем middleware
            await middleware(handler, mock_message, data)

            # Отладочная информация
            print(f"Data after middleware: {data}")
            print(f"Message type: {type(mock_message)}")
            print(f"Message isinstance Message: {isinstance(mock_message, Message)}")

            # Проверяем, что данные добавлены
            assert data['user_id'] == *********
            assert data['user_data'] == mock_user_data
            assert data['user_role'] == 'director'
            assert data['user_permissions'] == mock_permissions
            assert handler.called
    
    @pytest.mark.asyncio
    async def test_middleware_adds_user_data_for_callback(self, middleware, mock_callback):
        """Тест добавления данных пользователя для callback query"""
        # Мокаем AuthService
        mock_user_data = {
            'user_id': *********,
            'display_name': 'Test User',
            'active_company_id': 1
        }
        mock_permissions = {
            'can_generate_tokens': True,
            'can_generate_worker_tokens': True
        }

        with patch.object(AuthService, 'get_user', return_value=mock_user_data), \
             patch.object(AuthService, 'get_user_role', return_value='admin'), \
             patch.object(AuthService, 'get_user_permissions', return_value=mock_permissions):

            # Мокаем handler
            handler = AsyncMock()
            data = {}

            # Вызываем middleware
            await middleware(handler, mock_callback, data)

            # Проверяем, что данные добавлены
            assert data['user_id'] == *********
            assert data['user_data'] == mock_user_data
            assert data['user_role'] == 'admin'
            assert data['user_permissions'] == mock_permissions
            assert handler.called
    
    @pytest.mark.asyncio
    async def test_middleware_handles_unregistered_user(self, middleware, mock_message):
        """Тест обработки незарегистрированного пользователя"""
        # Мокаем AuthService для незарегистрированного пользователя
        with patch.object(AuthService, 'get_user', return_value=None):

            # Мокаем handler
            handler = AsyncMock()
            data = {}

            # Вызываем middleware
            await middleware(handler, mock_message, data)

            # Проверяем, что данные пустые
            assert data['user_data'] is None
            assert data['user_role'] is None
            assert data['user_permissions'] == {}
            assert handler.called


class TestRequirePermissionDecorator:
    """Тесты для декоратора @require_permission"""
    
    @pytest.fixture
    def mock_callback(self):
        """Мок callback query с методом answer"""
        callback = MagicMock()
        callback.answer = AsyncMock()
        return callback
    
    @pytest.mark.asyncio
    async def test_permission_granted(self, mock_callback):
        """Тест успешной проверки права"""
        # Создаем декорированную функцию
        @require_permission("can_generate_worker_tokens")
        async def test_handler(event, **kwargs):
            return "success"
        
        # Вызываем с правильными правами
        result = await test_handler(
            mock_callback,
            user_permissions={'can_generate_worker_tokens': True}
        )
        
        assert result == "success"
        assert not mock_callback.answer.called
    
    @pytest.mark.asyncio
    async def test_permission_denied(self, mock_callback):
        """Тест отказа в доступе"""
        # Создаем декорированную функцию
        @require_permission("can_generate_worker_tokens")
        async def test_handler(event, **kwargs):
            return "success"
        
        # Вызываем без прав
        result = await test_handler(
            mock_callback,
            user_permissions={'can_manage_workers': True}  # Нет нужного права
        )
        
        assert result is None
        assert mock_callback.answer.called
        mock_callback.answer.assert_called_with(
            "❌ У вас недостаточно прав для выполнения этого действия."
        )
    
    @pytest.mark.asyncio
    async def test_permission_denied_empty_permissions(self, mock_callback):
        """Тест отказа в доступе при пустых правах"""
        # Создаем декорированную функцию
        @require_permission("can_generate_worker_tokens")
        async def test_handler(event, **kwargs):
            return "success"
        
        # Вызываем с пустыми правами
        result = await test_handler(
            mock_callback,
            user_permissions={}
        )
        
        assert result is None
        assert mock_callback.answer.called


class TestPermissionLogic:
    """Тесты логики прав доступа"""
    
    def test_admin_has_all_permissions(self):
        """Тест что админ имеет все необходимые права"""
        admin_permissions = AuthService.ROLE_PERMISSIONS["admin"]
        
        # Админ должен иметь права на создание токенов директоров и рабочих
        assert admin_permissions["can_generate_tokens"] is True
        assert admin_permissions["can_generate_worker_tokens"] is True
    
    def test_director_has_worker_permissions_only(self):
        """Тест что директор имеет права только на создание токенов рабочих"""
        director_permissions = AuthService.ROLE_PERMISSIONS["director"]
        
        # Директор должен иметь права только на создание токенов рабочих
        assert director_permissions["can_generate_worker_tokens"] is True
        assert "can_generate_tokens" not in director_permissions
    
    def test_worker_has_no_token_permissions(self):
        """Тест что рабочий не имеет прав на создание токенов"""
        worker_permissions = AuthService.ROLE_PERMISSIONS["worker"]
        
        # Рабочий не должен иметь прав на создание токенов
        assert "can_generate_tokens" not in worker_permissions
        assert "can_generate_worker_tokens" not in worker_permissions


if __name__ == "__main__":
    pytest.main([__file__])
