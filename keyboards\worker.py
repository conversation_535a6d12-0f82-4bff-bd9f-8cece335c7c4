"""
Reply-клавиатуры для рабочего.

Включает:
- create_worker_menu() - главное меню рабочего
- create_work_entries_keyboard() - управление записями работ
- create_projects_keyboard() - проекты
- create_reports_keyboard() - отчеты
"""
from aiogram.types import ReplyKeyboardMarkup, KeyboardButton
from typing import List, Dict


def create_worker_menu() -> ReplyKeyboardMarkup:
    """Создает главное меню рабочего согласно role_worker.md"""
    return ReplyKeyboardMarkup(
        keyboard=[
            [KeyboardButton(text="📝 Добавить работу")],
            [KeyboardButton(text="📋 Мои записи")],
            [KeyboardButton(text="📊 Мой отчёт")],
            [KeyboardButton(text="🏗️ Выбрать проект")],
            [KeyboardButton(text="➕ Новый проект")],
            [KeyboardButton(text="✏️ Редактировать проект")],
            [KeyboardButton(text="📤 Экспорт данных")],
            [KeyboardButton(text="ℹ️ Инфо")]
        ],
        resize_keyboard=True
    )


def create_add_work_keyboard() -> ReplyKeyboardMarkup:
    """Создает клавиатуру для добавления работы"""
    return ReplyKeyboardMarkup(
        keyboard=[
            [
                KeyboardButton(text="📅 Сегодня"),
                KeyboardButton(text="📅 Вчера")
            ],
            [
                KeyboardButton(text="📅 Выбрать дату")
            ],
            [
                KeyboardButton(text="❌ Отменить")
            ]
        ],
        resize_keyboard=True
    )


def create_work_entries_keyboard() -> ReplyKeyboardMarkup:
    """Создает клавиатуру управления записями работ"""
    return ReplyKeyboardMarkup(
        keyboard=[
            [
                KeyboardButton(text="📋 Все записи"),
                KeyboardButton(text="📅 За период")
            ],
            [
                KeyboardButton(text="🔍 Поиск"),
                KeyboardButton(text="📊 Статистика")
            ],
            [
                KeyboardButton(text="📤 Экспорт"),
                KeyboardButton(text="⚙️ Настройки")
            ],
            [
                KeyboardButton(text="🔙 Назад")
            ]
        ],
        resize_keyboard=True
    )


def create_work_entry_actions_keyboard() -> ReplyKeyboardMarkup:
    """Создает клавиатуру действий с записью работы"""
    return ReplyKeyboardMarkup(
        keyboard=[
            [
                KeyboardButton(text="👁️ Просмотр"),
                KeyboardButton(text="✏️ Редактировать")
            ],
            [
                KeyboardButton(text="📋 Копировать"),
                KeyboardButton(text="🗑️ Удалить")
            ],
            [
                KeyboardButton(text="🔙 К списку")
            ]
        ],
        resize_keyboard=True
    )


def create_edit_work_keyboard() -> ReplyKeyboardMarkup:
    """Создает клавиатуру редактирования работы"""
    return ReplyKeyboardMarkup(
        keyboard=[
            [
                KeyboardButton(text="📅 Изменить дату"),
                KeyboardButton(text="🏗️ Изменить проект")
            ],
            [
                KeyboardButton(text="⚙️ Изменить тип"),
                KeyboardButton(text="📝 Изменить описание")
            ],
            [
                KeyboardButton(text="🔢 Изменить количество"),
                KeyboardButton(text="💰 Изменить сумму")
            ],
            [
                KeyboardButton(text="💾 Сохранить"),
                KeyboardButton(text="❌ Отменить")
            ]
        ],
        resize_keyboard=True
    )


def create_projects_keyboard() -> ReplyKeyboardMarkup:
    """Создает клавиатуру проектов"""
    return ReplyKeyboardMarkup(
        keyboard=[
            [
                KeyboardButton(text="📋 Мои проекты"),
                KeyboardButton(text="🔄 Переключить")
            ],
            [
                KeyboardButton(text="📊 Статистика"),
                KeyboardButton(text="📤 Экспорт")
            ],
            [
                KeyboardButton(text="🔙 Назад")
            ]
        ],
        resize_keyboard=True
    )



def create_reports_keyboard() -> ReplyKeyboardMarkup:
    """Создает клавиатуру отчетов"""
    return ReplyKeyboardMarkup(
        keyboard=[
            [
                KeyboardButton(text="📊 Мой отчет"),
                KeyboardButton(text="📅 За период")
            ],
            [
                KeyboardButton(text="🏗️ По проекту"),
                KeyboardButton(text="⚙️ По типу работ")
            ],
            [
                KeyboardButton(text="📤 Экспорт"),
                KeyboardButton(text="📋 Сводка")
            ],
            [
                KeyboardButton(text="🔙 Назад")
            ]
        ],
        resize_keyboard=True
    )


def create_export_keyboard() -> ReplyKeyboardMarkup:
    """Создает клавиатуру экспорта"""
    return ReplyKeyboardMarkup(
        keyboard=[
            [
                KeyboardButton(text="📄 Excel"),
                KeyboardButton(text="📋 PDF")
            ],
            [
                KeyboardButton(text="📊 CSV"),
                KeyboardButton(text="📝 Текст")
            ],
            [
                KeyboardButton(text="📧 Отправить на email")
            ],
            [
                KeyboardButton(text="🔙 Назад")
            ]
        ],
        resize_keyboard=True
    )


def create_work_types_keyboard(work_types: List[Dict]) -> ReplyKeyboardMarkup:
    """
    Создает клавиатуру выбора типа работы.
    
    Args:
        work_types: Список типов работ с полями name и id
    """
    keyboard = []
    
    # Добавляем типы работ по 2 в ряд
    for i in range(0, len(work_types), 2):
        row = []
        row.append(KeyboardButton(text=f"⚙️ {work_types[i]['name']}"))
        if i + 1 < len(work_types):
            row.append(KeyboardButton(text=f"⚙️ {work_types[i + 1]['name']}"))
        keyboard.append(row)
    
    keyboard.append([KeyboardButton(text="🔙 Назад")])
    
    return ReplyKeyboardMarkup(
        keyboard=keyboard,
        resize_keyboard=True
    )


def create_quantity_keyboard() -> ReplyKeyboardMarkup:
    """Создает клавиатуру для ввода количества"""
    return ReplyKeyboardMarkup(
        keyboard=[
            [
                KeyboardButton(text="1"),
                KeyboardButton(text="2"),
                KeyboardButton(text="3")
            ],
            [
                KeyboardButton(text="4"),
                KeyboardButton(text="5"),
                KeyboardButton(text="6")
            ],
            [
                KeyboardButton(text="7"),
                KeyboardButton(text="8"),
                KeyboardButton(text="9")
            ],
            [
                KeyboardButton(text="0"),
                KeyboardButton(text="✏️ Ввести вручную")
            ],
            [
                KeyboardButton(text="❌ Отменить")
            ]
        ],
        resize_keyboard=True
    )


# Дополнительные клавиатуры для рабочего (адаптировано из examples)

def create_date_choice_keyboard() -> ReplyKeyboardMarkup:
    """Клавиатура выбора даты (из examples)"""
    return ReplyKeyboardMarkup(
        keyboard=[
            [KeyboardButton(text="Сегодня")],
            [KeyboardButton(text="Ввести дату")],
            [KeyboardButton(text="🔙 Отмена")]
        ],
        resize_keyboard=True,
        one_time_keyboard=True
    )


def create_cancel_keyboard() -> ReplyKeyboardMarkup:
    """Клавиатура только с кнопкой отмены"""
    return ReplyKeyboardMarkup(
        keyboard=[
            [KeyboardButton(text="🔙 Отмена")]
        ],
        resize_keyboard=True
    )


def create_export_format_keyboard() -> ReplyKeyboardMarkup:
    """Клавиатура выбора формата экспорта"""
    return ReplyKeyboardMarkup(
        keyboard=[
            [KeyboardButton(text="Excel")],
            [KeyboardButton(text="PDF")],
            [KeyboardButton(text="🔙 Отмена")]
        ],
        resize_keyboard=True,
        one_time_keyboard=True
    )


def create_export_period_keyboard() -> ReplyKeyboardMarkup:
    """Клавиатура выбора периода для экспорта"""
    return ReplyKeyboardMarkup(
        keyboard=[
            [KeyboardButton(text="За всё время")],
            [KeyboardButton(text="За последнюю неделю")],
            [KeyboardButton(text="За последний месяц")],
            [KeyboardButton(text="За период")],
            [KeyboardButton(text="🔙 Отмена")]
        ],
        resize_keyboard=True,
        one_time_keyboard=True
    )


def create_list_filter_keyboard() -> ReplyKeyboardMarkup:
    """Клавиатура фильтров для списка записей"""
    return ReplyKeyboardMarkup(
        keyboard=[
            [KeyboardButton(text="Все записи")],
            [KeyboardButton(text="За период")],
            [KeyboardButton(text="🔙 Назад")]
        ],
        resize_keyboard=True,
        one_time_keyboard=True
    )


def create_rate_type_keyboard() -> ReplyKeyboardMarkup:
    """Клавиатура выбора типа ставки"""
    return ReplyKeyboardMarkup(
        keyboard=[
            [KeyboardButton(text="Фиксированная ставка")],
            [KeyboardButton(text="Ставка за единицу")],
            [KeyboardButton(text="🔙 Отмена")]
        ],
        resize_keyboard=True,
        one_time_keyboard=True
    )


def create_unit_keyboard() -> ReplyKeyboardMarkup:
    """Клавиатура с популярными единицами измерения"""
    return ReplyKeyboardMarkup(
        keyboard=[
            [KeyboardButton(text="час"), KeyboardButton(text="м²")],
            [KeyboardButton(text="шт"), KeyboardButton(text="м")],
            [KeyboardButton(text="кг"), KeyboardButton(text="л")],
            [KeyboardButton(text="🔙 Отмена")]
        ],
        resize_keyboard=True
    )


def create_copy_source_keyboard() -> ReplyKeyboardMarkup:
    """Клавиатура выбора источника копирования типов работ"""
    return ReplyKeyboardMarkup(
        keyboard=[
            [KeyboardButton(text="Скопировать из существующего")],
            [KeyboardButton(text="Создать новые")],
            [KeyboardButton(text="🔙 Отмена")]
        ],
        resize_keyboard=True,
        one_time_keyboard=True
    )


def create_add_another_keyboard() -> ReplyKeyboardMarkup:
    """Клавиатура для добавления еще одного элемента"""
    return ReplyKeyboardMarkup(
        keyboard=[
            [KeyboardButton(text="✅ Да")],
            [KeyboardButton(text="❌ Нет, создать проект")],
            [KeyboardButton(text="🔙 Отмена")]
        ],
        resize_keyboard=True,
        one_time_keyboard=True
    )


def create_project_selection_keyboard(projects: list, active_project_id: int = None) -> ReplyKeyboardMarkup:
    """Клавиатура выбора проекта"""
    keyboard_buttons = []

    for project in projects:
        active_mark = "✅ " if project.project_id == active_project_id else ""
        keyboard_buttons.append([
            KeyboardButton(text=f"{active_mark}{project.name}")
        ])

    keyboard_buttons.append([KeyboardButton(text="🔙 Назад")])

    return ReplyKeyboardMarkup(
        keyboard=keyboard_buttons,
        resize_keyboard=True,
        one_time_keyboard=True
    )


def create_work_type_selection_keyboard(work_types: list) -> ReplyKeyboardMarkup:
    """Клавиатура выбора типа работы"""
    keyboard_buttons = []

    for work_type in work_types:
        keyboard_buttons.append([KeyboardButton(text=work_type.name)])

    keyboard_buttons.append([KeyboardButton(text="🔙 Отмена")])

    return ReplyKeyboardMarkup(
        keyboard=keyboard_buttons,
        resize_keyboard=True,
        one_time_keyboard=True
    )


def create_confirmation_keyboard() -> ReplyKeyboardMarkup:
    """Клавиатура подтверждения действия"""
    return ReplyKeyboardMarkup(
        keyboard=[
            [KeyboardButton(text="✅ Сохранить")],
            [KeyboardButton(text="🔙 Отмена")]
        ],
        resize_keyboard=True,
        one_time_keyboard=True
    )


def create_skip_keyboard() -> ReplyKeyboardMarkup:
    """Клавиатура с возможностью пропустить"""
    return ReplyKeyboardMarkup(
        keyboard=[
            [KeyboardButton(text="Пропустить")],
            [KeyboardButton(text="🔙 Отмена")]
        ],
        resize_keyboard=True,
        one_time_keyboard=True
    )


def create_project_copy_keyboard(projects: list) -> ReplyKeyboardMarkup:
    """Клавиатура выбора проекта для копирования типов работ"""
    keyboard_buttons = []

    for project in projects:
        keyboard_buttons.append([KeyboardButton(text=project.name)])

    keyboard_buttons.append([KeyboardButton(text="🔙 Отмена")])

    return ReplyKeyboardMarkup(
        keyboard=keyboard_buttons,
        resize_keyboard=True,
        one_time_keyboard=True
    )
