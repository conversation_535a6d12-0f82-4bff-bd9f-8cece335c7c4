# 📋 Техническая спецификация Worklog Telegram Bot v2.0 (Финляндия)

## 🎯 Обзор системы

Worklog Bot — это Telegram-бот для учета рабочего времени строительных компаний в Финляндии с поддержкой ролевой модели доступа (RBAC), мультикомпаний и токен-системы регистрации. Интерфейс на русском языке, адаптирован под финляндские реалии (евро, финские адреса, местные стандарты).

### 🏗️ Архитектура

```
bot.py (точка входа)
├── middleware/rbac_middleware.py (RBAC проверки)
├── handlers/ (обработчики команд)
│   ├── common.py (/start, /cancel)
│   ├── addwork.py (/addwork)
│   ├── edit.py (/edit)
│   ├── delete.py (/delete)
│   ├── list.py (/list)
│   ├── report.py (/report)
│   ├── export.py (/export)
│   ├── project/
│   │   ├── common.py (/setproject)
│   │   ├── new_project.py (/newproject)
│   │   └── edit_project.py (/editproject)
│   ├── admin.py (/admin)
│   ├── director.py (/manage)
│   └── registration/register.py (/register)
├── services/ (бизнес-логика)
├── db/ (модели данных и DAO)
├── keyboards/ (клавиатуры)
├── states.py (FSM состояния)
└── localization/texts.py (тексты)
```

### �️ База данных

**Основная БД:** PostgreSQL 15+ (все среды)
**ORM:** SQLAlchemy 2.0 с async поддержкой
**Миграции:** Alembic

**⚠️ Важно:** Используется только PostgreSQL с самого начала разработки. SQLite исключен из-за проблем масштабирования, выявленных в MVP.

### �🔐 Система ролей (RBAC) v2.0

**Роли:**
- **Admin** - глобальный администратор (управление всей системой)
- **Director** - директор компании (управление компанией и рабочими)
- **Worker** - рабочий (ведение учета рабочего времени)

**Система регистрации:**
- **Продакшен:** Токен-ссылки для регистрации (одноразовые, с истечением срока)
- **Разработка/Тестирование:** Выбор роли через /start для админа (ADMIN_ID)
- Привязка к компаниям через токены
- Автоматическое назначение ролей

**Гибридная система для тестирования:**
- Если пользователь = ADMIN_ID → показывается меню выбора роли
- Выбранная роль дает полный доступ ко всем функциям этой роли
- Позволяет админу тестировать все роли без создания токенов
- В продакшене отключается через переменную окружения

**Права доступа:**
- `is_admin` - глобальные права администратора
- `can_add_work` - добавление записей о работе
- `can_view_own_reports` - просмотр собственных отчетов
- `can_edit_own_entries` - редактирование собственных записей
- `can_export_own_data` - экспорт собственных данных
- `can_manage_workers` - управление рабочими компании
- `can_manage_projects` - управление проектами компании

## 📊 Модели данных (Финляндия)

### Основные таблицы

```sql
-- Пользователи
users (
    user_id: BigInteger PK,
    display_name: String(100),
    active_project_id: Integer FK,
    active_company_id: Integer FK,
    created_at: DateTime,
    updated_at: DateTime
)

-- Компании (финские форматы)
companies (
    id: Integer PK,
    name: String(200) UNIQUE,  -- "Rakennus Virtanen Oy"
    business_id: String(20),   -- Y-tunnus (финский бизнес-ID)
    address: String(300),      -- "Mannerheimintie 15, 00100 Helsinki"
    is_deleted: Boolean DEFAULT FALSE,
    created_at: DateTime,
    updated_at: DateTime
)

-- Роли пользователей в компаниях
user_company_roles (
    id: Integer PK,
    user_id: BigInteger FK,
    company_id: Integer FK,
    role: String,  -- 'admin', 'director', 'worker'
    permissions: JSON,  -- Детальные права доступа
    created_at: DateTime
)

-- Проекты (финские адреса)
projects (
    project_id: Integer PK,
    name: String(100),         -- "Asuntokohde Kallio"
    address: String(300),      -- "Kielotie 15, 02150 Espoo"
    company_id: Integer FK,
    is_deleted: Boolean DEFAULT FALSE,
    created_at: DateTime,
    updated_at: DateTime
)

-- Типы работ (евро)
work_types (
    work_type_id: Integer PK,
    name: String(100),         -- "Кладочные работы"
    unit: String(20),          -- "час", "м²", "шт"
    rate_type: String(20),     -- "fixed", "per_unit"
    value: Numeric(10,2),      -- Ставка в евро
    project_id: Integer FK,
    created_at: DateTime,
    updated_at: DateTime
)

-- Записи о работе (евро)
work_entries (
    entry_id: Integer PK,
    user_id: BigInteger FK,
    work_type_id: Integer FK,
    date: Date,
    description: String(500),
    quantity: Numeric(10,2),
    calculated_amount: Numeric(10,2),  -- Рассчитанная сумма в евро
    created_at: DateTime,
    updated_at: DateTime
)

-- Токены регистрации
tokens (
    id: Integer PK,
    token: String(64) UNIQUE,
    role: String,              -- 'director', 'worker'
    company_id: Integer FK,
    created_by_user_id: BigInteger FK,
    used_by_user_id: BigInteger FK NULL,
    expires_at: DateTime,
    used: Boolean DEFAULT FALSE,
    created_at: DateTime
)
```

### Связи между таблицами

- User.active_project → Project
- Project.company → Company
- WorkType.project → Project
- WorkEntry.user → User
- WorkEntry.work_type → WorkType
- UserCompanyRole.user → User
- UserCompanyRole.company → Company
- Token.company → Company

## 🤖 Команды бота v2.0

### Основные принципы v2.0
- **Только reply-клавиатуры** - удобный интерфейс с текстовыми кнопками
- **Токен-система регистрации** - регистрация только по ссылкам
- **Адаптивные меню** - интерфейс зависит от роли пользователя
- **Финляндская локализация** - евро, финские адреса, местные стандарты

## � КОМАНДА /START (ГИБРИДНАЯ СИСТЕМА)

### Логика команды /start
```python
async def cmd_start(message: types.Message, command: CommandObject):
    user_id = message.from_user.id

    # Проверка токена регистрации
    if command.args and command.args.startswith("reg_"):
        await handle_token_registration(message, command.args[4:])
        return

    # Гибридная система для админа
    if user_id == int(ADMIN_ID) and DEVELOPMENT_MODE:
        await show_role_selection_menu(message)
        return

    # Обычная логика для зарегистрированных пользователей
    await show_user_main_menu(message)

async def show_role_selection_menu(message: types.Message):
    """Меню выбора роли для админа в режиме разработки"""
    keyboard = InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="👑 Администратор", callback_data="role_admin")],
        [InlineKeyboardButton(text="👨‍💼 Директор", callback_data="role_director")],
        [InlineKeyboardButton(text="👷 Рабочий", callback_data="role_worker")]
    ])

    await message.answer(
        "🔧 РЕЖИМ РАЗРАБОТКИ\n"
        "Выберите роль для тестирования:",
        reply_markup=keyboard
    )
```

### Переменные окружения
```
# Режим разработки (включает выбор ролей для админа)
DEVELOPMENT_MODE=true

# В продакшене
DEVELOPMENT_MODE=false
```

## �👑 КОМАНДЫ АДМИНИСТРАТОРА

### Главное меню администратора
```
📍 Вы — администратор
👇 Выберите действие:
[➕ Добавить пользователя]
[📋 Список директоров и компаний]
[👷 Список рабочих]
[🗂 Удалённые компании]
[ℹ️ Инфо]
```

### ➕ Добавить пользователя
**Логика:**
1. Выбор типа: [👨 Директора] [👷 Рабочего]
2. Генерация уникального токена-ссылки
3. Отправка ссылки формата: `https://t.me/your_bot?start=reg_abc123`
4. Срок действия: 24 часа

### 📋 Список директоров и компаний
**Логика:**
1. Показ списка всех директоров с их компаниями
2. Действия: [🔄 Сбросить токен] [❌ Удалить компанию]
3. Удаление через soft delete

### 👷 Список рабочих
**Логика:**
1. Группировка по компаниям
2. Действия: [❌ Удалить рабочего] [🔄 Сбросить токен]

### 🗂 Удалённые компании
**Логика:**
1. Список soft-deleted компаний
2. Действия: [♻️ Восстановить] [❌ Удалить навсегда]

### ℹ️ Инфо
**Содержимое:**
- Количество компаний и пользователей
- Статистика токенов за 24 часа
- Дата последнего действия
- Версия бота

## 👨‍💼 КОМАНДЫ ДИРЕКТОРА

### Главное меню директора
```
🏢 Управление компаниями
👷 Управление рабочими
📊 Просмотр отчётов
📁 Экспорт / Импорт данных
🛠️ Редактировать типы работ
ℹ️ Инфо
```

### 🏢 Управление компаниями
**Действия:**
- [➕ Добавить компанию] - создание новой компании
- [🔁 Сменить активную компанию] - переключение между компаниями
- [🗑️ Удалить компанию] - soft delete компании

### 👷 Управление рабочими
**Действия:**
- [➕ Добавить рабочего] - генерация токен-ссылки
- [📄 Список рабочих] - управление рабочими с действиями [⚙️]

### 📊 Просмотр отчётов
**Типы отчётов:**
- [📆 Отчёт по дате]
- [👷 Отчёт по рабочему]
- [🏗️ Отчёт по проекту]

### 📁 Экспорт / Импорт данных
**Функции:**
- [📤 Экспорт Excel] - с настраиваемыми фильтрами
- [📤 Экспорт PDF] - часовые листы для рабочих
- [📥 Импорт типов работ] - загрузка из Excel

## 👷 КОМАНДЫ РАБОЧЕГО

### Главное меню рабочего
```
📝 Добавить работу
📋 Мои записи
📊 Мой отчёт
🏗️ Выбрать проект
➕ Новый проект
✏️ Редактировать проект
📤 Экспорт данных
ℹ️ Инфо
```

### 📝 Добавить работу (/addwork)
**FSM состояния (7 шагов):**
1. `waiting_for_date_choice` - выбор даты (сегодня/ввести дату)
2. `waiting_for_date_input` - ввод конкретной даты
3. `waiting_for_work_type` - выбор типа работы из активного проекта
4. `waiting_for_description` - описание выполненной работы
5. `waiting_for_quantity` - количество (часы, м², шт и т.д.)
6. `confirming_entry` - подтверждение записи с расчётом суммы
7. **Сохранение** - создание записи в БД

**Зависимости:**
- Требует установленный активный проект
- Требует типы работ в проекте
- Права: `can_add_work = True`

### 📋 Мои записи (/list)
**FSM состояния (2 шага):**
1. `selecting_filter` - выбор фильтра (все записи/за период)
2. `entering_date_range` - ввод диапазона дат

**Функции:**
- Пагинация по 10 записей на страницу
- Фильтрация: все записи или за период
- Inline-кнопки для каждой записи: [✏️ Редактировать] [🗑️ Удалить]

### 📊 Мой отчёт (/report)
**Параметры отчёта:**
- Фильтрация по активному проекту
- Группировка по типам работ
- Подсчёт общих сумм и количества
- Средние показатели

### 🏗️ Выбрать проект (/setproject)
**FSM состояния (1 шаг):**
1. `selecting_project` - выбор проекта из доступных

**Логика:**
- Показывает только проекты компании рабочего
- Устанавливает `active_project_id` в таблице `users`
- Обновляет доступные типы работ

### ➕ Новый проект (/newproject)
**FSM состояния (8 шагов):**
1. `creating_project_name` - ввод названия проекта
2. `creating_project_address` - ввод адреса проекта
3. `choosing_copy_source` - выбор источника копирования типов работ
4. `adding_work_type_name` - ввод названия типа работы
5. `adding_work_type_unit` - ввод единицы измерения
6. `adding_work_type_rate_type` - выбор типа ставки
7. `adding_work_type_value` - ввод значения ставки
8. `confirming_add_another_work_type` - подтверждение добавления еще типов

**Циклическое добавление типов работ:**
- Можно добавить неограниченное количество типов
- Каждый тип: название → единица → тип ставки → значение
- Валидация на каждом шаге
- Автоматическая установка как активного проекта

### ✏️ Редактировать проект (/editproject)
**FSM состояния (4 основных):**
1. `selecting_project` - выбор проекта для редактирования
2. `choosing_action` - выбор действия (проект/типы работ)
3. `editing_field` - редактирование поля проекта
4. `managing_work_types` - управление типами работ

### 📤 Экспорт данных (/export)
**FSM состояния (6 шагов):**
1. `choosing_format` - выбор формата (Excel/PDF)
2. `choosing_project` - выбор проекта
3. `choosing_period` - выбор периода
4. `entering_date_range` - ввод дат
5. `choosing_columns` - выбор столбцов (только Excel)
6. `confirming_export` - подтверждение и генерация

### ✏️ Редактирование записи (/edit)
**FSM состояния (3 шага):**
1. `selecting_entry_to_edit` - выбор записи из последних 10
2. `choosing_edit_field` - выбор поля (дата, тип, количество, описание)
3. `entering_edit_value` - ввод нового значения

### 🗑️ Удаление записи (/delete)
**FSM состояния (2 шага):**
1. `selecting_entry_to_delete` - выбор записи
2. `confirming_delete` - подтверждение удаления

### 3. `/edit` - Редактирование записи

**Файл:** `handlers/edit.py`
**FSM:** EditWorkStates
**Права:** Доступ к записи через AccessControlService

**FSM состояния:**
1. `selecting_entry_to_edit` - выбор записи для редактирования
2. `choosing_edit_field` - выбор поля для редактирования
3. `entering_edit_value` - ввод нового значения

**Логика:**
1. Показывает последние 10 записей пользователя
2. Проверяет доступ к выбранной записи
3. Предлагает поля для редактирования: дата, тип работы, количество, описание
4. Обновляет запись в БД

**Используемые сервисы:**
- EntryDAO.get_recent_by_user()
- AccessControlService.can_access_entry()
- EntryDAO.update()

### 4. `/delete` - Удаление записи

**Файл:** `handlers/delete.py`
**FSM:** DeleteWorkStates
**Права:** Доступ к записи через AccessControlService

**FSM состояния:**
1. `selecting_entry_to_delete` - выбор записи для удаления
2. `confirming_delete` - подтверждение удаления

**Логика:**
1. Показывает последние 10 записей пользователя
2. Проверяет доступ к выбранной записи
3. Запрашивает подтверждение удаления
4. Удаляет запись из БД

**Используемые сервисы:**
- EntryDAO.get_recent_by_user()
- AccessControlService.can_access_entry()
- EntryDAO.delete()

### 5. `/list` - Просмотр списка записей

**Файл:** `handlers/list.py`
**FSM:** ListStates
**Права:** Доступ к записям пользователя

**FSM состояния:**
1. `selecting_filter` - выбор фильтра (все/за период)
2. `entering_date_range` - ввод диапазона дат

**Логика:**
1. Предлагает фильтры: "Все записи" / "За период"
2. Для периода запрашивает диапазон дат
3. Получает записи с фильтрацией
4. Форматирует и отображает список

**Используемые сервисы:**
- EntryDAO.get_by_user_and_date_range()
- validate_date_range()

### 6. `/report` - Генерация отчета

**Файл:** `handlers/report.py`
**FSM:** Нет
**Права:** Deprecated role check (нужно обновить на RBAC)

**Логика:**
1. Проверяет наличие активного проекта
2. Получает агрегированные данные по проекту
3. Рассчитывает общую сумму
4. Форматирует отчет в Markdown

**Используемые сервисы:**
- ProjectDAO.get_active_for_user()
- ProjectDAO.get_report_data()

**Формат отчета:**
```
📊 Отчет по проекту: [Название]

[Тип работы]: [Количество] [Единица] - [Сумма] руб.
...
💰 Общая сумма: [Сумма] руб.
```

### 7. `/export` - Экспорт данных

**Файл:** `handlers/export.py`
**FSM:** ExportStates
**Права:** Deprecated role check (нужно обновить на RBAC)

**FSM состояния:**
1. `choosing_format` - выбор формата (Excel/PDF)
2. `choosing_project` - выбор проекта
3. `choosing_period` - выбор периода
4. `entering_date_range` - ввод диапазона дат
5. `choosing_columns` - выбор столбцов (только для Excel)
6. `confirming_export` - подтверждение экспорта

**Логика по шагам:**

**Шаг 1 - cmd_export:**
- Показывает форматы: "Excel" / "PDF"

**Шаг 2 - process_format_selection:**
- Сохраняет выбранный формат
- Получает проекты пользователя
- Показывает список проектов

**Шаг 3 - process_project_selection:**
- Сохраняет выбранный проект
- Показывает периоды: "Все время" / "За период"

**Шаг 4 - process_period_selection:**
- Для "За период" запрашивает даты
- Для "Все время" переходит к выбору столбцов

**Шаг 5 - process_date_range_for_export:**
- Валидирует диапазон дат
- Для PDF пропускает выбор столбцов
- Для Excel переходит к выбору столбцов

**Шаг 6 - process_column_selection:**
- Позволяет выбрать столбцы для экспорта
- Обновляет список выбранных столбцов

**Шаг 7 - process_export_confirmation:**
- Генерирует файл через ExportService
- Отправляет файл пользователю

**Используемые сервисы:**
- ProjectDAO.get_by_user_id()
- ExportService.export_to_excel()
- ExportService.export_to_pdf()
- validate_date_range()

**Клавиатуры:**
- create_export_format_menu()
- create_export_period_menu()
- create_columns_keyboard()

### 8. `/setproject` - Выбор активного проекта

**Файл:** `handlers/project/common.py`
**FSM:** ProjectSelectionStates
**Права:** Доступ к проектам пользователя

**FSM состояния:**
1. `selecting_project` - выбор проекта из списка

**Логика:**
1. Получает проекты пользователя (ProjectDAO.get_by_user_id)
2. Показывает список проектов
3. Устанавливает выбранный проект как активный
4. Обновляет пользователя в БД

**Используемые сервисы:**
- ProjectDAO.get_by_user_id()
- UserDAO.set_active_project()

**Клавиатуры:**
- project_keyboard(projects)

### 9. `/newproject` - Создание нового проекта

**Файл:** `handlers/project/new_project.py`
**FSM:** NewProjectStates
**Права:** Права на создание проектов

**FSM состояния:**
1. `creating_project_name` - ввод названия проекта
2. `creating_project_address` - ввод адреса проекта
3. `choosing_copy_source` - выбор источника для копирования типов работ
4. `adding_work_type_name` - ввод названия типа работы
5. `adding_work_type_unit` - ввод единицы измерения
6. `adding_work_type_rate_type` - выбор типа ставки
7. `adding_work_type_value` - ввод значения ставки
8. `confirming_add_another_work_type` - подтверждение добавления еще одного типа

**Логика:**
1. Запрашивает название проекта
2. Запрашивает адрес проекта
3. Предлагает скопировать типы работ из существующего проекта
4. Циклически добавляет типы работ:
   - Название типа работы
   - Единица измерения
   - Тип ставки (фиксированная/за единицу)
   - Значение ставки
5. Создает проект и типы работ в транзакции
6. Устанавливает проект как активный

**Используемые сервисы:**
- ProjectDAO.create()
- WorkTypeDAO.create()
- WorkTypeDAO.copy_from_project()
- UserDAO.set_active_project()
- TransactionService
- WorkTypeWizardService

**Валидация:**
- validate_project_name()
- Проверка положительных чисел для ставок

### 10. `/editproject` - Редактирование проекта

**Файл:** `handlers/project/edit_project.py`
**FSM:** EditProjectStates
**Права:** Доступ к проектам пользователя

**FSM состояния:**
1. `selecting_project` - выбор проекта для редактирования
2. `choosing_action` - выбор действия (редактировать/типы работ)
3. `editing_field` - редактирование поля проекта
4. `managing_work_types` - управление типами работ

**Логика:**
1. Показывает список проектов пользователя
2. Предлагает действия: редактировать проект / управлять типами работ
3. Для редактирования: позволяет изменить название/адрес
4. Для типов работ: добавление/редактирование/удаление

**Используемые сервисы:**
- ProjectDAO.get_by_user_id()
- ProjectDAO.update()
- WorkTypeDAO.get_by_project_id()
- WorkTypeDAO.create()
- WorkTypeDAO.update()
- WorkTypeDAO.delete()

### 11. `/admin` - Панель администратора

**Файл:** `handlers/admin.py`
**FSM:** AdminStates
**Права:** is_admin (глобальный администратор)

**Доступные действия:**
- Создание компаний
- Создание токенов директоров
- Просмотр всех компаний
- Управление пользователями
- Просмотр статистики

**Логика:**
1. Показывает админ-панель с кнопками
2. Обрабатывает callback запросы для каждого действия
3. Использует inline клавиатуры для навигации

**Используемые сервисы:**
- AdminService.create_company()
- AdminService.create_director_token()
- AdminService.get_all_companies()
- AdminService.get_company_stats()

**Клавиатуры:**
- create_admin_menu()
- create_companies_keyboard()
- create_company_management_keyboard()

### 12. `/manage` - Панель директора

**Файл:** `handlers/director.py`
**FSM:** DirectorStates
**Права:** can_manage_workers

**Доступные действия:**
- Создание токенов для рабочих
- Управление рабочими компании
- Просмотр статистики компании
- Управление проектами компании

**Логика:**
1. Показывает панель управления компанией
2. Позволяет создавать токены для рабочих
3. Показывает список рабочих и их статистику

**Используемые сервисы:**
- DirectorService.create_worker_token()
- DirectorService.get_company_workers()
- DirectorService.get_worker_stats()

### 13. `/register` - Регистрация по токену

**Файл:** `handlers/registration/register.py`
**FSM:** RegistrationStates
**Права:** Все пользователи

**FSM состояния:**
1. `waiting_for_token` - ввод токена
2. `waiting_for_company_name` - ввод названия компании (для директоров)

**Логика:**

**Для рабочих (worker token):**
1. Валидирует токен
2. Регистрирует пользователя в компании с ролью worker
3. Помечает токен как использованный

**Для директоров (director token):**
1. Валидирует токен
2. Если токен без company_id - запрашивает название новой компании
3. Создает компанию (если нужно)
4. Регистрирует пользователя как директора

**Используемые сервисы:**
- AuthService.register_by_token()
- DirectorService.create_company_and_register()
- Validators.validate_token()
- Validators.validate_company_name()

**Валидация:**
- Проверка формата токена
- Проверка срока действия токена
- Валидация названия компании

## 🔧 Middleware и декораторы

### RBACMiddleware

**Файл:** `middleware/rbac_middleware.py`

**Функции:**
- Автоматическое получение прав пользователя для каждого сообщения
- Добавление в контекст: user_permissions, user_id, is_admin
- **Система виртуальных прав** для суперпользователя (ADMIN_ID=*********)

### Система виртуальных прав

**Файлы:**
- `utils/dev_session.py` - управление виртуальными правами
- `keyboards/dev.py` - интерфейс выбора ролей
- `states.py` - FSM состояния для dev-режима

**Функциональность:**
- Суперпользователь может выбирать роль через `/start`
- Виртуальные права имеют приоритет над реальными правами БД
- Полная изоляция от реальных данных (фиктивные company_id=999, project_id=9999)
- Логирование всех действий в dev-режиме

### Декораторы доступа

**@require_permission(permission_key):**
- Проверяет наличие конкретного права у пользователя
- Пример: @require_permission("can_add_work")

**@require_role(role, company_id=None):**
- Проверяет наличие роли в компании
- Пример: @require_role("director", company_id=123)

## 📱 Клавиатуры

### Адаптивное главное меню

**Функция:** `create_main_menu(user_permissions)`
**Файл:** `keyboards/common.py`

**Логика формирования кнопок:**
- Если `is_admin` → показывает все кнопки + "👑 Админ панель"
- Если `can_manage_workers` → добавляет "👨‍💼 Управление"
- Если `can_add_work` → добавляет кнопки работы с записями
- Если нет ролей → показывает только "🔐 Регистрация"

### Специализированные клавиатуры

- `create_export_format_menu()` - выбор формата экспорта
- `create_columns_keyboard()` - выбор столбцов для экспорта
- `project_keyboard()` - список проектов
- `yes_no_keyboard()` - подтверждение действий
- `create_admin_menu()` - админ панель
- `create_companies_keyboard()` - список компаний

## 🌐 Локализация

**Файл:** `localization/texts.py`

**Функция:** `get_text(key, **kwargs)`

**Основные ключи:**
- Команды меню: "menu_add_work", "menu_list", "menu_report", etc.
- Сообщения: "no_active_project", "access_denied", "success_message"
- Кнопки: "today", "cancel", "done", "yes_delete"
- Ошибки: "error_report", "entry_not_found", "invalid_date"

## 🔄 FSM состояния

**Файл:** `states.py`

**Группы состояний:**
- `AddWorkStates` - добавление записей
- `EditWorkStates` - редактирование записей
- `DeleteWorkStates` - удаление записей
- `NewProjectStates` - создание проектов
- `EditProjectStates` - редактирование проектов
- `ProjectSelectionStates` - выбор проекта
- `ListStates` - просмотр списков
- `ExportStates` - экспорт данных
- `RegistrationStates` - регистрация
- `AdminStates` - админ панель
- `DirectorStates` - панель директора

## 🛠️ Сервисы

### AuthService
- `get_user_permissions()` - получение прав пользователя
- `register_by_token()` - регистрация по токену
- `check_company_access()` - проверка доступа к компании

### AdminService
- `create_company()` - создание компании
- `create_director_token()` - создание токена директора
- `get_all_companies()` - получение всех компаний

### DirectorService
- `create_worker_token()` - создание токена рабочего
- `get_company_workers()` - получение рабочих компании
- `create_company_and_register()` - создание компании и регистрация

### ExportService
- `export_to_excel()` - экспорт в Excel
- `export_to_pdf()` - экспорт в PDF

### Вспомогательные сервисы
- `calculate_sum()` - расчет суммы за работу
- `validate_date_range()` - валидация дат
- `AccessControlService` - контроль доступа к записям

## 🗃️ DAO (Data Access Objects)

### UserDAO
- `get_by_id()` - получение пользователя
- `create_if_not_exists()` - создание если не существует
- `set_active_project()` - установка активного проекта

### ProjectDAO
- `get_by_user_id()` - проекты пользователя
- `get_active_for_user()` - активный проект
- `create()` - создание проекта
- `get_report_data()` - данные для отчета

### EntryDAO
- `create()` - создание записи
- `get_recent_by_user()` - последние записи
- `update()` - обновление записи
- `delete()` - удаление записи

### WorkTypeDAO
- `get_by_project_id()` - типы работ проекта
- `create()` - создание типа работы
- `copy_from_project()` - копирование типов

### TokenDAO
- `create()` - создание токена
- `get_valid_token()` - получение действительного токена
- `mark_as_used()` - пометка как использованный

## ⚠️ Обработка ошибок

### Стандартные паттерны

1. **Try-catch блоки** во всех обработчиках
2. **Логирование ошибок** с полным стеком
3. **Пользовательские сообщения** об ошибках
4. **Возврат к главному меню** при критических ошибках
5. **Rollback транзакций** при ошибках БД

### Типы ошибок

- `ValidationError` - ошибки валидации данных
- `AccessDeniedError` - ошибки доступа
- `DatabaseError` - ошибки БД
- `TokenExpiredError` - истекшие токены

## 🔗 Зависимости команд

### Критические зависимости

1. **Для работы с записями** (`/addwork`, `/edit`, `/delete`, `/list`):
   - Требует установленный активный проект (`/setproject`)
   - Требует роль worker или director

2. **Для создания проектов** (`/newproject`):
   - Требует права на создание проектов
   - Автоматически устанавливает как активный

3. **Для управления** (`/admin`, `/manage`):
   - Требует соответствующие роли
   - Доступ к конкретным компаниям

### Рекомендуемый порядок использования

1. `/start` - регистрация и получение меню
2. `/register` - регистрация по токену (если нужно)
3. `/newproject` или `/setproject` - настройка проекта
4. `/addwork` - добавление записей о работе
5. `/report`, `/export` - получение отчетов

## 🚀 Готовность к масштабированию

### Архитектурные решения

1. **Модульная структура** - легко добавлять новые команды
2. **Слоистая архитектура** - разделение UI, бизнес-логики и данных
3. **RBAC система** - гибкое управление правами
4. **Мультикомпании** - изоляция данных
5. **Асинхронность** - готовность к высоким нагрузкам

### Точки расширения

1. **Новые команды** - добавление в handlers/
2. **Новые роли** - расширение RBAC
3. **API интеграция** - вынос сервисов в отдельные модули
4. **NLP модуль** - интеграция через middleware
5. **Мобильное приложение** - использование существующих сервисов

---

*Этот документ содержит полную техническую спецификацию для воспроизведения функционала Worklog Bot в новом проекте.*