"""
Обработчики для управления проектами рабочим (адаптировано из examples).

Функции:
- Создание нового проекта (8-шаговый FSM)
- Выбор активного проекта
- Управление типами работ в проекте

Адаптировано под aiogram v3 и Reply-клавиатуры.
"""
import logging
from aiogram import Router, F, types
from aiogram.fsm.context import FSMContext

from db.session import get_session
from db.models import User, Project, WorkType
from services.project_service import ProjectService
from services.calculator import validate_positive_number
from keyboards.worker import create_worker_menu
from states import WorkerProjectStates
from utils.exceptions import ValidationError

logger = logging.getLogger(__name__)

# Создаем роутер для aiogram v3
router = Router()

# Константы валидации
MAX_PROJECT_NAME_LENGTH = 100
MAX_ADDRESS_LENGTH = 255
MAX_WORK_TYPE_NAME_LENGTH = 100
MAX_UNIT_LENGTH = 20


async def start_new_project_flow(message: types.Message, state: FSMContext):
    """
    Начинает FSM сценарий создания нового проекта.
    Вызывается из handlers/worker.py
    """
    await state.clear()
    
    keyboard = types.ReplyKeyboardMarkup(
        keyboard=[
            [types.KeyboardButton(text="🔙 Отмена")]
        ],
        resize_keyboard=True
    )
    
    await message.answer(
        "🏗️ **Создание нового проекта**\n\n"
        "📝 Введите название проекта:",
        reply_markup=keyboard,
        parse_mode="Markdown"
    )
    await state.set_state(WorkerProjectStates.creating_project_name)


@router.message(WorkerProjectStates.creating_project_name)
async def process_project_name(message: types.Message, state: FSMContext):
    """
    Обработка ввода названия проекта.
    """
    if message.text == "🔙 Отмена":
        await message.answer(
            "❌ Создание проекта отменено.",
            reply_markup=create_worker_menu()
        )
        await state.clear()
        return
    
    project_name = message.text.strip()
    user_id = message.from_user.id
    
    # Валидация названия
    if not project_name:
        await message.answer("❌ Название проекта не может быть пустым.")
        return
    
    if len(project_name) > MAX_PROJECT_NAME_LENGTH:
        await message.answer(
            f"❌ Название слишком длинное (максимум {MAX_PROJECT_NAME_LENGTH} символов).\n"
            f"Текущая длина: {len(project_name)} символов."
        )
        return
    
    try:
        async with get_session() as session:
            # Получаем компанию пользователя
            user = await session.get(User, user_id)
            if not user or not user.active_company_id:
                await message.answer(
                    "❌ Вы не привязаны к компании.",
                    reply_markup=create_worker_menu()
                )
                await state.clear()
                return
            
            # Проверяем уникальность названия в компании
            existing_project = await ProjectService.get_project_by_name(
                session=session,
                name=project_name,
                company_id=user.active_company_id
            )
            
            if existing_project:
                await message.answer(
                    f"❌ Проект с названием '{project_name}' уже существует в вашей компании."
                )
                return
        
        await state.update_data(
            project_name=project_name,
            company_id=user.active_company_id,
            created_by=user_id
        )
        
        keyboard = types.ReplyKeyboardMarkup(
            keyboard=[
                [types.KeyboardButton(text="Пропустить")],
                [types.KeyboardButton(text="🔙 Отмена")]
            ],
            resize_keyboard=True
        )
        
        await message.answer(
            "📍 Введите адрес проекта (или нажмите 'Пропустить'):",
            reply_markup=keyboard
        )
        await state.set_state(WorkerProjectStates.creating_project_address)
    
    except Exception as e:
        logger.error(f"Ошибка при проверке названия проекта для user {user_id}: {e}")
        await message.answer(
            "❌ Произошла ошибка. Попробуйте позже.",
            reply_markup=create_worker_menu()
        )
        await state.clear()


@router.message(WorkerProjectStates.creating_project_address)
async def process_project_address(message: types.Message, state: FSMContext):
    """
    Обработка ввода адреса проекта.
    """
    if message.text == "🔙 Отмена":
        await message.answer(
            "❌ Создание проекта отменено.",
            reply_markup=create_worker_menu()
        )
        await state.clear()
        return
    
    address = None if message.text == "Пропустить" else message.text.strip()
    
    # Валидация адреса
    if address and len(address) > MAX_ADDRESS_LENGTH:
        await message.answer(
            f"❌ Адрес слишком длинный (максимум {MAX_ADDRESS_LENGTH} символов).\n"
            f"Текущая длина: {len(address)} символов."
        )
        return
    
    await state.update_data(project_address=address)
    
    # Предлагаем скопировать типы работ из существующего проекта
    user_id = message.from_user.id
    try:
        async with get_session() as session:
            user = await session.get(User, user_id)
            user_projects = await ProjectService.get_company_projects(
                session=session,
                company_id=user.active_company_id
            )
        
        if user_projects:
            # Есть проекты - предлагаем копирование
            keyboard = types.ReplyKeyboardMarkup(
                keyboard=[
                    [types.KeyboardButton(text="Скопировать из существующего")],
                    [types.KeyboardButton(text="Создать новые")],
                    [types.KeyboardButton(text="🔙 Отмена")]
                ],
                resize_keyboard=True,
                one_time_keyboard=True
            )
            
            await message.answer(
                "🔧 **Типы работ**\n\n"
                "Хотите скопировать типы работ из существующего проекта или создать новые?",
                reply_markup=keyboard,
                parse_mode="Markdown"
            )
            await state.set_state(WorkerProjectStates.choosing_copy_source)
        else:
            # Нет проектов - сразу к созданию типов работ
            await start_work_type_creation(message, state)
    
    except Exception as e:
        logger.error(f"Ошибка при обработке адреса проекта для user {user_id}: {e}")
        await message.answer(
            "❌ Произошла ошибка. Попробуйте позже.",
            reply_markup=create_worker_menu()
        )
        await state.clear()


@router.message(WorkerProjectStates.choosing_copy_source)
async def process_copy_source_choice(message: types.Message, state: FSMContext):
    """
    Обработка выбора источника копирования типов работ.
    """
    choice = message.text
    
    if choice == "🔙 Отмена":
        await message.answer(
            "❌ Создание проекта отменено.",
            reply_markup=create_worker_menu()
        )
        await state.clear()
        return
    
    if choice == "Скопировать из существующего":
        await show_projects_for_copying(message, state)
    elif choice == "Создать новые":
        await start_work_type_creation(message, state)
    else:
        await message.answer("❌ Неверный выбор. Попробуйте еще раз.")


async def show_projects_for_copying(message: types.Message, state: FSMContext):
    """Показывает список проектов для копирования типов работ."""
    user_id = message.from_user.id
    
    try:
        async with get_session() as session:
            user = await session.get(User, user_id)
            projects = await ProjectService.get_company_projects(
                session=session,
                company_id=user.active_company_id
            )
        
        if not projects:
            await start_work_type_creation(message, state)
            return
        
        keyboard_buttons = []
        project_map = {}
        
        for project in projects:
            keyboard_buttons.append([types.KeyboardButton(text=project.name)])
            project_map[project.name] = project.project_id
        
        keyboard_buttons.append([types.KeyboardButton(text="🔙 Отмена")])
        
        keyboard = types.ReplyKeyboardMarkup(
            keyboard=keyboard_buttons,
            resize_keyboard=True,
            one_time_keyboard=True
        )
        
        await state.update_data(copy_projects_map=project_map)
        await message.answer(
            "📁 Выберите проект для копирования типов работ:",
            reply_markup=keyboard
        )
        # Остаемся в том же состоянии для обработки выбора проекта
    
    except Exception as e:
        logger.error(f"Ошибка при показе проектов для копирования для user {user_id}: {e}")
        await start_work_type_creation(message, state)


async def start_work_type_creation(message: types.Message, state: FSMContext):
    """Начинает процесс создания типов работ."""
    await state.update_data(work_types=[])  # Инициализируем список типов работ
    
    keyboard = types.ReplyKeyboardMarkup(
        keyboard=[
            [types.KeyboardButton(text="🔙 Отмена")]
        ],
        resize_keyboard=True
    )
    
    await message.answer(
        "🔧 **Добавление типа работы**\n\n"
        "📝 Введите название типа работы:",
        reply_markup=keyboard,
        parse_mode="Markdown"
    )
    await state.set_state(WorkerProjectStates.adding_work_type_name)


@router.message(WorkerProjectStates.adding_work_type_name)
async def process_work_type_name(message: types.Message, state: FSMContext):
    """
    Обработка ввода названия типа работы.
    """
    if message.text == "🔙 Отмена":
        await message.answer(
            "❌ Создание проекта отменено.",
            reply_markup=create_worker_menu()
        )
        await state.clear()
        return
    
    work_type_name = message.text.strip()
    
    if not work_type_name:
        await message.answer("❌ Название типа работы не может быть пустым.")
        return
    
    if len(work_type_name) > MAX_WORK_TYPE_NAME_LENGTH:
        await message.answer(
            f"❌ Название слишком длинное (максимум {MAX_WORK_TYPE_NAME_LENGTH} символов).\n"
            f"Текущая длина: {len(work_type_name)} символов."
        )
        return
    
    await state.update_data(current_work_type_name=work_type_name)
    
    keyboard = types.ReplyKeyboardMarkup(
        keyboard=[
            [types.KeyboardButton(text="час"), types.KeyboardButton(text="м²")],
            [types.KeyboardButton(text="шт"), types.KeyboardButton(text="м")],
            [types.KeyboardButton(text="🔙 Отмена")]
        ],
        resize_keyboard=True
    )
    
    await message.answer(
        "📏 Введите единицу измерения (или выберите из предложенных):",
        reply_markup=keyboard
    )
    await state.set_state(WorkerProjectStates.adding_work_type_unit)


@router.message(WorkerProjectStates.adding_work_type_unit)
async def process_work_type_unit(message: types.Message, state: FSMContext):
    """
    Обработка ввода единицы измерения.
    """
    if message.text == "🔙 Отмена":
        await message.answer(
            "❌ Создание проекта отменено.",
            reply_markup=create_worker_menu()
        )
        await state.clear()
        return
    
    unit = message.text.strip()
    
    if not unit:
        await message.answer("❌ Единица измерения не может быть пустой.")
        return
    
    if len(unit) > MAX_UNIT_LENGTH:
        await message.answer(
            f"❌ Единица измерения слишком длинная (максимум {MAX_UNIT_LENGTH} символов)."
        )
        return
    
    await state.update_data(current_work_type_unit=unit)
    
    # Выбор типа ставки
    keyboard = types.ReplyKeyboardMarkup(
        keyboard=[
            [types.KeyboardButton(text="Фиксированная ставка")],
            [types.KeyboardButton(text="Ставка за единицу")],
            [types.KeyboardButton(text="🔙 Отмена")]
        ],
        resize_keyboard=True,
        one_time_keyboard=True
    )
    
    await message.answer(
        "💰 Выберите тип ставки:",
        reply_markup=keyboard
    )
    await state.set_state(WorkerProjectStates.adding_work_type_rate_type)


@router.message(WorkerProjectStates.adding_work_type_rate_type)
async def process_rate_type(message: types.Message, state: FSMContext):
    """Обработка выбора типа ставки."""
    choice = message.text
    
    if choice == "🔙 Отмена":
        await message.answer(
            "❌ Создание проекта отменено.",
            reply_markup=create_worker_menu()
        )
        await state.clear()
        return
    
    if choice == "Фиксированная ставка":
        rate_type = "fixed"
        prompt = "💰 Введите фиксированную ставку в евро:"
    elif choice == "Ставка за единицу":
        rate_type = "per_unit"
        data = await state.get_data()
        unit = data.get("current_work_type_unit", "единицу")
        prompt = f"💰 Введите ставку за {unit} в евро:"
    else:
        await message.answer("❌ Неверный выбор. Попробуйте еще раз.")
        return
    
    await state.update_data(current_work_type_rate_type=rate_type)
    
    keyboard = types.ReplyKeyboardMarkup(
        keyboard=[
            [types.KeyboardButton(text="🔙 Отмена")]
        ],
        resize_keyboard=True
    )
    
    await message.answer(
        f"{prompt}\n"
        "Можно использовать запятую как разделитель (например: 25,50)",
        reply_markup=keyboard
    )
    await state.set_state(WorkerProjectStates.adding_work_type_value)


@router.message(WorkerProjectStates.adding_work_type_value)
async def process_rate_value(message: types.Message, state: FSMContext):
    """
    Обработка ввода значения ставки.
    """
    if message.text == "🔙 Отмена":
        await message.answer(
            "❌ Создание проекта отменено.",
            reply_markup=create_worker_menu()
        )
        await state.clear()
        return
    
    try:
        rate_value = validate_positive_number(message.text, "ставка")
    except ValueError as e:
        await message.answer(f"❌ {e}")
        return
    
    # Сохраняем тип работы
    data = await state.get_data()
    work_types = data.get("work_types", [])
    
    new_work_type = {
        "name": data["current_work_type_name"],
        "unit": data["current_work_type_unit"],
        "rate_type": data["current_work_type_rate_type"],
        "value": rate_value
    }
    work_types.append(new_work_type)
    
    await state.update_data(work_types=work_types)
    
    # Спрашиваем о добавлении еще одного типа работы
    keyboard = types.ReplyKeyboardMarkup(
        keyboard=[
            [types.KeyboardButton(text="✅ Да")],
            [types.KeyboardButton(text="❌ Нет, создать проект")],
            [types.KeyboardButton(text="🔙 Отмена")]
        ],
        resize_keyboard=True,
        one_time_keyboard=True
    )
    
    await message.answer(
        f"✅ **Тип работы добавлен:**\n"
        f"📝 {new_work_type['name']}\n"
        f"📏 {new_work_type['unit']}\n"
        f"💰 {rate_value} €\n\n"
        f"Добавить еще один тип работы?",
        reply_markup=keyboard,
        parse_mode="Markdown"
    )
    await state.set_state(WorkerProjectStates.confirming_add_another)


@router.message(WorkerProjectStates.confirming_add_another)
async def process_add_another_confirmation(message: types.Message, state: FSMContext):
    """
    Обработка подтверждения добавления еще одного типа работы.
    """
    choice = message.text
    
    if choice == "🔙 Отмена":
        await message.answer(
            "❌ Создание проекта отменено.",
            reply_markup=create_worker_menu()
        )
        await state.clear()
        return
    elif choice == "✅ Да":
        # Добавляем еще один тип работы
        await start_work_type_creation(message, state)
    elif choice == "❌ Нет, создать проект":
        # Создаем проект
        await create_project_with_work_types(message, state)
    else:
        await message.answer("❌ Неверный выбор. Попробуйте еще раз.")


async def create_project_with_work_types(message: types.Message, state: FSMContext):
    """
    Создает проект со всеми типами работ в транзакции.
    """
    data = await state.get_data()
    user_id = message.from_user.id
    
    try:
        await message.answer("⏳ Создаю проект...")
        
        async with get_session() as session:
            # Создаем проект
            project = await ProjectService.create_project(
                session=session,
                name=data["project_name"],
                address=data.get("project_address"),
                created_by=user_id,
                company_id=data["company_id"]
            )
            
            # Создаем типы работ
            work_types = data.get("work_types", [])
            for work_type_data in work_types:
                await ProjectService.create_work_type(
                    session=session,
                    project_id=project.project_id,
                    name=work_type_data["name"],
                    unit=work_type_data["unit"],
                    rate_type=work_type_data["rate_type"],
                    value=work_type_data["value"]
                )
            
            # Устанавливаем проект как активный
            await ProjectService.set_active_project(session, user_id, project.project_id)
        
        success_text = (
            f"✅ **Проект создан успешно!**\n\n"
            f"📝 **Название:** {data['project_name']}\n"
            f"📍 **Адрес:** {data.get('project_address', 'Не указан')}\n"
            f"🔧 **Типов работ:** {len(work_types)}\n\n"
            f"Проект установлен как активный."
        )
        
        await message.answer(
            success_text,
            reply_markup=create_worker_menu(),
            parse_mode="Markdown"
        )
        await state.clear()
        
        logger.info(f"Создан проект {project.project_id} пользователем {user_id}")
    
    except Exception as e:
        logger.error(f"Ошибка при создании проекта для user {user_id}: {e}")
        await message.answer(
            "❌ Ошибка при создании проекта. Попробуйте позже.",
            reply_markup=create_worker_menu()
        )
        await state.clear()
