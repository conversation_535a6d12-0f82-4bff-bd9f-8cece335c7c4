# 🇫🇮 Примеры локализации для Финляндии

## 🏢 Названия компаний

### Финские форматы компаний
```python
FINNISH_COMPANY_EXAMPLES = {
    "construction_companies": [
        "Rakennus Virtanen Oy",
        "BuildMaster Helsinki Ab",
        "Kivimiehen Tmi",
        "Sähköasennus Korhonen Oy",
        "Puutyö Professionals Oy",
        "Betonityö Espoo Ab",
        "Kattoremontti Vantaa Oy",
        "Sisustustyö Tampere Tmi"
    ],
    
    "company_suffixes": {
        "Oy": "Osa<PERSON><PERSON><PERSON><PERSON> (частная компания с ограниченной ответственностью)",
        "Ab": "Aktiebolag (шведский эквивалент Oy)",
        "Tmi": "<PERSON><PERSON><PERSON><PERSON> (индивидуальное предпринимательство)",
        "Ay": "<PERSON><PERSON><PERSON> (полное товарищество)",
        "Ky": "Kommandiittiyhtiö (коммандитное товарищество)"
    }
}
```

## 📍 Адреса и локации

### Финские адреса
```python
FINNISH_ADDRESSES = {
    "helsinki": [
        "Mannerheimintie 15, 00100 Helsinki",
        "Aleksanterinkatu 22, 00170 Helsinki", 
        "Bulevardi 7, 00120 Helsinki",
        "Unioninkatu 45, 00170 Helsinki"
    ],
    
    "espoo": [
        "Kielotie 15, 02150 Espoo",
        "Tapiolantie 6, 02100 Espoo",
        "Otakaari 24, 02150 Espoo"
    ],
    
    "vantaa": [
        "Tikkurilantie 10, 01300 Vantaa",
        "Koivukylänväylä 2, 01640 Vantaa"
    ],
    
    "tampere": [
        "Hämeenkatu 11, 33200 Tampere",
        "Satakunnankatu 18, 33210 Tampere"
    ]
}
```

## 🏗️ Проекты и объекты

### Типичные строительные проекты в Финляндии
```python
FINNISH_PROJECTS = {
    "residential": [
        "Asuntokohde Kallio",
        "Rivitalojen rakentaminen Espoo",
        "Kerrostalo Kamppi",
        "Omakotitalo Vantaa",
        "Loft-asunnot Punavuori"
    ],
    
    "commercial": [
        "Toimistokeskus Pasila",
        "Kauppakeskus Itäkeskus",
        "Hotelli Keskusta",
        "Ravintola Kruununhaka",
        "Liikekiinteistö Töölö"
    ],
    
    "industrial": [
        "Teollisuushalli Vantaa",
        "Logistiikkakeskus Kerava",
        "Tuotantolaitos Espoo"
    ],
    
    "renovation": [
        "Peruskorjaus Eira",
        "Kylpyhuoneremontti Kallio",
        "Keittiöremontti Töölö",
        "Julkisivuremontti Kamppi"
    ]
}
```

## 💰 Ставки и оплата

### Типичные ставки в Финляндии (2024-2025)
```python
FINNISH_RATES = {
    "hourly_rates_euro": {
        "general_construction": {"min": 25, "max": 35, "average": 30},
        "electrical_work": {"min": 30, "max": 45, "average": 37},
        "plumbing": {"min": 28, "max": 40, "average": 34},
        "painting": {"min": 22, "max": 32, "average": 27},
        "roofing": {"min": 30, "max": 42, "average": 36},
        "flooring": {"min": 25, "max": 38, "average": 31}
    },
    
    "piece_rates_euro": {
        "bricklaying": {"unit": "м²", "rate": 45},
        "plastering": {"unit": "м²", "rate": 25},
        "tiling": {"unit": "м²", "rate": 35},
        "painting_walls": {"unit": "м²", "rate": 15},
        "flooring_laminate": {"unit": "м²", "rate": 20},
        "electrical_points": {"unit": "шт", "rate": 85}
    }
}
```

## 🔧 Типы работ

### Строительные работы на русском языке
```python
WORK_TYPES_RUSSIAN = {
    "construction": {
        "masonry": "Кладочные работы",
        "concrete": "Бетонные работы", 
        "reinforcement": "Арматурные работы",
        "formwork": "Опалубочные работы",
        "demolition": "Демонтажные работы"
    },
    
    "finishing": {
        "plastering": "Штукатурные работы",
        "painting": "Малярные работы",
        "tiling": "Плиточные работы",
        "flooring": "Напольные покрытия",
        "wallpaper": "Обойные работы"
    },
    
    "electrical": {
        "wiring": "Электромонтаж",
        "lighting": "Освещение",
        "outlets": "Розетки и выключатели",
        "panels": "Электрощиты",
        "automation": "Автоматика"
    },
    
    "plumbing": {
        "pipes": "Трубопроводы",
        "heating": "Отопление",
        "sanitary": "Сантехника",
        "ventilation": "Вентиляция",
        "water_supply": "Водоснабжение"
    }
}
```

## 📅 Рабочее время

### Финские стандарты рабочего времени
```python
FINNISH_WORK_SCHEDULE = {
    "standard_hours": {
        "daily": 7.5,           # Стандартный рабочий день
        "weekly": 37.5,         # Стандартная рабочая неделя
        "monthly": 162.5        # Примерно в месяц
    },
    
    "overtime": {
        "daily_limit": 8,       # Лимит обычного времени
        "weekly_limit": 40,     # Лимит обычного времени в неделю
        "overtime_multiplier": 1.5  # Коэффициент сверхурочных
    },
    
    "breaks": {
        "lunch": 30,            # Обеденный перерыв (минуты)
        "coffee": 15            # Кофе-брейк (минуты)
    }
}
```

## 🌡️ Сезонность и погода

### Учет сезонных особенностей
```python
SEASONAL_CONSIDERATIONS = {
    "winter_months": [11, 12, 1, 2, 3],
    "summer_months": [6, 7, 8],
    
    "winter_adjustments": {
        "heating_costs": True,
        "daylight_hours": "short",
        "outdoor_work_limitations": True,
        "equipment_winterization": True
    },
    
    "summer_adjustments": {
        "extended_daylight": True,
        "vacation_period": "July",
        "increased_productivity": True
    }
}
```

## 📱 Примеры интерфейса

### Сообщения с финляндской спецификой
```python
FINLAND_MESSAGES = {
    "welcome": {
        "title": "🇫🇮 Добро пожаловать в WorkLog Finland!",
        "description": "Система учёта рабочего времени для строительных компаний в Финляндии.",
        "currency_note": "Все расчёты ведутся в евро (€)"
    },
    
    "project_examples": {
        "create_hint": "Пример: 'Asuntokohde Kallio' или 'Toimistoremontti Espoo'",
        "address_hint": "Пример: 'Mannerheimintie 15, 00100 Helsinki'"
    },
    
    "rate_examples": {
        "hourly": "Пример: 32,50 € (средняя ставка в Финляндии)",
        "piece": "Пример: 45,00 € за м² (кладочные работы)"
    },
    
    "company_examples": {
        "name_hint": "Пример: 'Rakennus Virtanen Oy' или 'BuildMaster Helsinki Ab'"
    }
}
```

## 🏛️ Соответствие финскому законодательству

### Требования к учету рабочего времени
```python
FINNISH_LEGAL_REQUIREMENTS = {
    "time_tracking": {
        "mandatory_fields": [
            "start_time",
            "end_time", 
            "break_duration",
            "work_description",
            "location"
        ],
        "retention_period": "2_years",
        "overtime_tracking": "mandatory"
    },
    
    "tax_considerations": {
        "vat_rate": 0.24,           # НДС 24%
        "employer_costs": 0.22,     # Социальные взносы ~22%
        "currency": "EUR"
    },
    
    "worker_rights": {
        "max_daily_hours": 8,
        "max_weekly_hours": 40,
        "mandatory_breaks": True,
        "overtime_compensation": 1.5
    }
}
```
