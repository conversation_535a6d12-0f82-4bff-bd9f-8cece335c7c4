"""
CompanyService - сервис для управления компаниями

Создание, редактирование и управление компаниями.
"""
import logging
from typing import Optional, Dict, Any, List
from sqlalchemy import select, update
from sqlalchemy.orm import selectinload

from db.models import Company, UserCompanyRole, Project
from db.database import async_session

logger = logging.getLogger(__name__)


class CompanyService:
    """Сервис для управления компаниями"""
    
    @staticmethod
    async def create_company(
        name: str,
        business_id: Optional[str] = None,
        address: Optional[str] = None,
        created_by: Optional[int] = None
    ) -> Optional[Dict[str, Any]]:
        """
        Создание новой компании
        
        Args:
            name: Название компании
            business_id: Y-tunnus (финский бизнес ID)
            address: Ад<PERSON><PERSON><PERSON> компании
            
        Returns:
            ID созданной компании или None при ошибке
        """
        async with async_session() as session:
            try:
                # Проверяем уникальность названия
                existing = await session.execute(
                    select(Company).where(Company.name == name)
                )
                if existing.scalar_one_or_none():
                    return None
                
                company = Company(
                    name=name,
                    business_id=business_id,
                    address=address
                )
                session.add(company)
                await session.commit()
                await session.refresh(company)

                # Если указан создатель, добавляем его как директора компании
                if created_by:
                    user_role = UserCompanyRole(
                        user_id=created_by,
                        company_id=company.id,
                        role='director'
                    )
                    session.add(user_role)
                    await session.commit()

                logger.info(f"Создана компания: {name} (ID: {company.id})")
                return {
                    'id': company.id,
                    'name': company.name,
                    'business_id': company.business_id,
                    'address': company.address,
                    'created_at': company.created_at,
                    'is_deleted': company.is_deleted
                }

            except Exception as e:
                logger.error(f"Ошибка создания компании: {e}")
                await session.rollback()
                return None
    
    @staticmethod
    async def get_company(company_id: int) -> Optional[Dict[str, Any]]:
        """
        Получение компании по ID
        
        Args:
            company_id: ID компании
            
        Returns:
            Данные компании или None
        """
        async with async_session() as session:
            result = await session.execute(
                select(Company)
                .options(
                    selectinload(Company.user_roles).selectinload(UserCompanyRole.user),
                    selectinload(Company.projects)
                )
                .where(Company.id == company_id)
                .where(Company.is_deleted == False)
            )
            company = result.scalar_one_or_none()
            
            if not company:
                return None
            
            return {
                "id": company.id,
                "name": company.name,
                "business_id": company.business_id,
                "address": company.address,
                "created_at": company.created_at,
                "updated_at": company.updated_at,
                "users_count": len(company.user_roles),
                "projects_count": len([p for p in company.projects if not p.is_deleted])
            }
    
    @staticmethod
    async def get_all_companies(include_deleted: bool = False) -> List[Dict[str, Any]]:
        """
        Получение списка всех компаний
        
        Args:
            include_deleted: Включать ли удаленные компании
            
        Returns:
            Список компаний
        """
        async with async_session() as session:
            query = select(Company).options(
                selectinload(Company.user_roles),
                selectinload(Company.projects)
            )
            
            if not include_deleted:
                query = query.where(Company.is_deleted == False)
            
            result = await session.execute(query.order_by(Company.created_at.desc()))
            companies = result.scalars().all()
            
            return [
                {
                    "id": company.id,
                    "name": company.name,
                    "business_id": company.business_id,
                    "address": company.address,
                    "is_deleted": company.is_deleted,
                    "created_at": company.created_at,
                    "users_count": len(company.user_roles),
                    "projects_count": len([p for p in company.projects if not p.is_deleted])
                }
                for company in companies
            ]
    
    @staticmethod
    async def update_company(
        company_id: int,
        name: Optional[str] = None,
        business_id: Optional[str] = None,
        address: Optional[str] = None
    ) -> bool:
        """
        Обновление данных компании
        
        Args:
            company_id: ID компании
            name: Новое название
            business_id: Новый Y-tunnus
            address: Новый адрес
            
        Returns:
            True если обновление успешно
        """
        async with async_session() as session:
            try:
                update_data = {}
                if name is not None:
                    # Проверяем уникальность нового названия
                    existing = await session.execute(
                        select(Company)
                        .where(Company.name == name)
                        .where(Company.id != company_id)
                    )
                    if existing.scalar_one_or_none():
                        return False
                    update_data["name"] = name
                
                if business_id is not None:
                    update_data["business_id"] = business_id
                
                if address is not None:
                    update_data["address"] = address
                
                if not update_data:
                    return True
                
                result = await session.execute(
                    update(Company)
                    .where(Company.id == company_id)
                    .where(Company.is_deleted == False)
                    .values(**update_data)
                )
                
                if result.rowcount == 0:
                    return False
                
                await session.commit()
                return True
                
            except Exception:
                await session.rollback()
                return False
    
    @staticmethod
    async def soft_delete_company(company_id: int) -> bool:
        """
        Мягкое удаление компании
        
        Args:
            company_id: ID компании
            
        Returns:
            True если удаление успешно
        """
        async with async_session() as session:
            try:
                result = await session.execute(
                    update(Company)
                    .where(Company.id == company_id)
                    .where(Company.is_deleted == False)
                    .values(is_deleted=True)
                )
                
                if result.rowcount == 0:
                    return False
                
                # Также помечаем как удаленные все проекты компании
                await session.execute(
                    update(Project)
                    .where(Project.company_id == company_id)
                    .where(Project.is_deleted == False)
                    .values(is_deleted=True)
                )
                
                await session.commit()
                return True
                
            except Exception:
                await session.rollback()
                return False
    
    @staticmethod
    async def restore_company(company_id: int) -> bool:
        """
        Восстановление удаленной компании
        
        Args:
            company_id: ID компании
            
        Returns:
            True если восстановление успешно
        """
        async with async_session() as session:
            try:
                result = await session.execute(
                    update(Company)
                    .where(Company.id == company_id)
                    .where(Company.is_deleted == True)
                    .values(is_deleted=False)
                )
                
                if result.rowcount == 0:
                    return False
                
                await session.commit()
                return True
                
            except Exception:
                await session.rollback()
                return False
    
    @staticmethod
    async def get_company_users(company_id: int) -> List[Dict[str, Any]]:
        """
        Получение пользователей компании
        
        Args:
            company_id: ID компании
            
        Returns:
            Список пользователей компании
        """
        async with async_session() as session:
            result = await session.execute(
                select(UserCompanyRole)
                .options(selectinload(UserCompanyRole.user))
                .where(UserCompanyRole.company_id == company_id)
                .order_by(UserCompanyRole.created_at.desc())
            )
            user_roles = result.scalars().all()
            
            return [
                {
                    "user_id": role.user.user_id,
                    "display_name": role.user.display_name,
                    "role": role.role,
                    "permissions": role.permissions,
                    "joined_at": role.created_at
                }
                for role in user_roles
            ]

    @staticmethod
    async def get_companies_by_director(user_id: int) -> List[Dict[str, Any]]:
        """
        Получает компании директора

        Args:
            user_id: ID пользователя-директора

        Returns:
            Список компаний директора
        """
        async with async_session() as session:
            try:
                result = await session.execute(
                    select(Company)
                    .join(UserCompanyRole, Company.id == UserCompanyRole.company_id)
                    .where(UserCompanyRole.user_id == user_id)
                    .where(UserCompanyRole.role == 'director')
                    .where(Company.is_deleted == False)
                    .order_by(Company.name)
                )

                companies = result.scalars().all()

                return [
                    {
                        'id': company.id,
                        'name': company.name,
                        'business_id': company.business_id,
                        'address': company.address,
                        'is_deleted': company.is_deleted,
                        'is_active': False,  # Определяется через user.active_company_id
                        'created_at': company.created_at,
                        'updated_at': company.updated_at
                    }
                    for company in companies
                ]

            except Exception as e:
                logger.error(f"Ошибка при получении компаний директора {user_id}: {e}")
                return []

    @staticmethod
    async def get_user_companies(user_id: int) -> List[Dict[str, Any]]:
        """
        Получение компаний пользователя

        Args:
            user_id: ID пользователя

        Returns:
            Список компаний пользователя
        """
        async with async_session() as session:
            try:
                # Получаем компании через роли пользователя
                result = await session.execute(
                    select(Company, UserCompanyRole)
                    .join(UserCompanyRole, Company.id == UserCompanyRole.company_id)
                    .where(UserCompanyRole.user_id == user_id)
                    .where(Company.is_deleted == False)
                )

                companies_data = []
                for company, role in result.all():
                    companies_data.append({
                        'id': company.id,
                        'name': company.name,
                        'business_id': company.business_id,
                        'address': company.address,
                        'is_deleted': company.is_deleted,
                        'is_active': False,  # TODO: определить через active_company_id
                        'role': role.role,
                        'created_at': company.created_at,
                        'updated_at': company.updated_at
                    })

                return companies_data

            except Exception as e:
                logger.error(f"Ошибка при получении компаний пользователя {user_id}: {e}")
                return []

    @staticmethod
    async def get_company_by_name(user_id: int, name: str) -> Optional[Dict[str, Any]]:
        """
        Получение компании по названию для конкретного пользователя

        Args:
            user_id: ID пользователя
            name: Название компании

        Returns:
            Данные компании или None
        """
        async with async_session() as session:
            try:
                # Получаем компанию через роли пользователя
                result = await session.execute(
                    select(Company)
                    .join(UserCompanyRole, Company.id == UserCompanyRole.company_id)
                    .where(UserCompanyRole.user_id == user_id)
                    .where(Company.name == name)
                    .where(Company.is_deleted == False)
                )

                company = result.scalar_one_or_none()
                if not company:
                    return None

                return {
                    'id': company.id,
                    'name': company.name,
                    'business_id': company.business_id,
                    'address': company.address,
                    'is_deleted': company.is_deleted,
                    'created_at': company.created_at,
                    'updated_at': company.updated_at
                }

            except Exception as e:
                logger.error(f"Ошибка при поиске компании {name} для пользователя {user_id}: {e}")
                return None
