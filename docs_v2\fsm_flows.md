# 🔄 FSM СОСТОЯНИЯ И ПОТОКИ WORKLOG MVP v2.0

## 🎯 Общие принципы FSM

### Философия использования FSM
1. **Сложные операции** - многошаговые процессы с валидацией
2. **Сохранение состояния** - данные между шагами
3. **Возможность отмены** - выход из процесса на любом этапе
4. **Валидация на каждом шаге** - проверка корректности данных
5. **Понятная навигация** - пользователь всегда знает, где находится

### Когда использовать FSM
- ✅ **Создание записей** - ввод нескольких полей подряд
- ✅ **Редактирование** - изменение существующих данных
- ✅ **Мастера настройки** - пошаговая конфигурация
- ✅ **Сложные фильтры** - многоуровневая фильтрация
- ❌ **Простые действия** - одиночные операции
- ❌ **Навигация по меню** - переходы между экранами

## 🏗️ Архитектура FSM

### Базовая структура

```python
from aiogram.fsm.state import State, StatesGroup

class BaseStatesGroup(StatesGroup):
    """Базовый класс для групп состояний."""
    
    @classmethod
    def get_all_states(cls) -> List[State]:
        """Возвращает все состояния группы."""
        return [getattr(cls, attr) for attr in dir(cls) 
                if isinstance(getattr(cls, attr), State)]
    
    @classmethod
    def get_state_names(cls) -> List[str]:
        """Возвращает названия всех состояний."""
        return [state.state for state in cls.get_all_states()]
```

### Группы состояний по функциональности

```python
# Работа с записями о работе
class WorkEntryStates(BaseStatesGroup):
    selecting_project = State()      # Выбор проекта
    selecting_work_type = State()    # Выбор типа работы
    entering_description = State()   # Ввод описания
    entering_quantity = State()      # Ввод количества
    confirming_entry = State()       # Подтверждение

# Управление проектами
class ProjectStates(BaseStatesGroup):
    entering_name = State()          # Ввод названия
    entering_address = State()       # Ввод адреса
    setting_hourly_rate = State()    # Установка почасовой ставки
    setting_piece_rate = State()     # Установка сдельной ставки
    adding_work_types = State()      # Добавление типов работ
    confirming_project = State()     # Подтверждение создания

# Отчёты
class ReportStates(BaseStatesGroup):
    selecting_type = State()         # Выбор типа отчёта
    selecting_period = State()       # Выбор периода
    setting_filters = State()        # Настройка фильтров
    confirming_export = State()      # Подтверждение экспорта

# Заметки и запросы
class NotesStates(BaseStatesGroup):
    entering_note_text = State()     # Ввод текста заметки

class RequestStates(BaseStatesGroup):
    entering_request_text = State()  # Ввод текста запроса
    adding_comment = State()         # Добавление комментария (для директора)
```

## 👷 FSM потоки для рабочего

### Добавление записи о работе

```mermaid
stateDiagram-v2
    [*] --> SelectingProject: /addwork
    SelectingProject --> SelectingWorkType: Проект выбран
    SelectingWorkType --> EnteringDescription: Тип выбран
    EnteringDescription --> EnteringQuantity: Описание введено
    EnteringQuantity --> ConfirmingEntry: Количество введено
    ConfirmingEntry --> [*]: Подтверждено
    
    SelectingProject --> [*]: Отменено
    SelectingWorkType --> SelectingProject: Назад
    EnteringDescription --> SelectingWorkType: Назад
    EnteringQuantity --> EnteringDescription: Назад
    ConfirmingEntry --> EnteringQuantity: Редактировать
```

#### Детальная реализация

```python
class AddWorkFlow:
    """Поток добавления записи о работе."""
    
    @staticmethod
    @router.callback_query(F.data == "add:work")
    @require_role("ROLE_WORKER", "ROLE_DIRECTOR", "ROLE_ADMIN")
    async def start_add_work(
        callback: CallbackQuery,
        state: FSMContext,
        user: User,
        session: AsyncSession
    ):
        """Начинает процесс добавления записи о работе."""
        # Получаем доступные проекты
        project_service = ProjectService(session)
        projects = await project_service.get_projects_for_user(user)
        
        if not projects:
            await callback.answer(
                "❌ Нет доступных проектов",
                show_alert=True
            )
            return
        
        # Сохраняем данные в состоянии
        await state.update_data(
            user_id=user.id,
            date=date.today().isoformat()
        )
        
        # Устанавливаем состояние
        await state.set_state(WorkEntryStates.selecting_project)
        
        # Создаём клавиатуру с проектами
        keyboard = create_projects_keyboard(projects)
        
        await callback.message.edit_text(
            "🏗️ Выберите проект для записи о работе:",
            reply_markup=keyboard
        )
    
    @staticmethod
    @router.callback_query(
        WorkEntryStates.selecting_project,
        F.data.startswith("select:project:")
    )
    async def project_selected(
        callback: CallbackQuery,
        state: FSMContext,
        session: AsyncSession
    ):
        """Обрабатывает выбор проекта."""
        _, _, project_id = callback.data.split(":")
        project_id = int(project_id)
        
        # Получаем информацию о проекте
        project_service = ProjectService(session)
        project = await project_service.get_project_by_id(project_id)
        
        if not project:
            await callback.answer("❌ Проект не найден", show_alert=True)
            return
        
        # Сохраняем выбранный проект
        await state.update_data(
            project_id=project_id,
            project_name=project.name
        )
        
        # Переходим к выбору типа работы
        await state.set_state(WorkEntryStates.selecting_work_type)
        
        keyboard = create_work_type_keyboard()
        
        await callback.message.edit_text(
            f"⚙️ Выберите тип работы:\n\n"
            f"🏗️ Проект: {project.name}",
            reply_markup=keyboard
        )
    
    @staticmethod
    @router.callback_query(
        WorkEntryStates.selecting_work_type,
        F.data.startswith("select:work_type:")
    )
    async def work_type_selected(
        callback: CallbackQuery,
        state: FSMContext
    ):
        """Обрабатывает выбор типа работы."""
        _, _, work_type = callback.data.split(":")
        
        # Сохраняем тип работы
        await state.update_data(work_type=work_type)
        
        # Переходим к вводу описания
        await state.set_state(WorkEntryStates.entering_description)
        
        data = await state.get_data()
        work_type_text = "⏰ Почасовая работа" if work_type == "hourly" else "📦 Сдельная работа"
        
        keyboard = create_cancel_keyboard()
        
        await callback.message.edit_text(
            f"📝 Введите описание выполненной работы:\n\n"
            f"🏗️ Проект: {data['project_name']}\n"
            f"⚙️ Тип: {work_type_text}\n\n"
            f"Пример: \"Кладка кирпича на 2 этаже\"",
            reply_markup=keyboard
        )
    
    @staticmethod
    @router.message(WorkEntryStates.entering_description)
    async def description_entered(
        message: Message,
        state: FSMContext
    ):
        """Обрабатывает ввод описания."""
        description = message.text.strip()
        
        # Валидация описания
        if len(description) < 3:
            await message.answer(
                "❌ Описание должно содержать минимум 3 символа. "
                "Попробуйте ещё раз:"
            )
            return
        
        if len(description) > 500:
            await message.answer(
                "❌ Описание не может быть длиннее 500 символов. "
                "Попробуйте ещё раз:"
            )
            return
        
        # Сохраняем описание
        await state.update_data(description=description)
        
        # Переходим к вводу количества
        await state.set_state(WorkEntryStates.entering_quantity)
        
        data = await state.get_data()
        work_type_text = "⏰ Почасовая работа" if data['work_type'] == "hourly" else "📦 Сдельная работа"
        quantity_text = "часов" if data['work_type'] == "hourly" else "единиц"
        
        keyboard = create_back_cancel_keyboard("back:description")
        
        await message.answer(
            f"🔢 Введите количество {quantity_text}:\n\n"
            f"🏗️ Проект: {data['project_name']}\n"
            f"⚙️ Тип: {work_type_text}\n"
            f"📝 Описание: {description}\n\n"
            f"Введите число (например: 8 или 8.5):",
            reply_markup=keyboard
        )
        
        # Удаляем сообщение пользователя
        await message.delete()
    
    @staticmethod
    @router.message(WorkEntryStates.entering_quantity)
    async def quantity_entered(
        message: Message,
        state: FSMContext,
        session: AsyncSession
    ):
        """Обрабатывает ввод количества."""
        try:
            quantity = float(message.text.strip().replace(",", "."))
        except ValueError:
            await message.answer(
                "❌ Введите корректное число. Пример: 8 или 8.5"
            )
            return
        
        # Валидация количества
        if quantity <= 0:
            await message.answer(
                "❌ Количество должно быть больше 0"
            )
            return
        
        if quantity > 24:  # Максимум 24 часа в день
            await message.answer(
                "❌ Количество не может быть больше 24"
            )
            return
        
        # Сохраняем количество
        await state.update_data(quantity=quantity)
        
        # Получаем все данные для подтверждения
        data = await state.get_data()
        
        # Получаем ставку для расчёта суммы
        project_service = ProjectService(session)
        rate = await project_service.get_work_rate(
            data['project_id'],
            data['work_type']
        )
        
        total_amount = quantity * rate
        await state.update_data(
            rate=rate,
            total_amount=total_amount
        )
        
        # Переходим к подтверждению
        await state.set_state(WorkEntryStates.confirming_entry)
        
        work_type_text = "⏰ Почасовая работа" if data['work_type'] == "hourly" else "📦 Сдельная работа"
        quantity_text = "часов" if data['work_type'] == "hourly" else "единиц"
        
        keyboard = create_confirmation_keyboard(
            "confirm:work_entry",
            "edit:work_entry"
        )
        
        await message.answer(
            f"✅ Подтвердите запись о работе:\n\n"
            f"📅 Дата: {data['date']}\n"
            f"🏗️ Проект: {data['project_name']}\n"
            f"⚙️ Тип: {work_type_text} ({rate}₽/{quantity_text[:-2]})\n"
            f"📝 Описание: {data['description']}\n"
            f"🔢 Количество: {quantity} {quantity_text}\n"
            f"💰 Сумма: {total_amount:,.0f}₽",
            reply_markup=keyboard
        )
        
        # Удаляем сообщение пользователя
        await message.delete()
    
    @staticmethod
    @router.callback_query(
        WorkEntryStates.confirming_entry,
        F.data == "confirm:work_entry"
    )
    async def confirm_work_entry(
        callback: CallbackQuery,
        state: FSMContext,
        user: User,
        session: AsyncSession
    ):
        """Подтверждает и сохраняет запись о работе."""
        data = await state.get_data()
        
        # Создаём запись о работе
        work_service = WorkService(session)
        work_entry = await work_service.create_work_entry(
            user=user,
            project_id=data['project_id'],
            description=data['description'],
            quantity=data['quantity'],
            work_type=data['work_type'],
            rate=data['rate'],
            date=datetime.fromisoformat(data['date']).date()
        )
        
        # Очищаем состояние
        await state.clear()
        
        # Получаем статистику пользователя за сегодня
        today_stats = await work_service.get_user_daily_stats(
            user, 
            datetime.fromisoformat(data['date']).date()
        )
        
        keyboard = create_worker_menu_keyboard()
        
        await callback.message.edit_text(
            f"🎉 Запись о работе успешно добавлена!\n\n"
            f"📊 Ваша статистика за сегодня:\n"
            f"• Записей: {today_stats.entries_count}\n"
            f"• Часов: {today_stats.total_hours}\n"
            f"• Сумма: {today_stats.total_amount:,.0f}₽",
            reply_markup=keyboard
        )
```

### Редактирование записи о работе

```mermaid
stateDiagram-v2
    [*] --> SelectingEntry: /edit
    SelectingEntry --> SelectingField: Запись выбрана
    SelectingField --> EnteringNewValue: Поле выбрано
    EnteringNewValue --> ConfirmingChanges: Значение введено
    ConfirmingChanges --> [*]: Подтверждено
    
    SelectingField --> SelectingEntry: Назад
    EnteringNewValue --> SelectingField: Назад
    ConfirmingChanges --> EnteringNewValue: Редактировать
```

## 👨‍💼 FSM потоки для директора

### Создание проекта

```mermaid
stateDiagram-v2
    [*] --> EnteringName: /newproject
    EnteringName --> EnteringAddress: Название введено
    EnteringAddress --> SettingHourlyRate: Адрес введён
    SettingHourlyRate --> SettingPieceRate: Почасовая ставка установлена
    SettingPieceRate --> AddingWorkTypes: Сдельная ставка установлена
    AddingWorkTypes --> AddingWorkTypes: Добавить ещё тип
    AddingWorkTypes --> ConfirmingProject: Типы добавлены
    ConfirmingProject --> [*]: Подтверждено
    
    EnteringAddress --> EnteringName: Назад
    SettingHourlyRate --> EnteringAddress: Назад
    SettingPieceRate --> SettingHourlyRate: Назад
    AddingWorkTypes --> SettingPieceRate: Назад
    ConfirmingProject --> AddingWorkTypes: Редактировать
```

### Создание отчёта

```mermaid
stateDiagram-v2
    [*] --> SelectingType: /report
    SelectingType --> SelectingPeriod: Тип выбран
    SelectingPeriod --> SettingFilters: Период выбран
    SettingFilters --> ConfirmingExport: Фильтры установлены
    ConfirmingExport --> [*]: Экспорт подтверждён
    
    SelectingPeriod --> SelectingType: Назад
    SettingFilters --> SelectingPeriod: Назад
    ConfirmingExport --> SettingFilters: Изменить фильтры
```

## 🔄 Управление состояниями

### Middleware для FSM

```python
class FSMMiddleware(BaseMiddleware):
    """Middleware для управления FSM состояниями."""
    
    async def __call__(
        self,
        handler: Callable[[TelegramObject, Dict[str, Any]], Awaitable[Any]],
        event: TelegramObject,
        data: Dict[str, Any]
    ) -> Any:
        """Обрабатывает FSM состояния."""
        state: FSMContext = data.get("state")
        
        if state:
            # Логируем текущее состояние
            current_state = await state.get_state()
            if current_state:
                logger.debug(f"Current FSM state: {current_state}")
            
            # Добавляем данные состояния в контекст
            state_data = await state.get_data()
            data["state_data"] = state_data
        
        return await handler(event, data)
```

### Обработка отмены операций

```python
@router.callback_query(F.data == "cancel")
async def handle_cancel(callback: CallbackQuery, state: FSMContext):
    """Обрабатывает отмену текущей операции."""
    current_state = await state.get_state()
    
    if current_state:
        logger.info(f"Отменена операция в состоянии: {current_state}")
        await state.clear()
    
    # Возвращаем в главное меню
    keyboard = create_role_based_menu(callback.from_user.role)
    
    await callback.message.edit_text(
        "❌ Операция отменена\n\n🏠 Главное меню:",
        reply_markup=keyboard
    )
```

### Обработка кнопки "Назад"

```python
@router.callback_query(F.data.startswith("back:"))
async def handle_back(callback: CallbackQuery, state: FSMContext):
    """Обрабатывает возврат к предыдущему шагу."""
    _, target = callback.data.split(":", 1)
    current_state = await state.get_state()
    
    # Определяем предыдущее состояние на основе текущего
    previous_state = get_previous_state(current_state, target)
    
    if previous_state:
        await state.set_state(previous_state)
        # Восстанавливаем интерфейс для предыдущего состояния
        await restore_state_interface(callback, state, previous_state)
    else:
        # Если нет предыдущего состояния, возвращаем в меню
        await state.clear()
        keyboard = create_role_based_menu(callback.from_user.role)
        await callback.message.edit_text(
            "🏠 Главное меню:",
            reply_markup=keyboard
        )
```

## 📊 Мониторинг FSM

### Логирование переходов состояний

```python
async def log_state_transition(
    user_id: int,
    from_state: Optional[str],
    to_state: Optional[str],
    action: str
):
    """Логирует переходы между состояниями."""
    logger.info(
        "FSM transition: user_id=%s, from=%s, to=%s, action=%s",
        user_id, from_state, to_state, action
    )
```

### Очистка зависших состояний

```python
async def cleanup_stale_states():
    """Очищает зависшие состояния FSM."""
    # Реализация зависит от используемого storage
    # Например, для Redis можно удалять ключи старше определённого времени
    pass
```

## 🧪 Тестирование FSM

### Тестирование потоков

```python
class TestAddWorkFlow:
    """Тесты для потока добавления работы."""
    
    async def test_complete_flow_success(self, mock_callback, mock_state):
        """Тест успешного прохождения всего потока."""
        # Arrange
        user = create_test_user()
        projects = [create_test_project()]
        
        # Act & Assert - тестируем каждый шаг
        await AddWorkFlow.start_add_work(mock_callback, mock_state, user, session)
        assert await mock_state.get_state() == WorkEntryStates.selecting_project
        
        await AddWorkFlow.project_selected(mock_callback, mock_state, session)
        assert await mock_state.get_state() == WorkEntryStates.selecting_work_type
        
        # ... продолжаем тестирование каждого шага
    
    async def test_validation_errors(self, mock_message, mock_state):
        """Тест обработки ошибок валидации."""
        # Тестируем некорректные данные на каждом шаге
        pass
    
    async def test_cancel_flow(self, mock_callback, mock_state):
        """Тест отмены операции."""
        # Тестируем отмену на разных этапах
        pass
```
