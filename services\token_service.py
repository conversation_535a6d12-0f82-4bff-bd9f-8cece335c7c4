"""
TokenService - сервис для работы с токенами регистрации

Генерация, валидация и использование одноразовых токенов.
"""
import secrets
import hashlib
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
from sqlalchemy.orm import selectinload

from db.models import Token, Company, User, UserCompanyRole
from db.database import async_session


class TokenService:
    """Сервис для работы с токенами регистрации"""
    
    @staticmethod
    def generate_token() -> str:
        """Генерация безопасного токена"""
        return secrets.token_urlsafe(32)
    
    @staticmethod
    async def create_registration_token(
        role: str,
        company_id: int,
        created_by_user_id: int,
        expires_hours: int = 24
    ) -> str:
        """
        Создание токена регистрации
        
        Args:
            role: Роль для регистрации (director, worker)
            company_id: ID компании
            created_by_user_id: ID пользователя, создавшего токен
            expires_hours: Время жизни токена в часах
            
        Returns:
            Токен для регистрации
        """
        async with async_session() as session:
            token_value = TokenService.generate_token()
            expires_at = datetime.utcnow() + timedelta(hours=expires_hours)
            
            token = Token(
                token=token_value,
                role=role,
                company_id=company_id,
                created_by_user_id=created_by_user_id,
                expires_at=expires_at
            )
            
            session.add(token)
            await session.commit()
            
            return token_value
    
    @staticmethod
    async def validate_token(token_value: str) -> Optional[Dict[str, Any]]:
        """
        Валидация токена регистрации
        
        Args:
            token_value: Значение токена
            
        Returns:
            Данные токена или None если токен недействителен
        """
        async with async_session() as session:
            # Поиск токена с загрузкой связанной компании
            result = await session.execute(
                select(Token)
                .options(selectinload(Token.company))
                .where(Token.token == token_value)
                .where(Token.used_by_user_id.is_(None))  # Не использован
                .where(Token.expires_at > datetime.utcnow())  # Не истек
            )
            token = result.scalar_one_or_none()
            
            if not token:
                return None
            
            return {
                "token_id": token.id,
                "role": token.role,
                "company_id": token.company_id,
                "company_name": token.company.name,
                "created_by_user_id": token.created_by_user_id,
                "expires_at": token.expires_at
            }
    
    @staticmethod
    async def use_token(token_value: str, user_id: int) -> bool:
        """
        Использование токена для регистрации
        
        Args:
            token_value: Значение токена
            user_id: ID пользователя, использующего токен
            
        Returns:
            True если токен успешно использован
        """
        async with async_session() as session:
            # Обновляем токен как использованный
            result = await session.execute(
                update(Token)
                .where(Token.token == token_value)
                .where(Token.used_by_user_id.is_(None))
                .where(Token.expires_at > datetime.utcnow())
                .values(used_by_user_id=user_id)
            )
            
            if result.rowcount == 0:
                return False
            
            await session.commit()
            return True
    
    @staticmethod
    async def get_active_tokens(company_id: int) -> list[Dict[str, Any]]:
        """
        Получение активных токенов компании
        
        Args:
            company_id: ID компании
            
        Returns:
            Список активных токенов
        """
        async with async_session() as session:
            result = await session.execute(
                select(Token)
                .where(Token.company_id == company_id)
                .where(Token.used_by_user_id.is_(None))
                .where(Token.expires_at > datetime.utcnow())
                .order_by(Token.created_at.desc())
            )
            tokens = result.scalars().all()
            
            return [
                {
                    "id": token.id,
                    "token": token.token,
                    "role": token.role,
                    "created_at": token.created_at,
                    "expires_at": token.expires_at,
                    "registration_link": f"https://t.me/WorkLog_v3Bot?start=reg_{token.token}"
                }
                for token in tokens
            ]
    
    @staticmethod
    async def revoke_token(token_id: int) -> bool:
        """
        Отзыв токена (пометка как использованный)
        
        Args:
            token_id: ID токена
            
        Returns:
            True если токен успешно отозван
        """
        async with async_session() as session:
            result = await session.execute(
                update(Token)
                .where(Token.id == token_id)
                .where(Token.used_by_user_id.is_(None))
                .values(used_by_user_id=-1)  # Специальное значение для отозванных токенов
            )
            
            if result.rowcount == 0:
                return False
            
            await session.commit()
            return True
    
    @staticmethod
    async def cleanup_expired_tokens() -> int:
        """
        Очистка истекших токенов
        
        Returns:
            Количество удаленных токенов
        """
        async with async_session() as session:
            # Удаляем истекшие токены старше 7 дней
            cutoff_date = datetime.utcnow() - timedelta(days=7)
            
            result = await session.execute(
                select(Token)
                .where(Token.expires_at < cutoff_date)
            )
            expired_tokens = result.scalars().all()
            
            for token in expired_tokens:
                await session.delete(token)
            
            await session.commit()
            return len(expired_tokens)

    @staticmethod
    async def get_all_active_tokens() -> list[Dict[str, Any]]:
        """
        Получение всех активных токенов в системе

        Returns:
            Список всех активных токенов с информацией о компаниях
        """
        async with async_session() as session:
            result = await session.execute(
                select(Token, Company.name.label('company_name'))
                .join(Company, Token.company_id == Company.id)
                .where(Token.used_by_user_id.is_(None))
                .where(Token.expires_at > datetime.utcnow())
                .where(Company.is_deleted == False)
                .order_by(Token.created_at.desc())
            )

            tokens_data = []
            for row in result:
                token = row.Token
                tokens_data.append({
                    "id": token.id,
                    "token": token.token,
                    "role": token.role,
                    "company_id": token.company_id,
                    "company_name": row.company_name,
                    "created_at": token.created_at,
                    "expires_at": token.expires_at,
                    "registration_link": f"https://t.me/WorkLog_v3Bot?start=reg_{token.token}"
                })

            return tokens_data
