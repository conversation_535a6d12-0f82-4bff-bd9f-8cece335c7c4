"""
FSM состояния для Worklog Bot v2.0

Все состояния для различных сценариев работы бота.
"""
from aiogram.fsm.state import State, StatesGroup


class RoleSelectionStates(StatesGroup):
    """Состояния для выбора роли (режим разработки)"""
    waiting_role_selection = State()


class DevModeStates(StatesGroup):
    """Состояния для dev-режима суперпользователя"""
    selecting_role = State()
    role_selected = State()


class RegistrationStates(StatesGroup):
    """Состояния регистрации по токену"""
    waiting_display_name = State()


class AdminStates(StatesGroup):
    """Состояния для админских операций"""
    # Создание компании
    waiting_company_name = State()
    waiting_business_id = State()
    waiting_company_address = State()

    # Редактирование компании
    editing_company_name = State()
    editing_business_id = State()
    editing_company_address = State()

    # Управление пользователями
    waiting_user_search = State()

    # Создание токенов
    waiting_token_role = State()
    waiting_token_company = State()


# Состояния для рабочего (адаптировано из examples)
class WorkerAddWorkStates(StatesGroup):
    """Состояния добавления работы рабочим (7 шагов из examples)"""
    waiting_for_date_choice = State()      # Шаг 1: Сегодня/Ввести дату
    waiting_for_date_input = State()       # Шаг 2: Ввод даты ДД.ММ.ГГГГ
    waiting_for_work_type = State()        # Шаг 3: Выбор типа работы
    waiting_for_description = State()      # Шаг 4: Описание работы
    waiting_for_quantity = State()         # Шаг 5: Количество
    confirming_entry = State()             # Шаг 6: Подтверждение


class WorkerListStates(StatesGroup):
    """Состояния просмотра записей рабочим"""
    selecting_filter = State()             # Все/За период
    entering_date_range = State()          # Ввод диапазона дат


class WorkerProjectStates(StatesGroup):
    """Состояния управления проектами рабочим"""
    selecting_project = State()            # Выбор проекта
    creating_project_name = State()        # Создание: название
    creating_project_address = State()     # Создание: адрес
    choosing_copy_source = State()         # Копирование типов работ
    adding_work_type_name = State()        # Добавление типа: название
    adding_work_type_unit = State()        # Добавление типа: единица
    adding_work_type_rate_type = State()   # Добавление типа: тип ставки
    adding_work_type_value = State()       # Добавление типа: значение
    confirming_add_another = State()       # Добавить еще тип?


class WorkerEditStates(StatesGroup):
    """Состояния редактирования записей рабочим"""
    selecting_entry = State()             # Выбор записи
    selecting_field = State()             # Выбор поля
    entering_new_value = State()          # Ввод нового значения
    confirming_changes = State()          # Подтверждение изменений


# Совместимость со старыми состояниями
class AddWorkStates(StatesGroup):
    """Состояния добавления работы (совместимость)"""
    waiting_date = State()
    waiting_work_type = State()
    waiting_quantity = State()
    waiting_description = State()
    waiting_confirmation = State()


class EditWorkStates(StatesGroup):
    """Состояния редактирования работы"""
    waiting_entry_selection = State()
    waiting_field_selection = State()
    waiting_new_value = State()
    waiting_confirmation = State()


class ProjectStates(StatesGroup):
    """Состояния управления проектами"""
    waiting_project_name = State()
    waiting_project_address = State()
    waiting_confirmation = State()


class WorkTypeStates(StatesGroup):
    """Состояния управления типами работ"""
    waiting_work_type_name = State()
    waiting_unit = State()
    waiting_rate_type = State()
    waiting_value = State()
    waiting_confirmation = State()


class ReportStates(StatesGroup):
    """Состояния создания отчетов"""
    waiting_date_range = State()
    waiting_format_selection = State()





class DirectorStates(StatesGroup):
    """Состояния директора"""
    # Управление компаниями
    waiting_company_name = State()
    waiting_company_address = State()
    waiting_business_id = State()
    confirming_company_creation = State()
    selecting_company_to_switch = State()
    selecting_company_to_delete = State()
    confirming_company_deletion = State()

    # Управление рабочими
    waiting_worker_name_edit = State()
    confirming_worker_deletion = State()

    # Отчеты
    selecting_report_period = State()
    entering_report_dates = State()
    selecting_worker_for_report = State()
    selecting_project_for_report = State()

    # Управление проектами
    waiting_project_name = State()
    waiting_project_address = State()
    selecting_project_to_edit = State()
    editing_project_field = State()

    # Типы работ
    waiting_work_type_name = State()
    waiting_work_type_unit = State()
    waiting_work_type_rate = State()
    selecting_work_type_to_edit = State()
    editing_work_type_field = State()
    confirming_work_type_deletion = State()


class ExportStates(StatesGroup):
    """Состояния экспорта данных"""
    choosing_format = State()
    choosing_project = State()
    choosing_period = State()
    entering_date_range = State()
    choosing_columns = State()
    confirming_export = State()


class NewProjectStates(StatesGroup):
    """Состояния создания нового проекта"""
    creating_project_name = State()
    creating_project_address = State()
    choosing_copy_source = State()
    adding_work_type_name = State()
    adding_work_type_unit = State()
    adding_work_type_rate_type = State()
    adding_work_type_value = State()
    confirming_add_another_work_type = State()


class EditProjectStates(StatesGroup):
    """Состояния редактирования проекта"""
    selecting_project = State()
    choosing_action = State()
    editing_field = State()
    managing_work_types = State()
