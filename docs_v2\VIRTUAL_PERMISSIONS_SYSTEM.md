# 🧪 Система виртуальных прав

**Дата создания**: 29.06.2025  
**Статус**: ✅ РЕАЛИЗОВАНА И ФУНКЦИОНАЛЬНА

## 📋 Описание

Система виртуальных прав позволяет суперпользователю (ADMIN_ID=199737918) тестировать различные роли в боте без изменения реальных данных в базе данных.

## 🎯 Цель

- **Безопасное тестирование** всех функций бота
- **Изоляция от реальных данных** БД
- **Удобный интерфейс** выбора ролей
- **Полное логирование** действий в dev-режиме

## 🔧 Техническая реализация

### Файлы системы

1. **`utils/dev_session.py`** — управление виртуальными правами
2. **`keyboards/dev.py`** — интерфейс выбора ролей  
3. **`states.py`** — FSM состояния DevModeStates
4. **`middleware/rbac_middleware.py`** — приоритет виртуальных прав
5. **`handlers/common.py`** — обработчик /start с выбором роли

### Принцип работы

1. **Суперпользователь отправляет `/start`**
2. **Система предлагает выбор роли:**
   - 🧱 Рабочий
   - 📋 Директор  
   - 👑 Администратор
3. **Выбранная роль сохраняется в FSM**
4. **RBAC Middleware использует виртуальные права**
5. **Все действия логируются**

### Виртуальные данные

```python
MOCK_DATA = {
    "company_id": 999,      # Фиктивная компания
    "project_id": 9999,     # Фиктивный проект
    "worker_id": 99999      # Фиктивный рабочий
}
```

### Права ролей

```python
VIRTUAL_PERMISSIONS = {
    "admin": {
        # Все права администратора
        "can_manage_users": True,
        "can_manage_companies": True,
        "can_view_all_data": True,
        # ... полный список
    },
    "director": {
        # Права директора
        "can_manage_workers": True,
        "can_manage_projects": True,
        "can_view_reports": True,
        # ... полный список
    },
    "worker": {
        # Права рабочего
        "can_add_work_entries": True,
        "can_view_own_data": True
    }
}
```

## 🧪 Использование

### Активация dev-режима

1. Отправьте `/start` в бот
2. Выберите роль из меню
3. Подтвердите выбор
4. Система активирует виртуальные права

### Логирование

Все действия логируются:
```
🧪 Суперпользователь 199737918 использует виртуальную роль: director
```

### Безопасность

- **Только ADMIN_ID=199737918** имеет доступ
- **Полная изоляция** от реальных данных
- **Фиктивные ID** для всех операций
- **Логирование** всех действий

## ✅ Статус функций

### Роль "Администратор"
- ✅ **Все функции работают** полностью

### Роль "Директор"  
- ✅ **Меню работает** частично
- 🔄 **Требует отладки** отдельных функций
- ✅ **Система прав** функциональна

### Роль "Рабочий"
- ⚠️ **Не реализовано** (согласно этапу разработки)

## 🚀 Результат

**Система виртуальных прав полностью реализована** и позволяет безопасно тестировать все функции бота без риска повреждения реальных данных.

**Следующий этап**: Отладка функций директора и реализация функций рабочего.
