# ✅ ЧЕК-ЛИСТ ДЛЯ ИИ-АГЕНТОВ

## 🎯 НАЗНАЧЕНИЕ

Этот чек-лист обеспечивает качество и консистентность при добавлении новых команд, функций и изменений в проект Worklog Bot v2.0.

---

## 📋 ОБЩИЙ ЧЕК-ЛИСТ ПЕРЕД НАЧАЛОМ РАБОТЫ

### 📚 Изучение документации
- [ ] Прочитан `agent_prompt.md` — понимание правил работы
- [ ] Изучен `context_memory.md` — понимание текущего состояния
- [ ] Проверен `CHANGELOG.md` — понимание последних изменений
- [ ] Изучены финальные спецификации: `role_admin.md`, `role_director.md`, `role_worker.md`
- [ ] Проверен активный checkpoint в `checkpoint_system.md`
- [ ] **НОВОЕ**: Изучен `AUDIT_REPORT.md` — понимание проблем качества кода

### 🔍 Анализ задачи
- [ ] Задача соответствует финальным role_*.md спецификациям
- [ ] Понятны зависимости и требования
- [ ] Определены критерии готовности
- [ ] Оценено влияние на существующий код

---

## 🤖 ЧЕК-ЛИСТ: ДОБАВЛЕНИЕ НОВОЙ КОМАНДЫ

### 📋 Планирование
- [ ] Команда описана в соответствующем role_*.md документе
- [ ] Определена роль пользователя (admin/director/worker)
- [ ] Определены необходимые права доступа
- [ ] Спроектирован FSM сценарий (если нужен)
- [ ] Определены зависимости (активный проект, компания и т.д.)

### 💻 Реализация кода
- [ ] **КРИТИЧНО**: Код размещен в правильной структуре (`src/handlers/`)
- [ ] **КРИТИЧНО**: Используются базовые классы (BaseService, BaseDAO)
- [ ] Использован aiogram v3.x синтаксис (Router, новые импорты)
- [ ] Создан роутер для команды
- [ ] Добавлен декоратор `@require_permission`
- [ ] Реализованы все FSM состояния
- [ ] Добавлена обработка ошибок
- [ ] Добавлено логирование
- [ ] **НОВОЕ**: Добавлена валидация входных данных

### 🎨 Пользовательский интерфейс
- [ ] Используются только inline-клавиатуры
- [ ] **КРИТИЧНО**: Применена финляндская локализация (€, Oy/Ab/Tmi, 25-45 €/час)
- [ ] **КРИТИЧНО**: Используется символ € вместо рублей
- [ ] Добавлены эмодзи для улучшения UX
- [ ] Сообщения понятны и информативны
- [ ] Есть кнопка "Отмена" в FSM сценариях
- [ ] **НОВОЕ**: Клавиатуры созданы в `src/keyboards/`

### 🔐 Безопасность
- [ ] Проверены права доступа
- [ ] **КРИТИЧНО**: Валидированы входные данные (защита от SQL-инъекций)
- [ ] Добавлена изоляция по компаниям (если применимо)
- [ ] Обработаны edge cases
- [ ] **КРИТИЧНО**: Добавлено логирование критических операций
- [ ] **НОВОЕ**: Добавлен rate limiting (если нужно)
- [ ] **НОВОЕ**: Проверена защита от CSRF атак

### 📝 Документация
- [ ] Обновлен `CHANGELOG.md`
- [ ] Обновлен `context_memory.md`
- [ ] Добавлены комментарии в код
- [ ] Обновлена техническая документация (если нужно)

### 🧪 Тестирование
- [ ] **КРИТИЧНО**: Тесты используют моки вместо реальной БД
- [ ] **КРИТИЧНО**: Тесты размещены в правильной структуре (`tests/unit/`, `tests/integration/`)
- [ ] Написаны unit-тесты для новой функциональности
- [ ] Тесты покрывают позитивные и негативные сценарии
- [ ] Тесты проходят успешно (цель: 90%+ прохождения)
- [ ] Добавлены моки для внешних зависимостей
- [ ] Протестирована интеграция с существующими компонентами
- [ ] **НОВОЕ**: Созданы фикстуры для тестовых данных

---

## 🚨 ЧЕК-ЛИСТ: ИСПРАВЛЕНИЕ ПРОБЛЕМ АУДИТА

### 🏗️ Критические исправления структуры
- [ ] **КРИТИЧНО**: Создана папка `src/` и перемещены файлы
- [ ] **КРИТИЧНО**: Создана правильная структура тестов (`tests/unit/`, `tests/integration/`)
- [ ] **КРИТИЧНО**: Исправлен RBACMiddleware (добавлен user_id в data)
- [ ] **КРИТИЧНО**: Созданы базовые классы (BaseService, BaseDAO)

### 🌍 Финляндская локализация
- [ ] **КРИТИЧНО**: Заменены рубли на евро (€)
- [ ] **КРИТИЧНО**: Добавлены финские форматы компаний (Oy, Ab, Tmi)
- [ ] **КРИТИЧНО**: Установлены реалистичные ставки (25-45 €/час)
- [ ] Добавлены финские примеры компаний
- [ ] Добавлены финские форматы дат и чисел

### 🧪 Исправление тестов
- [ ] **КРИТИЧНО**: Исправлены 13 проваленных тестов
- [ ] **КРИТИЧНО**: Заменена реальная БД на моки
- [ ] Добавлены интеграционные тесты
- [ ] Созданы фикстуры для тестовых данных
- [ ] Достигнуто 90%+ прохождения тестов

### 🔧 Архитектурные улучшения
- [ ] Созданы отсутствующие модели (WorkEntry, WorkCategory и др.)
- [ ] Реализованы DAO слои для всех моделей
- [ ] Созданы отсутствующие сервисы (user, project, work, report)
- [ ] Добавлены утилиты (validators, formatters)
- [ ] Созданы inline-клавиатуры в `src/keyboards/`

---

## 🔧 ЧЕК-ЛИСТ: ДОБАВЛЕНИЕ НОВОЙ ФУНКЦИИ/СЕРВИСА

### 🏗️ Архитектура
- [ ] **КРИТИЧНО**: Функция размещена в правильной структуре (`src/`)
- [ ] **КРИТИЧНО**: Сервис наследуется от BaseService
- [ ] **КРИТИЧНО**: DAO наследуется от BaseDAO
- [ ] Функция соответствует общей архитектуре проекта
- [ ] Создан соответствующий сервис класс
- [ ] Добавлены необходимые DAO методы
- [ ] Обновлены модели данных (если нужно)
- [ ] Добавлены миграции БД (если нужно)
- [ ] **НОВОЕ**: Добавлены типы аргументов (type hints)

### 💾 База данных
- [ ] Используется только PostgreSQL
- [ ] Добавлены необходимые индексы
- [ ] Учтена производительность запросов
- [ ] Добавлены ограничения целостности
- [ ] Протестированы миграции

### 🧪 Тестирование
- [ ] Написаны unit тесты
- [ ] Протестированы edge cases
- [ ] Проверена производительность
- [ ] Протестирована интеграция с другими компонентами

---

## 🌍 ЧЕК-ЛИСТ: ФИНЛЯНДСКАЯ ЛОКАЛИЗАЦИЯ

### 💰 Валюта и форматы
- [ ] Используется символ € (евро)
- [ ] Формат: "1 234,56 €" (с пробелами и запятой)
- [ ] Даты в формате ДД.ММ.ГГГГ
- [ ] Европейский разделитель десятичных (запятая)

### 🏢 Названия и адреса
- [ ] Компании: "Название Oy/Ab/Tmi"
- [ ] Адреса: "Улица номер, почтовый_код Город"
- [ ] Проекты: финские названия типа "Asuntokohde Kallio"
- [ ] Реалистичные ставки для Финляндии (25-45 €/час)

### 📊 Данные
- [ ] Примеры данных соответствуют финским реалиям
- [ ] Учтены финские стандарты рабочего времени
- [ ] Добавлены финские праздники (если применимо)

---

## 🔄 ЧЕК-ЛИСТ: ОБНОВЛЕНИЕ СУЩЕСТВУЮЩЕГО КОДА

### 📖 Анализ изменений
- [ ] Изучен существующий код
- [ ] Понято влияние изменений
- [ ] Проверена совместимость с другими компонентами
- [ ] Определены риски

### 🔧 Реализация
- [ ] Сохранена обратная совместимость (если возможно)
- [ ] Обновлены зависимые компоненты
- [ ] Обновлены тесты
- [ ] Проверена производительность

### 📝 Документация
- [ ] Обновлена документация
- [ ] Добавлены migration notes (если нужно)
- [ ] Обновлены примеры использования

---

## 🚨 ЧЕК-ЛИСТ: КРИТИЧЕСКИЕ ПРОВЕРКИ

### ⚠️ Обязательные проверки
- [ ] **НЕ РЕДАКТИРОВАЛИСЬ** файлы: `role_admin.md`, `role_director.md`, `role_worker.md`
- [ ] Используется **только PostgreSQL** (никакого SQLite)
- [ ] Применен **aiogram v3.x** синтаксис (роутеры, новые импорты)
- [ ] Реализована **токен-система** регистрации
- [ ] Добавлена **гибридная система** для админа (если применимо)
- [ ] Применена **финляндская локализация**

### 🔍 Проверка качества
- [ ] Код соответствует стандартам проекта
- [ ] Нет дублирования кода
- [ ] Обработаны все возможные ошибки
- [ ] Добавлено достаточно логирования
- [ ] Код читаем и понятен

---

## 📊 ЧЕК-ЛИСТ: ЗАВЕРШЕНИЕ РАБОТЫ

### 📝 Документирование
- [ ] Обновлен `CHANGELOG.md` с описанием изменений
- [ ] Обновлен `context_memory.md` с текущим состоянием
- [ ] Обновлен статус checkpoint в `checkpoint_system.md`
- [ ] Добавлены комментарии к сложным участкам кода

### 🧪 Финальная проверка
- [ ] Код протестирован с гибридной системой ролей
- [ ] Проверена работа с финляндской локализацией
- [ ] Протестированы все FSM сценарии
- [ ] Проверена обработка ошибок

### 🚀 Готовность к передаче
- [ ] Код готов к использованию следующим агентом
- [ ] Документация актуальна
- [ ] Нет блокирующих проблем
- [ ] Следующие шаги понятны

---

## 🎯 СПЕЦИАЛЬНЫЕ ЧЕК-ЛИСТЫ

### 🔐 Для команд с RBAC
- [ ] Добавлен декоратор `@require_permission`
- [ ] Проверены права доступа в middleware
- [ ] Добавлена изоляция по компаниям
- [ ] Протестированы все роли

### 📊 Для команд с отчетами
- [ ] Добавлены фильтры по датам
- [ ] Реализована пагинация
- [ ] Добавлен экспорт в Excel/PDF
- [ ] Учтена производительность больших данных

### 🏗️ Для команд с проектами
- [ ] Проверена привязка к компании
- [ ] Добавлена поддержка типов работ
- [ ] Реализован soft delete
- [ ] Добавлена валидация данных

---

## 📋 ШАБЛОН ОТЧЕТА О ВЫПОЛНЕНИИ

```markdown
## ✅ ОТЧЕТ О ВЫПОЛНЕНИИ ЗАДАЧИ

**Задача**: [Название задачи]
**Дата**: [ДД.ММ.ГГГГ]
**Агент**: [Имя агента]

### Выполненные пункты чек-листа
- [x] Пункт 1
- [x] Пункт 2
- [ ] Пункт 3 (не применимо)

### Основные изменения
- Файл 1: описание изменений
- Файл 2: описание изменений

### Проблемы и решения
- Проблема 1: решение
- Проблема 2: решение

### Следующие шаги
- Шаг 1
- Шаг 2
```

---

**Помните**: Этот чек-лист — ваш помощник для обеспечения качества. Не пропускайте пункты!
