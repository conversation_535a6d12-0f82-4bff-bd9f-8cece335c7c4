"""
Общие клавиатуры для Worklog Bot.

Включает:
- create_main_menu() - адаптивное главное меню
- create_export_format_menu() - выбор формата экспорта
- create_export_period_menu() - выбор периода экспорта
- create_columns_keyboard() - выбор столбцов для экспорта
- yes_no_keyboard() - подтверждение действий

Особенности:
- Адаптивное меню в зависимости от прав пользователя
- Поддержка локализации через get_text()
- Inline клавиатуры для сложных сценариев
"""
from aiogram import types
from aiogram.types import ReplyKeyboardMarkup, KeyboardButton, InlineKeyboardMarkup, InlineKeyboardButton
from typing import List, Dict, Optional

from localization.texts import get_text


def create_main_menu(user_permissions: Dict[str, bool] = None) -> ReplyKeyboardMarkup:
    """
    Создает адаптивное главное меню в зависимости от прав пользователя.
    
    Args:
        user_permissions: Словарь с правами пользователя
            Ключи: is_admin, can_add_work, can_view_reports, can_manage_workers, etc.
    
    Returns:
        Клавиатура главного меню
    """
    if user_permissions is None:
        user_permissions = {}
    
    keyboard = ReplyKeyboardMarkup(resize_keyboard=True, row_width=2)
    
    # Кнопки для работы с записями (если есть права)
    if user_permissions.get("can_add_work", False):
        keyboard.add(
            KeyboardButton(get_text("menu_add_work")),
            KeyboardButton(get_text("menu_list"))
        )
        keyboard.add(
            KeyboardButton(get_text("menu_edit")),
            KeyboardButton(get_text("menu_delete"))
        )
    
    # Кнопки для отчетов и экспорта
    if user_permissions.get("can_view_reports", False):
        keyboard.add(
            KeyboardButton(get_text("menu_report")),
            KeyboardButton(get_text("menu_export"))
        )
    
    # Кнопки для управления проектами
    if user_permissions.get("can_manage_projects", False):
        keyboard.add(
            KeyboardButton(get_text("menu_set_project")),
            KeyboardButton(get_text("menu_new_project"))
        )
        keyboard.add(KeyboardButton(get_text("menu_edit_project")))
    
    # Кнопка управления (для директоров)
    if user_permissions.get("can_manage_workers", False):
        keyboard.add(KeyboardButton(get_text("menu_manage")))
    
    # Кнопка админ панели (для администраторов)
    if user_permissions.get("is_admin", False):
        keyboard.add(KeyboardButton(get_text("menu_admin")))
    
    # Если нет ролей - показываем только регистрацию
    if not any(user_permissions.values()):
        keyboard.add(KeyboardButton(get_text("menu_register")))
    
    # Кнопка помощи (всегда доступна)
    keyboard.add(KeyboardButton(get_text("menu_help")))
    
    return keyboard


def create_export_format_menu() -> ReplyKeyboardMarkup:
    """
    Создает клавиатуру выбора формата экспорта.
    
    Returns:
        Клавиатура с форматами экспорта
    """
    keyboard = ReplyKeyboardMarkup(resize_keyboard=True, one_time_keyboard=True)
    keyboard.add(
        KeyboardButton("Excel"),
        KeyboardButton("PDF")
    )
    keyboard.add(KeyboardButton(get_text("cancel")))
    return keyboard


def create_export_period_menu() -> ReplyKeyboardMarkup:
    """
    Создает клавиатуру выбора периода для экспорта.
    
    Returns:
        Клавиатура с периодами
    """
    keyboard = ReplyKeyboardMarkup(resize_keyboard=True, one_time_keyboard=True)
    keyboard.add(KeyboardButton("За всё время"))
    keyboard.add(
        KeyboardButton("За последнюю неделю"),
        KeyboardButton("За последний месяц")
    )
    keyboard.add(KeyboardButton("За период"))
    keyboard.add(KeyboardButton(get_text("cancel")))
    return keyboard


def create_columns_keyboard(selected_columns: List[str]) -> ReplyKeyboardMarkup:
    """
    Создает клавиатуру выбора столбцов для экспорта Excel.
    
    Args:
        selected_columns: Список уже выбранных столбцов
    
    Returns:
        Клавиатура с столбцами (с галочками для выбранных)
    """
    available_columns = [
        "Дата", "Тип работы", "Описание", 
        "Количество", "Ед. изм.", "Ставка", "Сумма"
    ]
    
    keyboard = ReplyKeyboardMarkup(resize_keyboard=True, row_width=2)
    
    # Добавляем столбцы с галочками для выбранных
    for column in available_columns:
        if column in selected_columns:
            button_text = f"✓ {column}"
        else:
            button_text = column
        keyboard.insert(KeyboardButton(button_text))
    
    # Кнопки управления
    keyboard.add(
        KeyboardButton(get_text("done")),
        KeyboardButton(get_text("cancel"))
    )
    
    return keyboard


def yes_no_keyboard() -> ReplyKeyboardMarkup:
    """
    Создает клавиатуру подтверждения (Да/Нет).
    
    Returns:
        Клавиатура подтверждения
    """
    keyboard = ReplyKeyboardMarkup(resize_keyboard=True, one_time_keyboard=True)
    keyboard.add(
        KeyboardButton(get_text("yes")),
        KeyboardButton(get_text("no"))
    )
    return keyboard


def create_cancel_keyboard() -> ReplyKeyboardMarkup:
    """
    Создает клавиатуру только с кнопкой отмены.
    
    Returns:
        Клавиатура с кнопкой отмены
    """
    keyboard = ReplyKeyboardMarkup(resize_keyboard=True)
    keyboard.add(KeyboardButton(get_text("cancel")))
    return keyboard


def create_date_choice_keyboard() -> ReplyKeyboardMarkup:
    """
    Создает клавиатуру выбора даты (сегодня/ввести дату).
    
    Returns:
        Клавиатура выбора даты
    """
    keyboard = ReplyKeyboardMarkup(resize_keyboard=True, one_time_keyboard=True)
    keyboard.add(
        KeyboardButton(get_text("today")),
        KeyboardButton(get_text("enter_date"))
    )
    keyboard.add(KeyboardButton(get_text("cancel")))
    return keyboard


def create_admin_menu() -> InlineKeyboardMarkup:
    """
    Создает inline клавиатуру админ панели.
    
    Returns:
        Inline клавиатура админ панели
    """
    keyboard = InlineKeyboardMarkup(row_width=2)
    
    keyboard.add(
        InlineKeyboardButton(
            "🏢 Управление компаниями",
            callback_data="admin_companies"
        )
    )
    keyboard.add(
        InlineKeyboardButton(
            "👥 Управление пользователями", 
            callback_data="admin_users"
        ),
        InlineKeyboardButton(
            "🔑 Создать токен директора",
            callback_data="admin_create_director_token"
        )
    )
    keyboard.add(
        InlineKeyboardButton(
            "📊 Статистика системы",
            callback_data="admin_stats"
        ),
        InlineKeyboardButton(
            "⚙️ Настройки",
            callback_data="admin_settings"
        )
    )
    keyboard.add(
        InlineKeyboardButton(
            "🔙 Назад в меню",
            callback_data="admin_back_to_menu"
        )
    )
    
    return keyboard


def create_companies_keyboard(companies: List[Dict]) -> InlineKeyboardMarkup:
    """
    Создает inline клавиатуру со списком компаний.
    
    Args:
        companies: Список компаний с полями id и name
    
    Returns:
        Inline клавиатура с компаниями
    """
    keyboard = InlineKeyboardMarkup(row_width=1)
    
    for company in companies:
        keyboard.add(
            InlineKeyboardButton(
                f"🏢 {company['name']}",
                callback_data=f"company_{company['id']}"
            )
        )
    
    keyboard.add(
        InlineKeyboardButton(
            "➕ Создать новую компанию",
            callback_data="create_new_company"
        )
    )
    keyboard.add(
        InlineKeyboardButton(
            "🔙 Назад",
            callback_data="admin_back"
        )
    )
    
    return keyboard


def create_company_management_keyboard(company_id: int) -> InlineKeyboardMarkup:
    """
    Создает inline клавиатуру управления конкретной компанией.
    
    Args:
        company_id: ID компании
    
    Returns:
        Inline клавиатуру управления компанией
    """
    keyboard = InlineKeyboardMarkup(row_width=2)
    
    keyboard.add(
        InlineKeyboardButton(
            "👥 Пользователи",
            callback_data=f"company_users_{company_id}"
        ),
        InlineKeyboardButton(
            "📁 Проекты", 
            callback_data=f"company_projects_{company_id}"
        )
    )
    keyboard.add(
        InlineKeyboardButton(
            "🔑 Создать токен рабочего",
            callback_data=f"create_worker_token_{company_id}"
        )
    )
    keyboard.add(
        InlineKeyboardButton(
            "📊 Статистика",
            callback_data=f"company_stats_{company_id}"
        ),
        InlineKeyboardButton(
            "⚙️ Настройки",
            callback_data=f"company_settings_{company_id}"
        )
    )
    keyboard.add(
        InlineKeyboardButton(
            "🔙 К списку компаний",
            callback_data="admin_companies"
        )
    )
    
    return keyboard


def create_director_menu() -> InlineKeyboardMarkup:
    """
    Создает inline клавиатуру панели директора.
    
    Returns:
        Inline клавиатура панели директора
    """
    keyboard = InlineKeyboardMarkup(row_width=2)
    
    keyboard.add(
        InlineKeyboardButton(
            "👥 Управление рабочими",
            callback_data="director_workers"
        )
    )
    keyboard.add(
        InlineKeyboardButton(
            "🔑 Создать токен рабочего",
            callback_data="director_create_token"
        ),
        InlineKeyboardButton(
            "📊 Статистика компании",
            callback_data="director_stats"
        )
    )
    keyboard.add(
        InlineKeyboardButton(
            "📁 Проекты компании",
            callback_data="director_projects"
        ),
        InlineKeyboardButton(
            "📋 Отчеты",
            callback_data="director_reports"
        )
    )
    keyboard.add(
        InlineKeyboardButton(
            "🔙 Назад в меню",
            callback_data="director_back_to_menu"
        )
    )
    
    return keyboard


def create_pagination_keyboard(
    current_page: int,
    total_pages: int,
    callback_prefix: str
) -> InlineKeyboardMarkup:
    """
    Создает клавиатуру пагинации.
    
    Args:
        current_page: Текущая страница (начиная с 1)
        total_pages: Общее количество страниц
        callback_prefix: Префикс для callback_data
    
    Returns:
        Inline клавиатура пагинации
    """
    keyboard = InlineKeyboardMarkup(row_width=3)
    
    buttons = []
    
    # Кнопка "Назад"
    if current_page > 1:
        buttons.append(
            InlineKeyboardButton(
                "⬅️ Назад",
                callback_data=f"{callback_prefix}_page_{current_page - 1}"
            )
        )
    
    # Информация о странице
    buttons.append(
        InlineKeyboardButton(
            f"{current_page}/{total_pages}",
            callback_data="pagination_info"
        )
    )
    
    # Кнопка "Вперед"
    if current_page < total_pages:
        buttons.append(
            InlineKeyboardButton(
                "Вперед ➡️",
                callback_data=f"{callback_prefix}_page_{current_page + 1}"
            )
        )
    
    if buttons:
        keyboard.add(*buttons)
    
    return keyboard


# Примеры использования:
"""
# Адаптивное главное меню
user_permissions = {
    "is_admin": False,
    "can_add_work": True,
    "can_view_reports": True,
    "can_manage_workers": False,
    "can_manage_projects": True
}
main_menu = create_main_menu(user_permissions)

# Клавиатура экспорта
export_format_menu = create_export_format_menu()
export_period_menu = create_export_period_menu()

# Выбор столбцов
selected_columns = ["Дата", "Тип работы", "Сумма"]
columns_keyboard = create_columns_keyboard(selected_columns)

# Подтверждение
confirmation = yes_no_keyboard()

# Админ панель
admin_menu = create_admin_menu()

# Список компаний
companies = [
    {"id": 1, "name": "ООО Компания 1"},
    {"id": 2, "name": "ООО Компания 2"}
]
companies_keyboard = create_companies_keyboard(companies)

# Пагинация
pagination = create_pagination_keyboard(
    current_page=2,
    total_pages=5,
    callback_prefix="entries_list"
)
"""
