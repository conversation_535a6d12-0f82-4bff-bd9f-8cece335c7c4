"""
Тесты для ReportService

Проверяет все методы сервиса генерации отчетов.
"""
import pytest
import asyncio
from datetime import datetime, date, timedelta
from unittest.mock import AsyncMock, patch

from services.report_service import ReportService


class TestReportService:
    """Тесты для ReportService"""

    @pytest.fixture
    def sample_work_entries(self):
        """Тестовые записи работ"""
        return [
            AsyncMock(
                id=1,
                user_id=1,
                project_id=1,
                work_type_id=1,
                work_date=date.today(),
                hours=8.0,
                hourly_rate=25.0,
                total_amount=200.0,
                description="Тестовая работа 1",
                user=AsyncMock(display_name="Иван Иванов"),
                project=AsyncMock(name="Проект А"),
                work_type=AsyncMock(name="Монта<PERSON>")
            ),
            AsyncMock(
                id=2,
                user_id=2,
                project_id=1,
                work_type_id=2,
                work_date=date.today(),
                hours=6.0,
                hourly_rate=30.0,
                total_amount=180.0,
                description="Тестовая работа 2",
                user=AsyncMock(display_name="Петр Петров"),
                project=AsyncMock(name="Проект А"),
                work_type=AsyncMock(name="Демонтаж")
            )
        ]

    @pytest.mark.asyncio
    async def test_get_date_report_success(self, sample_work_entries):
        """Тест успешной генерации отчета по датам"""
        with patch('services.report_service.async_session') as mock_session:
            mock_session_instance = AsyncMock()
            mock_session.return_value.__aenter__.return_value = mock_session_instance
            
            mock_session_instance.execute.return_value.scalars.return_value.all.return_value = sample_work_entries
            
            start_date = date.today()
            end_date = date.today()
            
            result = await ReportService.get_date_report(
                company_id=1,
                start_date=start_date,
                end_date=end_date
            )
            
            # Проверки
            assert 'period' in result
            assert 'summary' in result
            assert 'workers' in result
            assert 'projects' in result
            assert 'work_types' in result
            assert 'entries' in result
            
            # Проверка периода
            assert result['period']['start_date'] == start_date
            assert result['period']['end_date'] == end_date
            assert result['period']['days'] == 1
            
            # Проверка сводки
            assert result['summary']['total_entries'] == 2
            assert result['summary']['total_hours'] == 14.0
            assert result['summary']['total_amount'] == 380.0
            assert result['summary']['workers_count'] == 2
            assert result['summary']['projects_count'] == 1
            assert result['summary']['work_types_count'] == 2

    @pytest.mark.asyncio
    async def test_get_date_report_with_filters(self, sample_work_entries):
        """Тест генерации отчета по датам с фильтрами"""
        with patch('services.report_service.async_session') as mock_session:
            mock_session_instance = AsyncMock()
            mock_session.return_value.__aenter__.return_value = mock_session_instance
            
            # Фильтруем только одну запись
            filtered_entries = [sample_work_entries[0]]
            mock_session_instance.execute.return_value.scalars.return_value.all.return_value = filtered_entries
            
            result = await ReportService.get_date_report(
                company_id=1,
                start_date=date.today(),
                end_date=date.today(),
                worker_id=1,
                project_id=1
            )
            
            # Проверки
            assert result['summary']['total_entries'] == 1
            assert result['summary']['total_hours'] == 8.0
            assert result['summary']['total_amount'] == 200.0

    @pytest.mark.asyncio
    async def test_get_date_report_empty_result(self):
        """Тест генерации отчета без данных"""
        with patch('services.report_service.async_session') as mock_session:
            mock_session_instance = AsyncMock()
            mock_session.return_value.__aenter__.return_value = mock_session_instance
            
            mock_session_instance.execute.return_value.scalars.return_value.all.return_value = []
            
            result = await ReportService.get_date_report(
                company_id=1,
                start_date=date.today(),
                end_date=date.today()
            )
            
            # Проверки
            assert result['summary']['total_entries'] == 0
            assert result['summary']['total_hours'] == 0.0
            assert result['summary']['total_amount'] == 0.0
            assert len(result['entries']) == 0

    @pytest.mark.asyncio
    async def test_get_worker_report_success(self, sample_work_entries):
        """Тест успешной генерации отчета по рабочему"""
        with patch('services.report_service.async_session') as mock_session:
            mock_session_instance = AsyncMock()
            mock_session.return_value.__aenter__.return_value = mock_session_instance
            
            # Мок пользователя
            mock_user = AsyncMock(
                id=1,
                display_name="Иван Иванов",
                telegram_id=*********
            )
            
            # Настройка моков для двух запросов
            mock_session_instance.execute.side_effect = [
                AsyncMock(scalar_one_or_none=AsyncMock(return_value=mock_user)),  # Запрос пользователя
                AsyncMock(scalars=AsyncMock(return_value=AsyncMock(all=AsyncMock(return_value=[sample_work_entries[0]]))))  # Запрос записей
            ]
            
            result = await ReportService.get_worker_report(
                company_id=1,
                worker_id=1
            )
            
            # Проверки
            assert 'worker' in result
            assert 'period' in result
            assert 'summary' in result
            assert 'projects' in result
            assert 'work_types' in result
            assert 'entries' in result
            
            # Проверка данных рабочего
            assert result['worker']['id'] == 1
            assert result['worker']['name'] == "Иван Иванов"
            assert result['worker']['telegram_id'] == *********

    @pytest.mark.asyncio
    async def test_get_worker_report_user_not_found(self):
        """Тест генерации отчета для несуществующего рабочего"""
        with patch('services.report_service.async_session') as mock_session:
            mock_session_instance = AsyncMock()
            mock_session.return_value.__aenter__.return_value = mock_session_instance
            
            mock_session_instance.execute.return_value.scalar_one_or_none.return_value = None
            
            result = await ReportService.get_worker_report(
                company_id=1,
                worker_id=999
            )
            
            # Проверки
            assert 'error' in result
            assert result['error'] == 'Рабочий не найден'

    @pytest.mark.asyncio
    async def test_get_project_report_success(self, sample_work_entries):
        """Тест успешной генерации отчета по проекту"""
        with patch('services.report_service.async_session') as mock_session:
            mock_session_instance = AsyncMock()
            mock_session.return_value.__aenter__.return_value = mock_session_instance
            
            # Мок проекта
            mock_project = AsyncMock(
                id=1,
                name="Проект А",
                address="Адрес проекта",
                is_deleted=False
            )
            
            # Настройка моков для двух запросов
            mock_session_instance.execute.side_effect = [
                AsyncMock(scalar_one_or_none=AsyncMock(return_value=mock_project)),  # Запрос проекта
                AsyncMock(scalars=AsyncMock(return_value=AsyncMock(all=AsyncMock(return_value=sample_work_entries))))  # Запрос записей
            ]
            
            result = await ReportService.get_project_report(
                company_id=1,
                project_id=1
            )
            
            # Проверки
            assert 'project' in result
            assert 'period' in result
            assert 'summary' in result
            assert 'workers' in result
            assert 'work_types' in result
            assert 'daily_stats' in result
            assert 'entries' in result
            
            # Проверка данных проекта
            assert result['project']['id'] == 1
            assert result['project']['name'] == "Проект А"
            assert result['project']['status'] == "Активный"

    @pytest.mark.asyncio
    async def test_get_project_report_project_not_found(self):
        """Тест генерации отчета для несуществующего проекта"""
        with patch('services.report_service.async_session') as mock_session:
            mock_session_instance = AsyncMock()
            mock_session.return_value.__aenter__.return_value = mock_session_instance
            
            mock_session_instance.execute.return_value.scalar_one_or_none.return_value = None
            
            result = await ReportService.get_project_report(
                company_id=1,
                project_id=999
            )
            
            # Проверки
            assert 'error' in result
            assert result['error'] == 'Проект не найден'

    @pytest.mark.asyncio
    async def test_get_company_summary_success(self):
        """Тест получения сводки по компании"""
        with patch('services.report_service.async_session') as mock_session:
            mock_session_instance = AsyncMock()
            mock_session.return_value.__aenter__.return_value = mock_session_instance
            
            # Мок для разных запросов статистики
            mock_session_instance.execute.side_effect = [
                AsyncMock(first=AsyncMock(return_value=(10, 80.0, 2000.0))),  # entries, hours, amount
                AsyncMock(scalar=AsyncMock(return_value=5)),  # workers_count
                AsyncMock(scalar=AsyncMock(return_value=3)),  # projects_count
                AsyncMock(scalar=AsyncMock(return_value=4))   # work_types_count
            ]
            
            result = await ReportService.get_company_summary(company_id=1)
            
            # Проверки
            assert 'summary' in result
            assert result['summary']['total_entries'] == 10
            assert result['summary']['total_hours'] == 80.0
            assert result['summary']['total_amount'] == 2000.0
            assert result['summary']['workers_count'] == 5
            assert result['summary']['projects_count'] == 3
            assert result['summary']['work_types_count'] == 4

    @pytest.mark.asyncio
    async def test_get_date_report_exception_handling(self):
        """Тест обработки исключений в отчете по датам"""
        with patch('services.report_service.async_session') as mock_session:
            mock_session_instance = AsyncMock()
            mock_session.return_value.__aenter__.return_value = mock_session_instance
            
            # Мок исключения
            mock_session_instance.execute.side_effect = Exception("Database error")
            
            result = await ReportService.get_date_report(
                company_id=1,
                start_date=date.today(),
                end_date=date.today()
            )
            
            # Проверки - должен вернуться пустой отчет
            assert result['summary']['total_entries'] == 0
            assert result['summary']['total_hours'] == 0.0
            assert result['summary']['total_amount'] == 0.0

    @pytest.mark.asyncio
    async def test_get_worker_report_with_custom_dates(self, sample_work_entries):
        """Тест отчета по рабочему с кастомными датами"""
        with patch('services.report_service.async_session') as mock_session:
            mock_session_instance = AsyncMock()
            mock_session.return_value.__aenter__.return_value = mock_session_instance
            
            mock_user = AsyncMock(
                id=1,
                display_name="Иван Иванов",
                telegram_id=*********
            )
            
            mock_session_instance.execute.side_effect = [
                AsyncMock(scalar_one_or_none=AsyncMock(return_value=mock_user)),
                AsyncMock(scalars=AsyncMock(return_value=AsyncMock(all=AsyncMock(return_value=[sample_work_entries[0]]))))
            ]
            
            start_date = date.today() - timedelta(days=7)
            end_date = date.today()
            
            result = await ReportService.get_worker_report(
                company_id=1,
                worker_id=1,
                start_date=start_date,
                end_date=end_date
            )
            
            # Проверки
            assert result['period']['start_date'] == start_date
            assert result['period']['end_date'] == end_date
            assert result['period']['days'] == 8
            assert result['summary']['avg_hours_per_day'] == 1.0  # 8 часов / 8 дней
