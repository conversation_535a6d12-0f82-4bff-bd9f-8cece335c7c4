"""
AuthService - сервис для аутентификации и авторизации

Управление пользователями, ролями и правами доступа.
"""
import logging
from typing import Optional, Dict, Any, List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete
from sqlalchemy.orm import selectinload

from db.models import User, Company, UserCompanyRole, Project
from db.database import async_session

logger = logging.getLogger(__name__)


class AuthService:
    """Сервис для аутентификации и авторизации"""
    
    # Определение прав доступа для каждой роли
    ROLE_PERMISSIONS = {
        "admin": {
            "can_manage_users": True,
            "can_manage_companies": True,
            "can_view_all_data": True,
            "can_generate_tokens": True,
            "can_generate_worker_tokens": True,
            "can_delete_companies": True,
            "can_view_statistics": True
        },
        "director": {
            "can_manage_workers": True,
            "can_manage_projects": True,
            "can_manage_work_types": True,
            "can_view_company_reports": True,
            "can_export_company_data": True,
            "can_generate_worker_tokens": True,
            "can_edit_company_settings": True,
            # Дополнительные права для совместимости с декораторами
            "can_manage_companies": True,  # Для director_companies
            "can_view_reports": True,      # Для director_reports
            "can_export_data": True        # Для director_export
        },
        "worker": {
            # Основные права рабочего (CHECKPOINT 7)
            "can_add_work": True,
            "can_edit_own_work": True,
            "can_delete_own_work": True,
            "can_view_own_work": True,
            "can_create_own_reports": True,
            "can_export_own_data": True,
            "can_manage_own_projects": True,
            # Дополнительные права для совместимости с новыми обработчиками
            "can_view_own_reports": True,      # Для кнопки "📊 Мой отчёт"
            "can_edit_own_entries": True,      # Для редактирования записей
            "can_manage_projects": True,       # Для создания и выбора проектов
            "can_list_own_entries": True       # Для просмотра списка записей
        }
    }
    
    @staticmethod
    async def get_user(user_id: int) -> Optional[Dict[str, Any]]:
        """
        Получение пользователя по ID
        
        Args:
            user_id: Telegram ID пользователя
            
        Returns:
            Данные пользователя или None
        """
        async with async_session() as session:
            result = await session.execute(
                select(User)
                .options(
                    selectinload(User.active_company),
                    selectinload(User.active_project),
                    selectinload(User.company_roles).selectinload(UserCompanyRole.company)
                )
                .where(User.user_id == user_id)
            )
            user = result.scalar_one_or_none()
            
            if not user:
                return None
            
            return {
                "user_id": user.user_id,
                "display_name": user.display_name,
                "active_company_id": user.active_company_id,
                "active_company_name": user.active_company.name if user.active_company else None,
                "active_project_id": user.active_project_id,
                "active_project_name": user.active_project.name if user.active_project else None,
                "roles": [
                    {
                        "company_id": role.company_id,
                        "company_name": role.company.name,
                        "role": role.role,
                        "permissions": role.permissions
                    }
                    for role in user.company_roles
                ],
                "created_at": user.created_at
            }
    
    @staticmethod
    async def create_user(
        user_id: int,
        display_name: str,
        role: str,
        company_id: int,
        permissions: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Создание нового пользователя
        
        Args:
            user_id: Telegram ID пользователя
            display_name: Отображаемое имя
            role: Роль пользователя
            company_id: ID компании
            permissions: Дополнительные права (если не указаны, используются стандартные)
            
        Returns:
            True если пользователь создан успешно
        """
        async with async_session() as session:
            try:
                # Проверяем, не существует ли уже пользователь
                existing_user = await session.execute(
                    select(User).where(User.user_id == user_id)
                )
                if existing_user.scalar_one_or_none():
                    return False
                
                # Создаем пользователя
                user = User(
                    user_id=user_id,
                    display_name=display_name,
                    active_company_id=company_id
                )
                session.add(user)
                
                # Создаем роль пользователя в компании
                user_permissions = permissions or AuthService.ROLE_PERMISSIONS.get(role, {})
                user_role = UserCompanyRole(
                    user_id=user_id,
                    company_id=company_id,
                    role=role,
                    permissions=user_permissions
                )
                session.add(user_role)
                
                await session.commit()
                return True
                
            except Exception:
                await session.rollback()
                return False
    
    @staticmethod
    async def get_user_permissions(user_id: int, company_id: Optional[int] = None) -> Dict[str, bool]:
        """
        Получение прав доступа пользователя
        
        Args:
            user_id: Telegram ID пользователя
            company_id: ID компании (если не указан, используется активная компания)
            
        Returns:
            Словарь с правами доступа
        """
        async with async_session() as session:
            # Получаем пользователя
            user_result = await session.execute(
                select(User).where(User.user_id == user_id)
            )
            user = user_result.scalar_one_or_none()
            
            if not user:
                return {}
            
            # Определяем компанию для проверки прав
            target_company_id = company_id or user.active_company_id
            if not target_company_id:
                return {}
            
            # Получаем роль пользователя в компании
            role_result = await session.execute(
                select(UserCompanyRole)
                .where(UserCompanyRole.user_id == user_id)
                .where(UserCompanyRole.company_id == target_company_id)
            )
            user_role = role_result.scalar_one_or_none()
            
            if not user_role:
                return {}

            # Возвращаем права из ROLE_PERMISSIONS по роли
            role_name = user_role.role
            return AuthService.ROLE_PERMISSIONS.get(role_name, {})
    
    @staticmethod
    async def has_permission(user_id: int, permission: str, company_id: Optional[int] = None) -> bool:
        """
        Проверка наличия конкретного права у пользователя
        
        Args:
            user_id: Telegram ID пользователя
            permission: Название права
            company_id: ID компании
            
        Returns:
            True если право есть
        """
        permissions = await AuthService.get_user_permissions(user_id, company_id)
        return permissions.get(permission, False)
    
    @staticmethod
    async def get_user_role(user_id: int, company_id: Optional[int] = None) -> Optional[str]:
        """
        Получение роли пользователя в компании
        
        Args:
            user_id: Telegram ID пользователя
            company_id: ID компании
            
        Returns:
            Роль пользователя или None
        """
        async with async_session() as session:
            user_result = await session.execute(
                select(User).where(User.user_id == user_id)
            )
            user = user_result.scalar_one_or_none()
            
            if not user:
                return None
            
            target_company_id = company_id or user.active_company_id
            if not target_company_id:
                return None
            
            role_result = await session.execute(
                select(UserCompanyRole.role)
                .where(UserCompanyRole.user_id == user_id)
                .where(UserCompanyRole.company_id == target_company_id)
            )
            role = role_result.scalar_one_or_none()
            
            return role

    @staticmethod
    async def get_all_users_with_companies():
        """Получить всех пользователей с их компаниями"""
        async with async_session() as session:
            # Получаем всех пользователей с их ролями и компаниями
            result = await session.execute(
                select(
                    User.user_id,
                    User.display_name,
                    UserCompanyRole.role,
                    Company.id.label('company_id'),
                    Company.name.label('company_name')
                )
                .join(UserCompanyRole, User.user_id == UserCompanyRole.user_id)
                .join(Company, UserCompanyRole.company_id == Company.id)
                .where(Company.is_deleted == False)
                .order_by(UserCompanyRole.role, User.display_name)
            )

            users_data = {}
            for row in result:
                user_id = row.user_id
                if user_id not in users_data:
                    users_data[user_id] = {
                        'user_id': user_id,
                        'display_name': row.display_name,
                        'role': row.role,
                        'companies': []
                    }

                users_data[user_id]['companies'].append({
                    'id': row.company_id,
                    'name': row.company_name
                })

            return list(users_data.values())

    @staticmethod
    async def set_active_company(user_id: int, company_id: int) -> bool:
        """
        Установка активной компании для пользователя
        
        Args:
            user_id: Telegram ID пользователя
            company_id: ID компании
            
        Returns:
            True если компания установлена успешно
        """
        async with async_session() as session:
            # Проверяем, есть ли у пользователя роль в этой компании
            role_result = await session.execute(
                select(UserCompanyRole)
                .where(UserCompanyRole.user_id == user_id)
                .where(UserCompanyRole.company_id == company_id)
            )
            if not role_result.scalar_one_or_none():
                return False
            
            # Обновляем активную компанию
            result = await session.execute(
                update(User)
                .where(User.user_id == user_id)
                .values(active_company_id=company_id, active_project_id=None)
            )
            
            if result.rowcount == 0:
                return False
            
            await session.commit()
            return True
    
    @staticmethod
    async def set_active_project(user_id: int, project_id: int) -> bool:
        """
        Установка активного проекта для пользователя
        
        Args:
            user_id: Telegram ID пользователя
            project_id: ID проекта
            
        Returns:
            True если проект установлен успешно
        """
        async with async_session() as session:
            # Проверяем, принадлежит ли проект активной компании пользователя
            user_result = await session.execute(
                select(User).where(User.user_id == user_id)
            )
            user = user_result.scalar_one_or_none()
            
            if not user or not user.active_company_id:
                return False
            
            project_result = await session.execute(
                select(Project)
                .where(Project.project_id == project_id)
                .where(Project.company_id == user.active_company_id)
                .where(Project.is_deleted == False)
            )
            if not project_result.scalar_one_or_none():
                return False
            
            # Обновляем активный проект
            result = await session.execute(
                update(User)
                .where(User.user_id == user_id)
                .values(active_project_id=project_id)
            )
            
            if result.rowcount == 0:
                return False

            await session.commit()
            return True

    @staticmethod
    async def get_company_workers(company_id: int) -> List[Dict[str, Any]]:
        """
        Получает список рабочих компании

        Args:
            company_id: ID компании

        Returns:
            Список рабочих компании
        """
        async with async_session() as session:
            try:
                result = await session.execute(
                    select(User, UserCompanyRole.role)
                    .join(UserCompanyRole, User.user_id == UserCompanyRole.user_id)
                    .where(UserCompanyRole.company_id == company_id)
                    .where(UserCompanyRole.role == 'worker')
                    .order_by(User.display_name)
                )

                workers = []
                for user, role in result.all():
                    workers.append({
                        'user_id': user.user_id,
                        'display_name': user.display_name,
                        'role': role,
                        'active_company_id': user.active_company_id,
                        'active_project_id': user.active_project_id,
                        'created_at': user.created_at
                    })

                return workers

            except Exception as e:
                logger.error(f"Ошибка при получении рабочих компании {company_id}: {e}")
                return []

    @staticmethod
    async def add_user_to_company(user_id: int, company_id: int, role: str, permissions: Dict[str, Any] = None) -> bool:
        """
        Добавляет пользователя к компании с указанной ролью

        Args:
            user_id: Telegram ID пользователя
            company_id: ID компании
            role: Роль пользователя ('director', 'worker', 'admin')
            permissions: Дополнительные права доступа

        Returns:
            True если пользователь успешно добавлен
        """
        async with async_session() as session:
            try:
                # Проверяем, не существует ли уже такая связь
                existing = await session.execute(
                    select(UserCompanyRole)
                    .where(UserCompanyRole.user_id == user_id)
                    .where(UserCompanyRole.company_id == company_id)
                )
                if existing.scalar_one_or_none():
                    return False

                # Создаем новую роль
                user_role = UserCompanyRole(
                    user_id=user_id,
                    company_id=company_id,
                    role=role,
                    permissions=permissions or {}
                )
                session.add(user_role)
                await session.commit()

                return True

            except Exception as e:
                logger.error(f"Ошибка при добавлении пользователя {user_id} к компании {company_id}: {e}")
                await session.rollback()
                return False

    @staticmethod
    async def update_user_active_company(user_id: int, company_id: int) -> bool:
        """
        Обновляет активную компанию пользователя

        Args:
            user_id: Telegram ID пользователя
            company_id: ID компании

        Returns:
            True если обновление успешно
        """
        async with async_session() as session:
            try:
                result = await session.execute(
                    update(User)
                    .where(User.user_id == user_id)
                    .values(active_company_id=company_id)
                )

                if result.rowcount == 0:
                    return False

                await session.commit()
                logger.info(f"Обновлена активная компания {company_id} для пользователя {user_id}")
                return True

            except Exception as e:
                logger.error(f"Ошибка при обновлении активной компании {company_id} для пользователя {user_id}: {e}")
                await session.rollback()
                return False

    @staticmethod
    async def update_user_active_project(user_id: int, project_id: int) -> bool:
        """
        Обновляет активный проект пользователя

        Args:
            user_id: Telegram ID пользователя
            project_id: ID проекта

        Returns:
            True если обновление успешно
        """
        async with async_session() as session:
            try:
                result = await session.execute(
                    update(User)
                    .where(User.user_id == user_id)
                    .values(active_project_id=project_id)
                )

                if result.rowcount == 0:
                    return False

                await session.commit()
                logger.info(f"Обновлен активный проект {project_id} для пользователя {user_id}")
                return True

            except Exception as e:
                logger.error(f"Ошибка при обновлении активного проекта {project_id} для пользователя {user_id}: {e}")
                await session.rollback()
                return False

    @staticmethod
    async def get_user_companies(user_id: int) -> List[Dict[str, Any]]:
        """
        Получает все компании пользователя с ролями

        Args:
            user_id: Telegram ID пользователя

        Returns:
            Список компаний с ролями пользователя
        """
        async with async_session() as session:
            try:
                result = await session.execute(
                    select(UserCompanyRole, Company)
                    .join(Company, UserCompanyRole.company_id == Company.id)
                    .where(UserCompanyRole.user_id == user_id)
                    .where(Company.is_deleted == False)
                    .order_by(Company.name)
                )

                companies = []
                for user_role, company in result:
                    companies.append({
                        'company_id': company.id,
                        'company_name': company.name,
                        'role': user_role.role,
                        'created_at': user_role.created_at
                    })

                return companies

            except Exception as e:
                logger.error(f"Ошибка при получении компаний пользователя {user_id}: {e}")
                return []
