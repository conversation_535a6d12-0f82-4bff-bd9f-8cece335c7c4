"""
Мониторинг тестирования системы в реальном времени

Показывает текущее состояние БД и последние операции
"""
import asyncio
import asyncpg
import os
from dotenv import load_dotenv
from datetime import datetime

async def monitor_testing():
    """Мониторинг состояния БД во время тестирования"""
    load_dotenv()
    database_url = os.getenv("DATABASE_URL")
    
    if not database_url:
        print("❌ DATABASE_URL не найден в .env файле")
        return
    
    # Преобразуем URL для asyncpg
    if "+asyncpg" in database_url:
        database_url = database_url.replace("postgresql+asyncpg://", "postgresql://")
    
    try:
        conn = await asyncpg.connect(database_url)
        print("✅ Подключение к PostgreSQL установлено")
        
        print("\n🔍 МОНИТОРИНГ ТЕСТИРОВАНИЯ")
        print("=" * 60)
        print(f"⏰ Время: {datetime.now().strftime('%H:%M:%S')}")
        
        # Проверяем компании
        companies = await conn.fetch("""
            SELECT id, name, business_id, address, created_at
            FROM companies 
            WHERE is_deleted = false 
            ORDER BY created_at DESC
        """)
        
        print(f"\n🏢 КОМПАНИИ ({len(companies)}):")
        for company in companies:
            print(f"  📋 ID {company['id']}: {company['name']}")
            print(f"      Y-tunnus: {company['business_id'] or 'Не указан'}")
            print(f"      Адрес: {company['address'] or 'Не указан'}")
            print(f"      Создана: {company['created_at'].strftime('%H:%M:%S')}")
        
        # Проверяем пользователей
        users = await conn.fetch("""
            SELECT user_id, display_name, active_company_id, created_at
            FROM users 
            ORDER BY created_at DESC
        """)
        
        print(f"\n👤 ПОЛЬЗОВАТЕЛИ ({len(users)}):")
        for user in users:
            print(f"  👤 ID {user['user_id']}: {user['display_name'] or 'Без имени'}")
            print(f"      Активная компания: {user['active_company_id'] or 'Не выбрана'}")
            print(f"      Зарегистрирован: {user['created_at'].strftime('%H:%M:%S')}")
        
        # Проверяем роли
        roles = await conn.fetch("""
            SELECT ucr.user_id, ucr.company_id, ucr.role, ucr.permissions, ucr.created_at,
                   c.name as company_name
            FROM user_company_roles ucr
            JOIN companies c ON c.id = ucr.company_id
            ORDER BY ucr.created_at DESC
        """)
        
        print(f"\n🔗 РОЛИ ПОЛЬЗОВАТЕЛЕЙ ({len(roles)}):")
        for role in roles:
            print(f"  🎭 Пользователь {role['user_id']} → {role['role']} в {role['company_name']}")
            print(f"      Права: {role['permissions'] or '{}'}")
            print(f"      Назначена: {role['created_at'].strftime('%H:%M:%S')}")
        
        # Проверяем токены
        tokens = await conn.fetch("""
            SELECT id, token, role, company_id, created_by_user_id, used_by_user_id, 
                   expires_at, is_used, created_at
            FROM tokens 
            ORDER BY created_at DESC
            LIMIT 5
        """)
        
        print(f"\n🔑 ТОКЕНЫ (последние 5):")
        for token in tokens:
            status = "✅ Использован" if token['used_by_user_id'] else "⏳ Активен"
            expires = token['expires_at'].strftime('%H:%M:%S')
            
            print(f"  🔑 {token['token'][:20]}...")
            print(f"      Роль: {token['role']} | Компания: {token['company_id'] or 'Любая'}")
            print(f"      Создатель: {token['created_by_user_id']} | {status}")
            print(f"      Истекает: {expires}")
        
        # Проверяем проекты
        projects = await conn.fetch("""
            SELECT project_id, name, address, company_id, created_by, created_at
            FROM projects 
            WHERE is_deleted = false
            ORDER BY created_at DESC
        """)
        
        print(f"\n🏗️ ПРОЕКТЫ ({len(projects)}):")
        for project in projects:
            print(f"  🏗️ ID {project['project_id']}: {project['name']}")
            print(f"      Компания: {project['company_id']} | Создатель: {project['created_by']}")
            print(f"      Адрес: {project['address'] or 'Не указан'}")
        
        # Проверяем записи о работе
        entries = await conn.fetch("""
            SELECT entry_id, user_id, project_id, date, description, 
                   quantity, calculated_amount, created_at
            FROM work_entries 
            ORDER BY created_at DESC
            LIMIT 3
        """)
        
        print(f"\n📝 ЗАПИСИ О РАБОТЕ (последние 3):")
        for entry in entries:
            print(f"  📝 ID {entry['entry_id']}: {entry['description'][:50]}...")
            print(f"      Пользователь: {entry['user_id']} | Проект: {entry['project_id']}")
            print(f"      Количество: {entry['quantity']} | Сумма: €{entry['calculated_amount']}")
            print(f"      Дата работы: {entry['date']}")
        
        await conn.close()
        
        print(f"\n📊 СТАТИСТИКА:")
        print(f"  🏢 Компаний: {len(companies)}")
        print(f"  👤 Пользователей: {len(users)}")
        print(f"  🔗 Ролей: {len(roles)}")
        print(f"  🔑 Токенов: {len(tokens)}")
        print(f"  🏗️ Проектов: {len(projects)}")
        print(f"  📝 Записей: {len(entries)}")
        
        print(f"\n🎯 ГОТОВНОСТЬ СИСТЕМЫ:")
        if len(companies) > 0:
            print("  ✅ Компании создаются")
        if len(tokens) > 0:
            print("  ✅ Токены генерируются")
        if len(users) > 0:
            print("  ✅ Пользователи регистрируются")
        if len(roles) > 0:
            print("  ✅ RBAC система работает")
        if len(projects) > 0:
            print("  ✅ Проекты создаются")
        if len(entries) > 0:
            print("  ✅ Записи о работе ведутся")
        
        print(f"\n🎉 МОНИТОРИНГ ЗАВЕРШЕН!")
        
    except Exception as e:
        print(f"❌ Ошибка при мониторинге: {e}")

if __name__ == "__main__":
    asyncio.run(monitor_testing())
