"""
Reply-клавиатуры для директора.

Включает:
- create_director_menu() - главное меню директора
- create_workers_keyboard() - управление рабочими
- create_projects_keyboard() - управление проектами
- create_reports_keyboard() - отчеты
"""
from aiogram.types import ReplyKeyboardMarkup, KeyboardButton
from typing import List, Dict


def create_director_menu() -> ReplyKeyboardMarkup:
    """Создает главное меню директора"""
    return ReplyKeyboardMarkup(
        keyboard=[
            [
                KeyboardButton(text="🏢 Управление компаниями")
            ],
            [
                KeyboardButton(text="👷 Управление рабочими")
            ],
            [
                KeyboardButton(text="📊 Просмотр отчётов")
            ],
            [
                KeyboardButton(text="📁 Экспорт / Импорт данных")
            ],
            [
                KeyboardButton(text="🛠️ Редактирование типов работ")
            ],
            [
                KeyboardButton(text="ℹ️ Информация")
            ],
            [
                KeyboardButton(text="🔙 Назад в меню")
            ]
        ],
        resize_keyboard=True,
        input_field_placeholder="Выберите действие директора"
    )


def create_companies_management_keyboard() -> ReplyKeyboardMarkup:
    """Создает клавиатуру управления компаниями"""
    return ReplyKeyboardMarkup(
        keyboard=[
            [
                KeyboardButton(text="➕ Добавить компанию")
            ],
            [
                KeyboardButton(text="🔁 Сменить активную компанию")
            ],
            [
                KeyboardButton(text="🗑️ Удалить компанию (soft delete)")
            ],
            [
                KeyboardButton(text="📋 Список компаний")
            ],
            [
                KeyboardButton(text="🔙 Назад")
            ]
        ],
        resize_keyboard=True,
        input_field_placeholder="Управление компаниями"
    )


def create_companies_keyboard() -> ReplyKeyboardMarkup:
    """Создает клавиатуру управления компаниями"""
    return ReplyKeyboardMarkup(
        keyboard=[
            [
                KeyboardButton(text="📋 Мои компании"),
                KeyboardButton(text="➕ Создать компанию")
            ],
            [
                KeyboardButton(text="🔄 Переключить компанию"),
                KeyboardButton(text="✏️ Редактировать")
            ],
            [
                KeyboardButton(text="📊 Статистика"),
                KeyboardButton(text="⚙️ Настройки")
            ],
            [
                KeyboardButton(text="🔙 Назад")
            ]
        ],
        resize_keyboard=True
    )


def create_workers_management_keyboard() -> ReplyKeyboardMarkup:
    """Создает клавиатуру управления рабочими"""
    return ReplyKeyboardMarkup(
        keyboard=[
            [
                KeyboardButton(text="📋 Список рабочих"),
                KeyboardButton(text="➕ Добавить рабочего")
            ],
            [
                KeyboardButton(text="🔗 Создать токен"),
                KeyboardButton(text="📊 Статистика")
            ],
            [
                KeyboardButton(text="🔍 Поиск"),
                KeyboardButton(text="⚙️ Настройки")
            ],
            [
                KeyboardButton(text="🔙 Назад")
            ]
        ],
        resize_keyboard=True
    )


def create_worker_actions_keyboard() -> ReplyKeyboardMarkup:
    """Создает клавиатуру действий с рабочим"""
    return ReplyKeyboardMarkup(
        keyboard=[
            [
                KeyboardButton(text="👁️ Просмотр"),
                KeyboardButton(text="✏️ Редактировать")
            ],
            [
                KeyboardButton(text="📊 Статистика"),
                KeyboardButton(text="🏗️ Проекты")
            ],
            [
                KeyboardButton(text="🔒 Заблокировать"),
                KeyboardButton(text="🗑️ Удалить")
            ],
            [
                KeyboardButton(text="🔙 К списку")
            ]
        ],
        resize_keyboard=True
    )


def create_reports_keyboard() -> ReplyKeyboardMarkup:
    """Создает клавиатуру отчетов"""
    return ReplyKeyboardMarkup(
        keyboard=[
            [
                KeyboardButton(text="📆 Отчёт по дате")
            ],
            [
                KeyboardButton(text="👷 Отчёт по рабочему")
            ],
            [
                KeyboardButton(text="🏗️ Отчёт по проекту")
            ],
            [
                KeyboardButton(text="🔙 Назад к отчётам")
            ]
        ],
        resize_keyboard=True
    )


def create_export_import_keyboard() -> ReplyKeyboardMarkup:
    """Создает клавиатуру экспорта/импорта"""
    return ReplyKeyboardMarkup(
        keyboard=[
            [
                KeyboardButton(text="📤 Экспорт данных"),
                KeyboardButton(text="📥 Импорт данных")
            ],
            [
                KeyboardButton(text="📊 Экспорт отчетов"),
                KeyboardButton(text="📋 Шаблоны")
            ],
            [
                KeyboardButton(text="⚙️ Настройки экспорта"),
                KeyboardButton(text="📅 По периоду")
            ],
            [
                KeyboardButton(text="🔙 Назад")
            ]
        ],
        resize_keyboard=True
    )


def create_export_format_keyboard() -> ReplyKeyboardMarkup:
    """Создает клавиатуру выбора формата экспорта"""
    return ReplyKeyboardMarkup(
        keyboard=[
            [
                KeyboardButton(text="📄 Excel"),
                KeyboardButton(text="📋 PDF")
            ],
            [
                KeyboardButton(text="📊 CSV"),
                KeyboardButton(text="📝 JSON")
            ],
            [
                KeyboardButton(text="🔙 Назад")
            ]
        ],
        resize_keyboard=True
    )


def create_work_types_keyboard() -> ReplyKeyboardMarkup:
    """Создает клавиатуру управления типами работ"""
    return ReplyKeyboardMarkup(
        keyboard=[
            [
                KeyboardButton(text="📋 Список типов"),
                KeyboardButton(text="➕ Добавить тип")
            ],
            [
                KeyboardButton(text="✏️ Редактировать"),
                KeyboardButton(text="🗑️ Удалить")
            ],
            [
                KeyboardButton(text="📊 Статистика"),
                KeyboardButton(text="⚙️ Настройки")
            ],
            [
                KeyboardButton(text="🔙 Назад")
            ]
        ],
        resize_keyboard=True
    )


def create_work_type_actions_keyboard() -> ReplyKeyboardMarkup:
    """Создает клавиатуру действий с типом работы"""
    return ReplyKeyboardMarkup(
        keyboard=[
            [
                KeyboardButton(text="👁️ Просмотр"),
                KeyboardButton(text="✏️ Редактировать")
            ],
            [
                KeyboardButton(text="💰 Изменить цену"),
                KeyboardButton(text="📊 Статистика")
            ],
            [
                KeyboardButton(text="🔒 Деактивировать"),
                KeyboardButton(text="🗑️ Удалить")
            ],
            [
                KeyboardButton(text="🔙 К списку")
            ]
        ],
        resize_keyboard=True
    )


def create_projects_keyboard() -> ReplyKeyboardMarkup:
    """Создает клавиатуру управления проектами"""
    return ReplyKeyboardMarkup(
        keyboard=[
            [
                KeyboardButton(text="📋 Список проектов"),
                KeyboardButton(text="➕ Создать проект")
            ],
            [
                KeyboardButton(text="🔍 Поиск"),
                KeyboardButton(text="📊 Статистика")
            ],
            [
                KeyboardButton(text="⚙️ Настройки"),
                KeyboardButton(text="📤 Экспорт")
            ],
            [
                KeyboardButton(text="🔙 Назад")
            ]
        ],
        resize_keyboard=True
    )


def create_project_actions_keyboard() -> ReplyKeyboardMarkup:
    """Создает клавиатуру действий с проектом"""
    return ReplyKeyboardMarkup(
        keyboard=[
            [
                KeyboardButton(text="👁️ Просмотр"),
                KeyboardButton(text="✏️ Редактировать")
            ],
            [
                KeyboardButton(text="👷 Рабочие"),
                KeyboardButton(text="📊 Статистика")
            ],
            [
                KeyboardButton(text="🔒 Закрыть"),
                KeyboardButton(text="🗑️ Удалить")
            ],
            [
                KeyboardButton(text="🔙 К списку")
            ]
        ],
        resize_keyboard=True
    )


def create_info_keyboard() -> ReplyKeyboardMarkup:
    """Создает клавиатуру информации"""
    return ReplyKeyboardMarkup(
        keyboard=[
            [
                KeyboardButton(text="🏢 О компании"),
                KeyboardButton(text="📊 Статистика")
            ],
            [
                KeyboardButton(text="👤 Мой профиль"),
                KeyboardButton(text="⚙️ Настройки")
            ],
            [
                KeyboardButton(text="📞 Контакты"),
                KeyboardButton(text="❓ Помощь")
            ],
            [
                KeyboardButton(text="🔙 Назад")
            ]
        ],
        resize_keyboard=True
    )
