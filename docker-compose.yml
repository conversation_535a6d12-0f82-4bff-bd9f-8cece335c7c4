version: '3.8'

services:
  # PostgreSQL база данных
  db:
    image: postgres:15
    container_name: worklog_postgres
    environment:
      POSTGRES_DB: worklog
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: 7244
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis для FSM storage
  redis:
    image: redis:7-alpine
    container_name: worklog_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Telegram Bot (опционально, для продакшена)
  bot:
    build: .
    container_name: worklog_bot
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      - DATABASE_URL=postgresql+asyncpg://postgres:7244@db:5432/worklog
      - REDIS_URL=redis://redis:6379/0
    env_file:
      - .env
    restart: unless-stopped
    profiles:
      - production  # Запускается только с профилем production

volumes:
  postgres_data:
  redis_data:
