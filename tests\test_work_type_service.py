"""
Тесты для WorkTypeService

Проверяет все методы сервиса управления типами работ.
"""
import pytest
import asyncio
from datetime import datetime
from unittest.mock import AsyncMock, patch

from services.work_type_service import WorkTypeService
from db.models import WorkType


class TestWorkTypeService:
    """Тесты для WorkTypeService"""

    @pytest.fixture
    def sample_work_type_data(self):
        """Тестовые данные для типа работы"""
        return {
            'company_id': 1,
            'name': 'Монтаж',
            'unit': 'час',
            'rate': 25.50,
            'created_by': 123
        }

    @pytest.fixture
    def mock_work_type(self):
        """Мок объекта WorkType"""
        work_type = AsyncMock()
        # Правильные поля схемы
        work_type.work_type_id = 1
        work_type.id = 1
        work_type.company_id = 1
        work_type.project_id = 1
        work_type.name = 'Монтаж'
        work_type.unit = 'час'
        work_type.rate = 25.50
        work_type.hourly_rate = 25.50
        work_type.value = 25.50
        work_type.rate_type = 'hourly'
        work_type.is_deleted = False
        work_type.created_at = datetime.now()
        work_type.updated_at = datetime.now()
        return work_type

    @pytest.mark.asyncio
    async def test_create_work_type_success(self, sample_work_type_data, mock_work_type):
        """Тест успешного создания типа работы"""
        with patch('services.work_type_service.async_session') as mock_session:
            # Настройка мока сессии
            mock_session_instance = AsyncMock()
            mock_session.return_value.__aenter__.return_value = mock_session_instance

            # Мок для проверки существования
            mock_session_instance.execute.return_value.scalar_one_or_none.return_value = None

            # Мок для создания
            mock_session_instance.add = AsyncMock()
            mock_session_instance.commit = AsyncMock()
            mock_session_instance.refresh = AsyncMock()

            # Настройка возвращаемого объекта с правильными атрибутами
            mock_work_type.work_type_id = 1
            mock_work_type.id = 1
            mock_work_type.name = 'Монтаж'
            mock_work_type.unit = 'час'
            mock_work_type.rate = 25.50
            mock_work_type.hourly_rate = 25.50
            mock_work_type.company_id = 1
            mock_work_type.is_deleted = False

            with patch('services.work_type_service.WorkType', return_value=mock_work_type):
                result = await WorkTypeService.create_work_type(**sample_work_type_data)

            # Проверки
            assert result is not None
            mock_session_instance.add.assert_called_once()
            mock_session_instance.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_work_type_duplicate_name(self, sample_work_type_data, mock_work_type):
        """Тест создания типа работы с дублирующимся названием"""
        with patch('services.work_type_service.async_session') as mock_session:
            mock_session_instance = AsyncMock()
            mock_session.return_value.__aenter__.return_value = mock_session_instance
            
            # Мок для существующего типа работы
            mock_session_instance.execute.return_value.scalar_one_or_none.return_value = mock_work_type
            
            result = await WorkTypeService.create_work_type(**sample_work_type_data)
            
            # Проверки
            assert result is None

    @pytest.mark.asyncio
    async def test_get_company_work_types_success(self):
        """Тест получения типов работ компании"""
        with patch('services.work_type_service.async_session') as mock_session:
            mock_session_instance = AsyncMock()
            mock_session.return_value.__aenter__.return_value = mock_session_instance

            # Мок данных с правильными полями
            mock_work_type1 = AsyncMock()
            mock_work_type1.work_type_id = 1
            mock_work_type1.name = 'Монтаж'
            mock_work_type1.unit = 'час'
            mock_work_type1.rate = 25.50
            mock_work_type1.hourly_rate = 25.50
            mock_work_type1.value = 25.50
            mock_work_type1.company_id = 1
            mock_work_type1.project_id = 1
            mock_work_type1.is_deleted = False
            mock_work_type1.created_at = datetime.now()
            mock_work_type1.updated_at = datetime.now()

            mock_work_type2 = AsyncMock()
            mock_work_type2.work_type_id = 2
            mock_work_type2.name = 'Демонтаж'
            mock_work_type2.unit = 'м²'
            mock_work_type2.rate = 20.00
            mock_work_type2.hourly_rate = 20.00
            mock_work_type2.value = 20.00
            mock_work_type2.company_id = 1
            mock_work_type2.project_id = 1
            mock_work_type2.is_deleted = False
            mock_work_type2.created_at = datetime.now()
            mock_work_type2.updated_at = datetime.now()

            mock_work_types = [mock_work_type1, mock_work_type2]
            mock_session_instance.execute.return_value.scalars.return_value.all.return_value = mock_work_types

            result = await WorkTypeService.get_company_work_types(company_id=1)

            # Проверки
            assert len(result) == 2
            assert result[0]['name'] == 'Монтаж'
            assert result[1]['name'] == 'Демонтаж'

    @pytest.mark.asyncio
    async def test_get_work_type_by_id_success(self, mock_work_type):
        """Тест получения типа работы по ID"""
        with patch('services.work_type_service.async_session') as mock_session:
            mock_session_instance = AsyncMock()
            mock_session.return_value.__aenter__.return_value = mock_session_instance

            # Настраиваем мок для возврата объекта
            mock_session_instance.execute.return_value.scalar_one_or_none.return_value = mock_work_type

            result = await WorkTypeService.get_work_type_by_id(work_type_id=1)

            # Проверки
            assert result is not None
            assert result['id'] == 1
            assert result['work_type_id'] == 1
            assert result['name'] == 'Монтаж'

    @pytest.mark.asyncio
    async def test_get_work_type_by_id_not_found(self):
        """Тест получения несуществующего типа работы"""
        with patch('services.work_type_service.async_session') as mock_session:
            mock_session_instance = AsyncMock()
            mock_session.return_value.__aenter__.return_value = mock_session_instance
            
            mock_session_instance.execute.return_value.scalar_one_or_none.return_value = None
            
            result = await WorkTypeService.get_work_type_by_id(work_type_id=999)
            
            # Проверки
            assert result is None

    @pytest.mark.asyncio
    async def test_update_work_type_success(self, mock_work_type):
        """Тест успешного обновления типа работы"""
        with patch('services.work_type_service.async_session') as mock_session:
            mock_session_instance = AsyncMock()
            mock_session.return_value.__aenter__.return_value = mock_session_instance
            
            # Мок для получения типа работы
            mock_session_instance.execute.return_value.scalar_one_or_none.return_value = mock_work_type
            mock_session_instance.commit = AsyncMock()
            
            result = await WorkTypeService.update_work_type(
                work_type_id=1,
                name='Новое название',
                rate=30.00
            )
            
            # Проверки
            assert result is True
            assert mock_work_type.name == 'Новое название'
            assert mock_work_type.rate == 30.00
            mock_session_instance.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_delete_work_type_soft_delete(self, mock_work_type):
        """Тест мягкого удаления типа работы"""
        with patch('services.work_type_service.async_session') as mock_session:
            mock_session_instance = AsyncMock()
            mock_session.return_value.__aenter__.return_value = mock_session_instance
            
            mock_session_instance.execute.return_value.scalar_one_or_none.return_value = mock_work_type
            mock_session_instance.commit = AsyncMock()
            
            result = await WorkTypeService.delete_work_type(work_type_id=1, soft_delete=True)
            
            # Проверки
            assert result is True
            assert mock_work_type.is_deleted is True
            mock_session_instance.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_validate_work_type_data_valid(self):
        """Тест валидации корректных данных"""
        result = await WorkTypeService.validate_work_type_data(
            name='Монтаж',
            unit='час',
            rate=25.50
        )
        
        # Проверки
        assert result['is_valid'] is True
        assert len(result['errors']) == 0
        assert result['cleaned_data']['name'] == 'Монтаж'
        assert result['cleaned_data']['unit'] == 'час'
        assert result['cleaned_data']['rate'] == 25.50

    @pytest.mark.asyncio
    async def test_validate_work_type_data_invalid(self):
        """Тест валидации некорректных данных"""
        result = await WorkTypeService.validate_work_type_data(
            name='',  # Пустое название
            unit='',  # Пустая единица
            rate=-5   # Отрицательная ставка
        )
        
        # Проверки
        assert result['is_valid'] is False
        assert len(result['errors']) == 3
        assert 'Название не может быть пустым' in result['errors']
        assert 'Единица измерения не может быть пустой' in result['errors']
        assert 'Ставка должна быть больше нуля' in result['errors']

    @pytest.mark.asyncio
    async def test_get_work_types_statistics(self):
        """Тест получения статистики типов работ"""
        with patch('services.work_type_service.async_session') as mock_session:
            mock_session_instance = AsyncMock()
            mock_session.return_value.__aenter__.return_value = mock_session_instance

            # Мок для разных запросов статистики
            mock_session_instance.execute.side_effect = [
                AsyncMock(scalar=AsyncMock(return_value=5)),  # total_count
                AsyncMock(scalar=AsyncMock(return_value=2)),  # deleted_count
                AsyncMock(scalar=AsyncMock(return_value=25.75))  # avg_rate
            ]

            result = await WorkTypeService.get_work_types_statistics(company_id=1)

            # Проверки
            assert result['total_count'] == 5
            assert result['active_count'] == 5
            assert result['deleted_count'] == 2
            assert result['average_rate'] == 25.75

    @pytest.mark.asyncio
    async def test_create_work_type_exception_handling(self, sample_work_type_data):
        """Тест обработки исключений при создании типа работы"""
        with patch('services.work_type_service.async_session') as mock_session:
            mock_session_instance = AsyncMock()
            mock_session.return_value.__aenter__.return_value = mock_session_instance
            
            # Мок исключения
            mock_session_instance.execute.side_effect = Exception("Database error")
            mock_session_instance.rollback = AsyncMock()
            
            result = await WorkTypeService.create_work_type(**sample_work_type_data)
            
            # Проверки
            assert result is None
            mock_session_instance.rollback.assert_called_once()

    @pytest.mark.asyncio
    async def test_update_work_type_duplicate_name(self, mock_work_type):
        """Тест обновления типа работы с дублирующимся названием"""
        with patch('services.work_type_service.async_session') as mock_session:
            mock_session_instance = AsyncMock()
            mock_session.return_value.__aenter__.return_value = mock_session_instance
            
            # Первый вызов - получение текущего типа работы
            # Второй вызов - проверка существования с новым названием
            mock_session_instance.execute.side_effect = [
                AsyncMock(scalar_one_or_none=AsyncMock(return_value=mock_work_type)),
                AsyncMock(scalar_one_or_none=AsyncMock(return_value=mock_work_type))  # Дубликат найден
            ]
            
            result = await WorkTypeService.update_work_type(
                work_type_id=1,
                name='Существующее название'
            )
            
            # Проверки
            assert result is False
