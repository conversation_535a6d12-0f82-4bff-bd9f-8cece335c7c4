# FSM для редактирования существующего проекта
# Будет реализовано здесь 

import logging
from aiogram import types, Dispatcher
from aiogram.dispatcher import FSMContext
from db.dao.project_dao import ProjectDAO
from db.dao.work_type_dao import WorkTypeDAO
from db.session import async_session
from keyboards.project import edit_project_menu, worktypes_manage_keyboard, rate_type_keyboard, yes_no_keyboard, project_keyboard
from localization.texts import get_text
from states import EditProjectStates
from aiogram.types import ReplyKeyboardMarkup
from services.worktype_wizard import WorkTypeWizardService
from keyboards.common import create_main_menu

logger = logging.getLogger(__name__)

async def cmd_edit_project(message: types.Message, state: FSMContext):
    """Запуск сценария редактирования проекта."""
    user_id = message.from_user.id
    logger.info(f"User {user_id} started /editproject command.")
    await state.finish()
    async with async_session() as session:
        projects = await ProjectDAO.get_by_user_id(session, user_id)
    if not projects:
        await message.answer(get_text('no_projects_for_edit'), reply_markup=create_main_menu())
        return
    keyboard = project_keyboard(projects)
    await message.answer("Выберите проект для редактирования:", reply_markup=keyboard)
    await EditProjectStates.selecting_project.set()

async def process_select_project(message: types.Message, state: FSMContext):
    """Обработка выбора проекта для редактирования."""
    project_name = message.text.strip()
    user_id = message.from_user.id
    if project_name.lower() == get_text("cancel").lower():
        await state.finish()
        await message.answer("Редактирование отменено.", reply_markup=create_main_menu())
        return
    async with async_session() as session:
        project = await ProjectDAO.get_by_name(session, user_id, project_name)
    if not project:
        await message.answer("Проект не найден. Выберите из списка.")
        return
    await state.update_data(selected_project_id=project.project_id, selected_project_name=project.name)
    await message.answer(get_text('what_to_edit', project_name=project.name), reply_markup=edit_project_menu())
    await EditProjectStates.choosing_action.set()

async def process_edit_action(message: types.Message, state: FSMContext):
    """Обработка выбора действия в меню редактирования."""
    text = message.text.strip()
    if text == get_text("cancel"):
        await state.finish()
        await message.answer("Редактирование отменено.", reply_markup=create_main_menu())
        return
    if text == "✏️ Редактировать название":
        await message.answer("Введите новое название (3–100 символов):", reply_markup=types.ReplyKeyboardRemove())
        await EditProjectStates.editing_name.set()
        return
    if text == "🏠 Редактировать адрес":
        await message.answer("Введите новый адрес:", reply_markup=types.ReplyKeyboardRemove())
        await EditProjectStates.editing_address.set()
        return
    if text == "🛠 Изменить типы работ":
        await process_manage_worktypes(message, state)
        return
    if text == "📋 Скопировать типы":
        data = await state.get_data()
        user_id = message.from_user.id
        current_project_id = data.get("selected_project_id")
        async with async_session() as session:
            projects = await ProjectDAO.get_by_user_id(session, user_id)
            projects = [p for p in projects if p.project_id != current_project_id]
        if not projects:
            await message.answer("Нет других проектов для копирования.")
            await EditProjectStates.choosing_action.set()
            return
        keyboard = project_keyboard(projects)
        await message.answer("Выберите проект, с которого скопировать типы работ:", reply_markup=keyboard)
        await EditProjectStates.copying_worktypes.set()
        return
    await message.answer("Пожалуйста, выберите действие из меню.")

async def process_edit_name(message: types.Message, state: FSMContext):
    """Обработка ввода нового названия проекта."""
    new_name = message.text.strip()
    user_id = message.from_user.id
    data = await state.get_data()
    project_id = data.get("selected_project_id")
    if not (3 <= len(new_name) <= 100):
        await message.answer("Название должно быть от 3 до 100 символов.")
        return
    try:
        async with async_session() as session:
            await ProjectDAO.update(session, project_id, name=new_name)
        logger.info(f"User {user_id} updated project name to '{new_name}' (ID: {project_id})")
        await state.update_data(selected_project_name=new_name)
        await message.answer("Название проекта успешно обновлено.", reply_markup=edit_project_menu())
        await EditProjectStates.choosing_action.set()
    except Exception as e:
        logger.exception(f"Ошибка при обновлении названия проекта: {e}")
        await message.answer("Ошибка при обновлении названия проекта.")

async def process_edit_address(message: types.Message, state: FSMContext):
    """Обработка ввода нового адреса проекта."""
    new_address = message.text.strip()
    user_id = message.from_user.id
    data = await state.get_data()
    project_id = data.get("selected_project_id")
    if not new_address:
        await message.answer("Адрес не может быть пустым.")
        return
    try:
        async with async_session() as session:
            await ProjectDAO.update(session, project_id, address=new_address)
        logger.info(f"User {user_id} updated project address to '{new_address}' (ID: {project_id})")
        await message.answer("Адрес проекта успешно обновлён.", reply_markup=edit_project_menu())
        await EditProjectStates.choosing_action.set()
    except Exception as e:
        logger.exception(f"Ошибка при обновлении адреса проекта: {e}")
        await message.answer("Ошибка при обновлении адреса проекта.")

async def process_manage_worktypes(message: types.Message, state: FSMContext):
    """Меню управления типами работ."""
    data = await state.get_data()
    project_id = data.get("selected_project_id")
    async with async_session() as session:
        work_types = await WorkTypeDAO.get_by_project_id(session, project_id)
    if not work_types:
        await message.answer("В этом проекте пока нет типов работ. Добавьте первый тип.", reply_markup=ReplyKeyboardMarkup(resize_keyboard=True, one_time_keyboard=True).add(get_text("add_worktype"), get_text("cancel")))
        await EditProjectStates.managing_worktypes.set()
        return
    keyboard = worktypes_manage_keyboard(work_types)
    await message.answer("Типы работ:", reply_markup=keyboard)
    await EditProjectStates.managing_worktypes.set()

async def process_worktype_action(message: types.Message, state: FSMContext):
    """Обработка выбора действия с типом работы (редактировать, удалить, добавить)."""
    text = message.text.strip()
    data = await state.get_data()
    project_id = data.get("selected_project_id")
    if text == get_text("cancel"):
        await state.finish()
        await message.answer("Редактирование отменено.", reply_markup=create_main_menu())
        return
    if text == get_text("add_worktype"):
        await start_add_worktype_edit(message, state)
        return
    if text.startswith("🖊 "):
        wt_name = text[2:].strip()
        await state.update_data(work_type_to_edit=wt_name)
        kb = ReplyKeyboardMarkup(resize_keyboard=True, one_time_keyboard=True)
        kb.add("Название", "Ед. изм.", "Ставка")
        kb.add(get_text("cancel"))
        await message.answer(get_text('what_to_edit_work_type', work_type_name=wt_name), reply_markup=kb)
        await EditProjectStates.editing_work_type_field.set()
        return
    if text.startswith("❌ "):
        wt_name = text[2:].strip()
        await state.update_data(work_type_to_delete=wt_name)
        await message.answer(get_text('confirm_delete_work_type', work_type_name=wt_name), reply_markup=yes_no_keyboard())
        await EditProjectStates.confirm_delete_work_type.set()
        return
    await message.answer(get_text("please_choose_action"), reply_markup=worktypes_manage_keyboard(await WorkTypeDAO.get_by_project_id(async_session(), data.get("selected_project_id"))))
    return

async def process_edit_work_type_field(message: types.Message, state: FSMContext):
    """Выбор поля для редактирования типа работы."""
    field = message.text.strip()
    if field == get_text("cancel"):
        await process_manage_worktypes(message, state)
        return

    # Получаем данные о редактируемом типе работы
    data = await state.get_data()
    work_type_name = data.get('work_type_to_edit')

    # Определяем поле для редактирования
    if field == "Название":
        await state.update_data(editing_field="name")
        await message.answer(get_text("edit_work_type_name_prompt"), reply_markup=types.ReplyKeyboardRemove())
        await EditProjectStates.editing_worktype.set()
        return
    elif field == "Ед. изм.":
        await state.update_data(editing_field="unit")
        await message.answer(get_text("edit_work_type_unit_prompt", work_type_name=work_type_name), reply_markup=types.ReplyKeyboardRemove())
        await EditProjectStates.editing_worktype.set()
        return
    elif field == "Ставка":
        await state.update_data(editing_field="value")
        await message.answer(get_text("edit_work_type_value_prompt"), reply_markup=types.ReplyKeyboardRemove())
        await EditProjectStates.editing_worktype.set()
        return
    else:
        await message.answer("Пожалуйста, выберите поле из меню.", reply_markup=ReplyKeyboardMarkup(resize_keyboard=True, one_time_keyboard=True).add("Название", "Ед. изм.", "Ставка", get_text("cancel")))
        return

async def process_edit_worktype(message: types.Message, state: FSMContext):
    """Ввод нового значения и сохранение изменений типа работы."""
    data = await state.get_data()
    wt_name = data.get('work_type_to_edit')
    field = data.get('editing_field')
    project_id = data.get('selected_project_id')
    new_value = message.text.strip()
    if new_value.lower() == get_text("cancel").lower():
        await process_manage_worktypes(message, state)
        return
    async with async_session() as session:
        work_types = await WorkTypeDAO.get_by_project_id(session, project_id)
        wt = next((w for w in work_types if w.name == wt_name), None)
        if not wt:
            await message.answer(get_text('work_type_not_found', work_type_name=wt_name))
            await process_manage_worktypes(message, state)
            return
        if field == "name":
            if not new_value:
                await message.answer("Название не может быть пустым.")
                return
            # Проверка на уникальность имени
            if any(w.name == new_value and w.name != wt_name for w in work_types):
                await message.answer("Тип работы с таким названием уже существует.")
                return
            wt.name = new_value
        elif field == "unit":
            if not new_value:
                await message.answer("Единица измерения не может быть пустой.")
                return
            wt.unit = new_value
        elif field == "value":
            try:
                value = float(new_value.replace(',', '.'))
                if value <= 0:
                    raise ValueError
            except Exception:
                await message.answer("Введите корректное число для ставки.")
                return
            wt.value = value
        else:
            await message.answer("Неизвестное поле для редактирования.")
            return
        session.add(wt)
        await session.commit()
    await message.answer(get_text("work_type_updated"))
    await process_manage_worktypes(message, state)

async def process_confirm_delete_work_type(message: types.Message, state: FSMContext):
    """Подтверждение удаления типа работы."""
    answer = message.text.strip().lower()
    data = await state.get_data()
    wt_name = data.get('work_type_to_delete')
    project_id = data.get('selected_project_id')
    if answer == get_text("cancel").lower():
        await message.answer(get_text('what_to_edit', project_name=data.get('selected_project_name')), reply_markup=edit_project_menu())
        await EditProjectStates.choosing_action.set()
        return
    if answer in (get_text("yes").lower(), "yes"):
        try:
            async with async_session() as session:
                work_types = await WorkTypeDAO.get_by_project_id(session, project_id)
                wt = next((w for w in work_types if w.name == wt_name), None)
                if wt:
                    await WorkTypeDAO.delete(session, wt.id)
                    await message.answer(get_text('work_type_deleted', work_type_name=wt_name))
                else:
                    await message.answer(get_text('work_type_not_found', work_type_name=wt_name))
        except Exception as e:
            logger.exception(f"Ошибка при удалении типа работы: {e}")
            await message.answer("Произошла ошибка при удалении типа работы.")
        await process_manage_worktypes(message, state)
        return
    elif answer in (get_text("no").lower(), "no"):
        await message.answer("Удаление отменено.")
        await process_manage_worktypes(message, state)
        return
    else:
        await message.answer(get_text("invalid_yes_no"), reply_markup=yes_no_keyboard())
        return

async def on_save_worktype_editproject(state, data, value):
    # Накапливаем новые типы работ в FSM
    fsm_data = await state.get_data()
    new_work_types = fsm_data.get('new_work_types', [])
    new_work_types.append({
        "name": data.get('work_type_name'),
        "unit": data.get('unit'),
        "rate_type": data.get('rate_type'),
        "value": value,
    })
    await state.update_data(new_work_types=new_work_types)

async def save_new_worktypes_to_db(message, state):
    data = await state.get_data()
    project_id = data.get('selected_project_id')
    new_work_types = data.get('new_work_types', [])
    if new_work_types:
        async with async_session() as session:
            for wt in new_work_types:
                await WorkTypeDAO.create(
                    session=session,
                    project_id=project_id,
                    name=wt['name'],
                    unit=wt['unit'],
                    rate_type=wt['rate_type'],
                    value=wt['value']
                )
        await state.update_data(new_work_types=[])
        await message.answer(get_text('all_worktypes_saved'))
    await process_manage_worktypes(message, state)

async def start_add_worktype_edit(message, state):
    await WorkTypeWizardService.start_add_worktype(message, state, EditProjectStates, on_save_worktype_editproject)

async def process_work_type_name_edit(message, state):
    await WorkTypeWizardService.process_name(message, state, EditProjectStates)

async def process_work_type_unit_edit(message, state):
    await WorkTypeWizardService.process_unit(message, state, EditProjectStates)

async def process_work_type_rate_type_edit(message, state):
    await WorkTypeWizardService.process_rate_type(message, state, EditProjectStates)

async def process_work_type_value_edit(message, state):
    await WorkTypeWizardService.process_value(message, state, EditProjectStates)

async def process_add_another_work_type_edit(message, state):
    # Используем callback для завершения добавления типов работ
    await WorkTypeWizardService.confirm_add_another(message, state, EditProjectStates, save_new_worktypes_to_db)

async def process_copy_worktypes(message: types.Message, state: FSMContext):
    """Копирование типов работ с другого проекта."""
    source_project_name = message.text.strip()
    data = await state.get_data()
    user_id = message.from_user.id
    current_project_id = data.get("selected_project_id")
    async with async_session() as session:
        source_project = await ProjectDAO.get_by_name(session, user_id, source_project_name)
        if not source_project or source_project.project_id == current_project_id:
            await message.answer("Проект не найден. Выберите из списка.")
            return
        # Получаем типы работ текущего проекта
        current_types = await WorkTypeDAO.get_by_project_id(session, current_project_id)
        current_names = {wt.name for wt in current_types}
        # Получаем типы работ источника
        source_types = await WorkTypeDAO.get_by_project_id(session, source_project.project_id)
        copied = 0
        for wt in source_types:
            if wt.name not in current_names:
                await WorkTypeDAO.create(
                    session=session,
                    project_id=current_project_id,
                    name=wt.name,
                    unit=wt.unit,
                    rate_type=wt.rate_type,
                    value=wt.value,
                    hourly_rate=wt.hourly_rate
                )
                copied += 1
        if copied == 0:
            await message.answer("Нет новых типов работ для копирования.")
        else:
            await message.answer(get_text('work_types_copied', copied=copied))
    await process_manage_worktypes(message, state)

def register_edit_project_handlers(dp: Dispatcher):
    dp.register_message_handler(cmd_edit_project, commands=["editproject"], state="*")
    dp.register_message_handler(process_select_project, state=EditProjectStates.selecting_project)
    dp.register_message_handler(process_edit_action, state=EditProjectStates.choosing_action)
    dp.register_message_handler(process_edit_name, state=EditProjectStates.editing_name)
    dp.register_message_handler(process_edit_address, state=EditProjectStates.editing_address)
    dp.register_message_handler(process_worktype_action, state=EditProjectStates.managing_worktypes)
    dp.register_message_handler(process_work_type_name_edit, state=EditProjectStates.adding_work_type_name)
    dp.register_message_handler(process_work_type_unit_edit, state=EditProjectStates.adding_work_type_unit)
    dp.register_message_handler(process_work_type_rate_type_edit, state=EditProjectStates.adding_work_type_rate_type)
    dp.register_message_handler(process_work_type_value_edit, state=EditProjectStates.adding_work_type_value)
    dp.register_message_handler(process_add_another_work_type_edit, state=EditProjectStates.confirming_add_another_work_type)
    dp.register_message_handler(process_confirm_delete_work_type, state=EditProjectStates.confirm_delete_work_type)
    dp.register_message_handler(process_edit_work_type_field, state=EditProjectStates.editing_work_type_field)
    dp.register_message_handler(process_edit_worktype, state=EditProjectStates.editing_worktype)
    dp.register_message_handler(process_copy_worktypes, state=EditProjectStates.copying_worktypes)
    logger.info("Хендлеры /editproject зарегистрированы") 