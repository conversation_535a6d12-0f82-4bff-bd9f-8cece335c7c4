"""
FSM состояния для всех команд Worklog Bot.

Каждая группа состояний соответствует определенной команде или сценарию.
Используется aiogram v3.x FSM для управления диалогами с пользователями.

ВАЖНО: Этот файл адаптирован для aiogram v3.x
Для aiogram v2.x используйте: from aiogram.dispatcher.filters.state import State, StatesGroup
"""
from aiogram.fsm.state import State, StatesGroup

# Состояния для добавления записей о работе (/addwork)
class AddWorkStates(StatesGroup):
    """
    Состояния FSM для добавления записей о работе.
    
    Команда: /addwork
    Шаги:
    1. waiting_for_date_choice - выбор даты (сегодня/ввести дату)
    2. waiting_for_date_input - ввод конкретной даты
    3. waiting_for_work_type - выбор типа работы
    4. waiting_for_description - ввод описания
    5. waiting_for_quantity - ввод количества
    6. confirming_entry - подтверждение записи
    """
    waiting_for_date_choice = State()
    waiting_for_date_input = State()
    waiting_for_work_type = State()
    waiting_for_description = State()
    waiting_for_quantity = State()
    confirming_entry = State()

# Состояния для редактирования записей (/edit)
class EditWorkStates(StatesGroup):
    """
    Состояния FSM для редактирования записей о работе.
    
    Команда: /edit
    Шаги:
    1. selecting_entry_to_edit - выбор записи для редактирования
    2. choosing_edit_field - выбор поля для редактирования
    3. entering_edit_value - ввод нового значения
    """
    selecting_entry_to_edit = State()
    choosing_edit_field = State()
    entering_edit_value = State()

# Состояния для удаления записей (/delete)
class DeleteWorkStates(StatesGroup):
    """
    Состояния FSM для удаления записей о работе.
    
    Команда: /delete
    Шаги:
    1. selecting_entry_to_delete - выбор записи для удаления
    2. confirming_delete - подтверждение удаления
    """
    selecting_entry_to_delete = State()
    confirming_delete = State()

# Состояния для создания нового проекта (/newproject)
class NewProjectStates(StatesGroup):
    """
    Состояния FSM для создания нового проекта.
    
    Команда: /newproject
    Шаги:
    1. creating_project_name - ввод названия проекта
    2. creating_project_address - ввод адреса проекта
    3. choosing_copy_source - выбор источника для копирования типов работ
    4. adding_work_type_name - ввод названия типа работы
    5. adding_work_type_unit - ввод единицы измерения
    6. adding_work_type_rate_type - выбор типа ставки
    7. adding_work_type_value - ввод значения ставки
    8. confirming_add_another_work_type - подтверждение добавления еще одного типа
    """
    creating_project_name = State()
    creating_project_address = State()
    choosing_copy_source = State()
    adding_work_type_name = State()
    adding_work_type_unit = State()
    adding_work_type_rate_type = State()
    adding_work_type_value = State()
    confirming_add_another_work_type = State()

# Состояния для редактирования проекта (/editproject)
class EditProjectStates(StatesGroup):
    """
    Состояния FSM для редактирования проекта.
    
    Команда: /editproject
    Шаги:
    1. selecting_project - выбор проекта для редактирования
    2. choosing_action - выбор действия (редактировать/типы работ)
    3. editing_field - редактирование поля проекта
    4. managing_work_types - управление типами работ
    """
    selecting_project = State()
    choosing_action = State()
    editing_field = State()
    managing_work_types = State()

# Состояния для выбора проекта (/setproject)
class ProjectSelectionStates(StatesGroup):
    """
    Состояния FSM для выбора активного проекта.
    
    Команда: /setproject
    Шаги:
    1. selecting_project - выбор проекта из списка
    """
    selecting_project = State()

# Состояния для просмотра списка записей (/list)
class ListStates(StatesGroup):
    """
    Состояния FSM для просмотра списка записей.
    
    Команда: /list
    Шаги:
    1. selecting_filter - выбор фильтра (все/за период)
    2. entering_date_range - ввод диапазона дат
    """
    selecting_filter = State()
    entering_date_range = State()

# Состояния для экспорта данных (/export)
class ExportStates(StatesGroup):
    """
    Состояния FSM для экспорта данных.
    
    Команда: /export
    Шаги:
    1. choosing_format - выбор формата (Excel/PDF)
    2. choosing_project - выбор проекта
    3. choosing_period - выбор периода
    4. entering_date_range - ввод диапазона дат
    5. choosing_columns - выбор столбцов (только для Excel)
    6. confirming_export - подтверждение экспорта
    """
    choosing_format = State()
    choosing_project = State()
    choosing_period = State()
    entering_date_range = State()
    choosing_columns = State()
    confirming_export = State()

# Состояния для регистрации (/register)
class RegistrationStates(StatesGroup):
    """
    Состояния FSM для регистрации по токену.
    
    Команда: /register
    Шаги:
    1. waiting_for_token - ввод токена
    2. waiting_for_company_name - ввод названия компании (для директоров)
    """
    waiting_for_token = State()
    waiting_for_company_name = State()

# Состояния для админ панели (/admin)
class AdminStates(StatesGroup):
    """
    Состояния FSM для админ панели.
    
    Команда: /admin
    Используется для сложных админских операций
    """
    managing_companies = State()
    creating_company = State()
    managing_users = State()

# Состояния для панели директора (/manage)
class DirectorStates(StatesGroup):
    """
    Состояния FSM для панели директора.
    
    Команда: /manage
    Используется для управления компанией
    """
    managing_workers = State()
    creating_tokens = State()
    viewing_stats = State()

# DEPRECATED: Оставляем для обратной совместимости
class WorkStates(StatesGroup):
    """
    DEPRECATED: Используйте специализированные классы состояний.
    
    Этот класс оставлен для обратной совместимости со старым кодом.
    Рекомендуется использовать специализированные классы:
    - AddWorkStates для /addwork
    - EditWorkStates для /edit
    - DeleteWorkStates для /delete
    - NewProjectStates для /newproject
    - и т.д.
    """
    # Состояния для добавления записи (/addwork)
    choosing_date = State()
    choosing_work_type = State()
    entering_description = State()
    entering_quantity = State()
    confirming_entry = State()

    # Состояния для редактирования записи (/edit)
    selecting_entry_to_edit = State()
    choosing_edit_field = State()
    entering_edit_value = State()

    # Состояния для удаления записи (/delete)
    selecting_entry_to_delete = State()
    confirming_delete = State()

    # Состояния для управления проектами (/newproject, /setproject)
    creating_project_name = State()
    creating_project_address = State()
    choosing_copy_source = State()

    # Цикл добавления типов работ
    adding_work_type_name = State()
    adding_work_type_unit = State()
    adding_work_type_rate_type = State()
    adding_work_type_value = State()
    confirming_add_another_work_type = State()

    selecting_project = State()

    # Состояния обработки фильтров списка (/list)
    selecting_filter = State()
    entering_date_range = State()

# Примеры использования в обработчиках для aiogram v3.x:
"""
# В обработчике команды (aiogram v3.x)
await state.set_state(AddWorkStates.waiting_for_date_choice)

# В обработчике состояния (aiogram v3.x)
@router.message(AddWorkStates.waiting_for_date_choice)
async def process_date_choice(message: types.Message, state: FSMContext):
    # Логика обработки
    await state.set_state(AddWorkStates.waiting_for_work_type)

# Получение данных состояния (одинаково в v2.x и v3.x)
data = await state.get_data()
await state.update_data(key="value")

# Завершение состояния (aiogram v3.x)
await state.clear()

# Для aiogram v2.x (DEPRECATED):
# await AddWorkStates.waiting_for_date_choice.set()
# await state.finish()
"""
