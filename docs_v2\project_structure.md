# 🏗️ СТРУКТУРА ПРОЕКТА WORKLOG MVP v2.0

## 📁 Общая структура

```
worklog_mvp_v2/
├── 📁 src/                          # Исходный код приложения
│   ├── 📁 handlers/                 # Обработчики Telegram команд
│   ├── 📁 services/                 # Бизнес-логика
│   ├── 📁 models/                   # Модели данных (SQLAlchemy)
│   ├── 📁 dao/                      # Data Access Objects
│   ├── 📁 keyboards/                # Inline-клавиатуры
│   ├── 📁 middleware/               # Middleware для обработки запросов
│   ├── 📁 localization/             # Тексты и локализация
│   ├── 📁 utils/                    # Утилиты и вспомогательные функции
│   ├── 📁 alembic/                  # Миграции базы данных
│   ├── 📄 bot.py                    # Главный файл бота
│   ├── 📄 config.py                 # Конфигурация приложения
│   ├── 📄 database.py               # Настройка подключения к БД
│   ├── 📄 states.py                 # FSM состояния
│   └── 📄 exceptions.py             # Пользовательские исключения
├── 📁 tests/                        # Тесты
│   ├── 📁 unit/                     # Unit-тесты
│   ├── 📁 integration/              # Интеграционные тесты
│   ├── 📁 fixtures/                 # Тестовые данные
│   └── 📄 conftest.py               # Конфигурация pytest
├── 📁 docs_v2/                      # Документация v2.0
├── 📁 scripts/                      # Скрипты для развёртывания
├── 📄 requirements.txt              # Python зависимости
├── 📄 docker-compose.yml            # Docker конфигурация
├── 📄 .env.example                  # Пример конфигурации
├── 📄 pytest.ini                    # Конфигурация тестов
├── 📄 alembic.ini                   # Конфигурация миграций
└── 📄 README.md                     # Основная документация
```

## 📂 Детальная структура модулей

### 🎮 Handlers (Обработчики команд)

```
src/handlers/
├── 📄 __init__.py                   # Регистрация всех роутеров
├── 📄 common.py                     # Общие обработчики (/start, /help)
├── 📄 auth.py                       # Аутентификация и регистрация
├── 📄 worker.py                     # Команды для рабочих
├── 📄 director.py                   # Команды для директоров
├── 📄 admin.py                      # Команды для администраторов
├── 📄 projects.py                   # Управление проектами
├── 📄 reports.py                    # Создание отчётов
├── 📄 notes.py                      # Заметки и запросы
└── 📄 errors.py                     # Обработка ошибок
```

#### Принципы организации handlers:
- **По ролям** - отдельные файлы для каждой роли
- **По функциональности** - группировка связанных команд
- **Единый стиль** - все обработчики используют декораторы и middleware
- **Валидация** - проверка прав доступа на каждом уровне

### ⚙️ Services (Бизнес-логика)

```
src/services/
├── 📄 __init__.py                   # Экспорт всех сервисов
├── 📄 base_service.py               # Базовый класс для сервисов
├── 📄 auth_service.py               # Аутентификация и авторизация
├── 📄 user_service.py               # Управление пользователями
├── 📄 company_service.py            # Управление компаниями
├── 📄 project_service.py            # Управление проектами
├── 📄 work_service.py               # Записи о работе
├── 📄 director_service.py           # Функции директора
├── 📄 report_service.py             # Создание отчётов
├── 📄 export_service.py             # Экспорт данных
├── 📄 notification_service.py       # Уведомления
└── 📄 validation_service.py         # Валидация данных
```

#### Архитектура сервисов:
```python
class BaseService:
    """Базовый класс для всех сервисов."""
    
    def __init__(self, session: AsyncSession):
        self.session = session
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def _validate_permissions(self, user: User, action: str, resource: Any):
        """Проверяет права доступа."""
        pass
    
    async def _log_action(self, user: User, action: str, details: dict):
        """Логирует действие пользователя."""
        pass

class WorkService(BaseService):
    """Сервис для работы с записями о работе."""
    
    async def create_work_entry(self, user: User, **kwargs) -> WorkEntry:
        """Создаёт новую запись о работе."""
        # Валидация прав
        await self._validate_permissions(user, "create_work", kwargs)
        
        # Валидация данных
        self._validate_work_data(**kwargs)
        
        # Создание записи
        work_entry = WorkEntry(**kwargs)
        self.session.add(work_entry)
        await self.session.commit()
        
        # Логирование
        await self._log_action(user, "create_work", {"work_id": work_entry.id})
        
        return work_entry
```

### 🗃️ Models (Модели данных)

```
src/models/
├── 📄 __init__.py                   # Экспорт всех моделей
├── 📄 base.py                       # Базовые классы и миксины
├── 📄 user.py                       # Модель пользователя
├── 📄 company.py                    # Модель компании
├── 📄 project.py                    # Модель проекта
├── 📄 work_entry.py                 # Модель записи о работе
├── 📄 work_category.py              # Модель категории работы
├── 📄 project_assignment.py         # Назначения на проекты
├── 📄 registration_token.py         # Токены регистрации
├── 📄 export_log.py                 # Логи экспорта
└── 📄 notification.py               # Уведомления
```

#### Принципы моделей:
```python
class BaseModel:
    """Базовая модель с общими полями."""
    
    id: Mapped[int] = mapped_column(primary_key=True)
    created_at: Mapped[datetime] = mapped_column(default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(
        default=datetime.utcnow,
        onupdate=datetime.utcnow
    )

class TimestampMixin:
    """Миксин для временных меток."""
    created_at: Mapped[datetime]
    updated_at: Mapped[datetime]

class SoftDeleteMixin:
    """Миксин для мягкого удаления."""
    is_deleted: Mapped[bool] = mapped_column(default=False)
    deleted_at: Mapped[Optional[datetime]]
```

### 💾 DAO (Data Access Objects)

```
src/dao/
├── 📄 __init__.py                   # Экспорт всех DAO
├── 📄 base_dao.py                   # Базовый класс DAO
├── 📄 user_dao.py                   # DAO для пользователей
├── 📄 company_dao.py                # DAO для компаний
├── 📄 project_dao.py                # DAO для проектов
├── 📄 work_entry_dao.py             # DAO для записей о работе
└── 📄 report_dao.py                 # DAO для отчётов
```

#### Архитектура DAO:
```python
class BaseDAO:
    """Базовый класс для всех DAO."""
    
    def __init__(self, session: AsyncSession):
        self.session = session
    
    async def create(self, **kwargs) -> Any:
        """Создаёт новую запись."""
        pass
    
    async def get_by_id(self, id: int) -> Optional[Any]:
        """Получает запись по ID."""
        pass
    
    async def update(self, id: int, **kwargs) -> Optional[Any]:
        """Обновляет запись."""
        pass
    
    async def delete(self, id: int) -> bool:
        """Удаляет запись."""
        pass
    
    async def list_with_filters(self, **filters) -> List[Any]:
        """Получает список с фильтрами."""
        pass
```

### ⌨️ Keyboards (Reply-клавиатуры)

```
src/keyboards/
├── 📄 __init__.py                   # Экспорт всех клавиатур
├── 📄 base.py                       # Базовые классы для клавиатур
├── 📄 common.py                     # Общие клавиатуры
├── 📄 worker.py                     # Клавиатуры для рабочих
├── 📄 director.py                   # Клавиатуры для директоров
├── 📄 admin.py                      # Клавиатуры для администраторов
├── 📄 projects.py                   # Клавиатуры проектов
├── 📄 reports.py                    # Клавиатуры отчётов
└── 📄 navigation.py                 # Навигационные клавиатуры
```

#### Стандарт создания клавиатур:
```python
from src.keyboards.base import ReplyKeyboardBuilder
from src.localization.texts import get_text

def create_worker_menu_keyboard() -> ReplyKeyboardMarkup:
    """Создаёт главное меню для рабочего."""
    return (ReplyKeyboardBuilder()
        .add_row([
            get_text("buttons.add_work"),
            get_text("buttons.my_work")
        ])
        .add_row([
            get_text("buttons.my_projects"),
            get_text("buttons.notes")
        ])
        .add_back_button()
        .build())
```

### 🛡️ Middleware

```
src/middleware/
├── 📄 __init__.py                   # Регистрация middleware
├── 📄 auth_middleware.py            # Аутентификация
├── 📄 rbac_middleware.py            # Контроль доступа
├── 📄 logging_middleware.py         # Логирование запросов
├── 📄 rate_limit_middleware.py      # Ограничение частоты запросов
└── 📄 error_middleware.py           # Обработка ошибок
```

#### Порядок применения middleware:
1. **ErrorMiddleware** - обработка исключений
2. **LoggingMiddleware** - логирование запросов
3. **RateLimitMiddleware** - ограничение частоты
4. **AuthMiddleware** - аутентификация
5. **RBACMiddleware** - проверка прав доступа

### 🌍 Localization (Локализация)

```
src/localization/
├── 📄 __init__.py                   # Инициализация локализации
├── 📄 texts.py                      # Основные тексты
├── 📄 buttons.py                    # Тексты кнопок
├── 📄 errors.py                     # Сообщения об ошибках
├── 📄 notifications.py              # Тексты уведомлений
└── 📁 locales/                      # Файлы переводов (будущее)
    ├── 📄 ru.json                   # Русский язык
    └── 📄 en.json                   # Английский язык
```

#### Структура текстов:
```python
TEXTS = {
    "common": {
        "confirm": "✅ Подтвердить",
        "cancel": "❌ Отменить",
        "back": "🔙 Назад",
        "save": "💾 Сохранить"
    },
    "worker": {
        "menu_title": "👷 Меню рабочего",
        "add_work": "➕ Добавить работу",
        "my_work": "📋 Мои работы"
    },
    "director": {
        "menu_title": "👨‍💼 Меню директора",
        "manage_projects": "🏗️ Управление проектами"
    },
    "errors": {
        "access_denied": "❌ Доступ запрещён",
        "invalid_data": "❌ Некорректные данные"
    }
}
```

### 🔧 Utils (Утилиты)

```
src/utils/
├── 📄 __init__.py                   # Экспорт утилит
├── 📄 decorators.py                 # Декораторы (require_role, etc.)
├── 📄 validators.py                 # Валидаторы данных
├── 📄 formatters.py                 # Форматирование данных
├── 📄 date_utils.py                 # Работа с датами
├── 📄 file_utils.py                 # Работа с файлами
├── 📄 crypto_utils.py               # Криптографические функции
└── 📄 logging_config.py             # Настройка логирования
```

## 🧪 Структура тестов

```
tests/
├── 📄 conftest.py                   # Конфигурация pytest и фикстуры
├── 📁 unit/                         # Unit-тесты
│   ├── 📁 services/                 # Тесты сервисов
│   ├── 📁 models/                   # Тесты моделей
│   ├── 📁 dao/                      # Тесты DAO
│   └── 📁 utils/                    # Тесты утилит
├── 📁 integration/                  # Интеграционные тесты
│   ├── 📁 handlers/                 # Тесты обработчиков
│   ├── 📁 workflows/                # Тесты пользовательских сценариев
│   └── 📁 api/                      # Тесты API (будущее)
├── 📁 fixtures/                     # Тестовые данные
│   ├── 📄 users.py                  # Фикстуры пользователей
│   ├── 📄 companies.py              # Фикстуры компаний
│   └── 📄 projects.py               # Фикстуры проектов
└── 📁 performance/                  # Нагрузочные тесты
    └── 📄 test_load.py              # Тесты производительности
```

## 📄 Конфигурационные файлы

### requirements.txt
```txt
# Core dependencies
aiogram==3.4.1
sqlalchemy[asyncio]==2.0.25
alembic==1.13.1
asyncpg==0.29.0
pydantic==2.5.3
pydantic-settings==2.1.0

# Development dependencies
pytest==7.4.4
pytest-asyncio==0.23.2
pytest-cov==4.1.0
black==23.12.1
flake8==7.0.0
mypy==1.8.0

# Production dependencies
uvloop==0.19.0
redis==5.0.1
celery==5.3.4
```

### docker-compose.yml
```yaml
version: '3.8'

services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: worklog_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: 7244
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

  bot:
    build: .
    depends_on:
      - postgres
      - redis
    environment:
      - DATABASE_URL=postgresql+asyncpg://postgres:7244@postgres:5432/worklog_db
      - REDIS_URL=redis://redis:6379/0
    volumes:
      - ./src:/app/src

volumes:
  postgres_data:
```

### .env.example
```env
# Bot Configuration
BOT_TOKEN=your_bot_token_here
DEBUG=True
LOG_LEVEL=INFO

# Database
DATABASE_URL=postgresql+asyncpg://postgres:7244@localhost:5432/worklog_db

# Redis (for FSM storage)
REDIS_URL=redis://localhost:6379/0

# Security
SECRET_KEY=your_secret_key_here
TOKEN_TTL_HOURS=24

# Rate Limiting
RATE_LIMIT_COMMANDS_PER_MINUTE=10
RATE_LIMIT_REPORTS_PER_HOUR=5

# Test Data
TEST_ADMIN_TELEGRAM_ID=*********
TEST_COMPANY_NAME=Тестовая Строительная Компания
```

## 🚀 Скрипты развёртывания

```
scripts/
├── 📄 setup.sh                     # Первоначальная настройка
├── 📄 migrate.sh                   # Применение миграций
├── 📄 test.sh                      # Запуск тестов
├── 📄 deploy.sh                    # Развёртывание в продакшен
├── 📄 backup.sh                    # Резервное копирование
└── 📄 restore.sh                   # Восстановление из бэкапа
```

## 📋 Принципы организации кода

### 1. Разделение ответственности
- **Handlers** - только обработка Telegram событий
- **Services** - бизнес-логика и валидация
- **DAO** - доступ к данным
- **Models** - структура данных

### 2. Dependency Injection
```python
# Внедрение зависимостей через middleware
@router.callback_query(F.data == "add:work")
async def add_work_handler(
    callback: CallbackQuery,
    user: User,                    # Внедряется через AuthMiddleware
    session: AsyncSession,         # Внедряется через DatabaseMiddleware
    work_service: WorkService      # Внедряется через ServiceMiddleware
):
    await work_service.create_work_entry(user, **data)
```

### 3. Единообразие
- Все файлы имеют docstring с описанием
- Единый стиль именования
- Консистентная обработка ошибок
- Стандартизированное логирование

### 4. Тестируемость
- Каждый компонент легко мокается
- Фикстуры для тестовых данных
- Изоляция тестов
- Покрытие критических путей

### 5. Масштабируемость
- Модульная архитектура
- Слабая связанность компонентов
- Возможность горизонтального масштабирования
- Подготовка к микросервисной архитектуре
