"""
Reply-клавиатуры для администратора.

Включает:
- create_admin_menu() - главное меню администратора
- create_companies_keyboard() - управление компаниями
- create_users_keyboard() - управление пользователями
- create_tokens_keyboard() - управление токенами
"""
from aiogram.types import ReplyKeyboardMarkup, KeyboardButton
from typing import List, Dict


def create_admin_menu() -> ReplyKeyboardMarkup:
    """Создает главное меню администратора"""
    return ReplyKeyboardMarkup(
        keyboard=[
            [
                KeyboardButton(text="🏢 Управление компаниями"),
                KeyboardButton(text="👥 Управление пользователями")
            ],
            [
                KeyboardButton(text="🔗 Управление токенами"),
                KeyboardButton(text="📊 Статистика")
            ],
            [
                KeyboardButton(text="🔧 Системные настройки")
            ],
            [
                KeyboardButton(text="🔙 Назад в меню")
            ]
        ],
        resize_keyboard=True,
        input_field_placeholder="Выберите действие администратора"
    )


def create_companies_management_keyboard() -> ReplyKeyboardMarkup:
    """Создает клавиатуру управления компаниями"""
    return ReplyKeyboardMarkup(
        keyboard=[
            [
                KeyboardButton(text="📋 Список компаний"),
                KeyboardButton(text="➕ Создать компанию")
            ],
            [
                KeyboardButton(text="🗑️ Удаленные компании"),
                KeyboardButton(text="📊 Статистика компаний")
            ],
            [
                KeyboardButton(text="🔙 Назад")
            ]
        ],
        resize_keyboard=True
    )


def create_company_actions_keyboard() -> ReplyKeyboardMarkup:
    """Создает клавиатуру действий с компанией"""
    return ReplyKeyboardMarkup(
        keyboard=[
            [
                KeyboardButton(text="👥 Пользователи"),
                KeyboardButton(text="🔗 Токены")
            ],
            [
                KeyboardButton(text="✏️ Редактировать"),
                KeyboardButton(text="🗑️ Удалить")
            ],
            [
                KeyboardButton(text="📊 Статистика"),
                KeyboardButton(text="🏗️ Проекты")
            ],
            [
                KeyboardButton(text="🔙 К списку")
            ]
        ],
        resize_keyboard=True
    )


def create_users_management_keyboard() -> ReplyKeyboardMarkup:
    """Создает клавиатуру управления пользователями"""
    return ReplyKeyboardMarkup(
        keyboard=[
            [
                KeyboardButton(text="📋 Список директоров"),
                KeyboardButton(text="📋 Список рабочих")
            ],
            [
                KeyboardButton(text="🔍 Поиск пользователя"),
                KeyboardButton(text="📊 Статистика")
            ],
            [
                KeyboardButton(text="🔙 Назад")
            ]
        ],
        resize_keyboard=True
    )


def create_user_actions_keyboard() -> ReplyKeyboardMarkup:
    """Создает клавиатуру действий с пользователем"""
    return ReplyKeyboardMarkup(
        keyboard=[
            [
                KeyboardButton(text="👁️ Просмотр"),
                KeyboardButton(text="✏️ Редактировать")
            ],
            [
                KeyboardButton(text="🔒 Заблокировать"),
                KeyboardButton(text="🗑️ Удалить")
            ],
            [
                KeyboardButton(text="📊 Статистика"),
                KeyboardButton(text="🏢 Компании")
            ],
            [
                KeyboardButton(text="🔙 К списку")
            ]
        ],
        resize_keyboard=True
    )


def create_tokens_management_keyboard() -> ReplyKeyboardMarkup:
    """Создает клавиатуру управления токенами"""
    return ReplyKeyboardMarkup(
        keyboard=[
            [
                KeyboardButton(text="🔗 Создать токен директора"),
                KeyboardButton(text="🔗 Создать токен рабочего")
            ],
            [
                KeyboardButton(text="📋 Активные токены"),
                KeyboardButton(text="🗑️ Удаленные токены")
            ],
            [
                KeyboardButton(text="📊 Статистика токенов")
            ],
            [
                KeyboardButton(text="🔙 Назад")
            ]
        ],
        resize_keyboard=True
    )


def create_token_actions_keyboard() -> ReplyKeyboardMarkup:
    """Создает клавиатуру действий с токеном"""
    return ReplyKeyboardMarkup(
        keyboard=[
            [
                KeyboardButton(text="👁️ Просмотр"),
                KeyboardButton(text="📋 Копировать ссылку")
            ],
            [
                KeyboardButton(text="🔒 Деактивировать"),
                KeyboardButton(text="🗑️ Удалить")
            ],
            [
                KeyboardButton(text="🔙 К списку")
            ]
        ],
        resize_keyboard=True
    )


def create_statistics_keyboard() -> ReplyKeyboardMarkup:
    """Создает клавиатуру статистики"""
    return ReplyKeyboardMarkup(
        keyboard=[
            [
                KeyboardButton(text="📊 Общая статистика"),
                KeyboardButton(text="📈 По компаниям")
            ],
            [
                KeyboardButton(text="👥 По пользователям"),
                KeyboardButton(text="🔗 По токенам")
            ],
            [
                KeyboardButton(text="📅 За период"),
                KeyboardButton(text="📤 Экспорт")
            ],
            [
                KeyboardButton(text="🔙 Назад")
            ]
        ],
        resize_keyboard=True
    )


def create_system_settings_keyboard() -> ReplyKeyboardMarkup:
    """Создает клавиатуру системных настроек"""
    return ReplyKeyboardMarkup(
        keyboard=[
            [
                KeyboardButton(text="⚙️ Общие настройки"),
                KeyboardButton(text="🔧 Конфигурация")
            ],
            [
                KeyboardButton(text="📝 Логи системы"),
                KeyboardButton(text="🔄 Обновления")
            ],
            [
                KeyboardButton(text="💾 Резервные копии"),
                KeyboardButton(text="🧹 Очистка")
            ],
            [
                KeyboardButton(text="🔙 Назад")
            ]
        ],
        resize_keyboard=True
    )
