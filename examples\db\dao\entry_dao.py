"""
DAO для работы с записями о работе (WorkEntry).

Основные операции:
- create() - создание записи
- get_recent_by_user() - последние записи пользователя
- get_by_user_and_date_range() - записи за период
- get_for_export() - данные для экспорта
- update() - обновление записи
- delete() - удаление записи

Все операции учитывают изоляцию данных по компаниям.
"""
from sqlalchemy import select, and_, desc, func
from sqlalchemy.orm import selectinload
from typing import List, Optional
import datetime

from ..models import WorkEntry, WorkType, Project, Company


class EntryDAO:
    """Data Access Object для записей о работе."""

    @staticmethod
    async def create(
        session,
        project_id: int,
        work_type_id: int,
        date: datetime.date,
        quantity: float,
        description: str,
        sum_total: float,
        created_by: int
    ) -> WorkEntry:
        """
        Создает новую запись о работе.
        
        Args:
            session: Сессия БД
            project_id: ID проекта
            work_type_id: ID типа работы
            date: Дата выполнения работы
            quantity: Количество
            description: Описание работы
            sum_total: Рассчитанная сумма
            created_by: Telegram ID создателя
            
        Returns:
            Созданная запись
        """
        # Получаем company_id из проекта для изоляции данных
        project_result = await session.execute(
            select(Project.company_id).where(Project.project_id == project_id)
        )
        company_id = project_result.scalar_one()
        
        entry = WorkEntry(
            project_id=project_id,
            work_type_id=work_type_id,
            created_by=created_by,
            company_id=company_id,
            date=date,
            quantity=quantity,
            description=description,
            sum_total=sum_total
        )
        
        session.add(entry)
        await session.commit()
        await session.refresh(entry)
        return entry

    @staticmethod
    async def get_recent_by_user(
        session,
        user_id: int,
        limit: int = 10,
        company_id: Optional[int] = None
    ) -> List[WorkEntry]:
        """
        Получает последние записи пользователя.
        
        Args:
            session: Сессия БД
            user_id: Telegram ID пользователя
            limit: Количество записей
            company_id: ID компании (для фильтрации)
            
        Returns:
            Список последних записей
        """
        query = select(WorkEntry).where(
            WorkEntry.created_by == user_id
        ).options(
            selectinload(WorkEntry.work_type),
            selectinload(WorkEntry.project)
        ).order_by(desc(WorkEntry.created_at)).limit(limit)
        
        # Фильтрация по компании если указана
        if company_id:
            query = query.where(WorkEntry.company_id == company_id)
        
        result = await session.execute(query)
        return list(result.scalars().all())

    @staticmethod
    async def get_by_user_and_date_range(
        session,
        user_id: int,
        start_date: Optional[datetime.date] = None,
        end_date: Optional[datetime.date] = None,
        company_id: Optional[int] = None
    ) -> List[WorkEntry]:
        """
        Получает записи пользователя за период.
        
        Args:
            session: Сессия БД
            user_id: Telegram ID пользователя
            start_date: Начальная дата (включительно)
            end_date: Конечная дата (включительно)
            company_id: ID компании (для фильтрации)
            
        Returns:
            Список записей за период
        """
        conditions = [WorkEntry.created_by == user_id]
        
        if start_date:
            conditions.append(WorkEntry.date >= start_date)
        if end_date:
            conditions.append(WorkEntry.date <= end_date)
        if company_id:
            conditions.append(WorkEntry.company_id == company_id)
        
        query = select(WorkEntry).where(
            and_(*conditions)
        ).options(
            selectinload(WorkEntry.work_type),
            selectinload(WorkEntry.project)
        ).order_by(WorkEntry.date.desc(), WorkEntry.created_at.desc())
        
        result = await session.execute(query)
        return list(result.scalars().all())

    @staticmethod
    async def get_for_export(
        session,
        project_id: int,
        start_date: Optional[datetime.date] = None,
        end_date: Optional[datetime.date] = None,
        user_id: Optional[int] = None
    ) -> List[dict]:
        """
        Получает данные для экспорта в Excel/PDF.
        
        Args:
            session: Сессия БД
            project_id: ID проекта
            start_date: Начальная дата
            end_date: Конечная дата
            user_id: ID пользователя (для фильтрации)
            
        Returns:
            Список словарей с данными для экспорта
        """
        conditions = [WorkEntry.project_id == project_id]
        
        if start_date:
            conditions.append(WorkEntry.date >= start_date)
        if end_date:
            conditions.append(WorkEntry.date <= end_date)
        if user_id:
            conditions.append(WorkEntry.created_by == user_id)
        
        query = select(WorkEntry).where(
            and_(*conditions)
        ).options(
            selectinload(WorkEntry.work_type),
            selectinload(WorkEntry.project)
        ).order_by(WorkEntry.date, WorkEntry.created_at)
        
        result = await session.execute(query)
        entries = result.scalars().all()
        
        # Преобразуем в формат для экспорта
        export_data = []
        for entry in entries:
            export_data.append({
                "date": entry.date,
                "work_type": entry.work_type.name,
                "description": entry.description or "",
                "quantity": float(entry.quantity),
                "unit": entry.work_type.unit,
                "rate": float(entry.work_type.value),
                "sum": float(entry.sum_total),
                "project_name": entry.project.name,
                "project_address": entry.project.address or ""
            })
        
        return export_data

    @staticmethod
    async def get_summary_by_work_types(
        session,
        project_id: int,
        start_date: Optional[datetime.date] = None,
        end_date: Optional[datetime.date] = None
    ) -> List[dict]:
        """
        Получает сводку по типам работ для отчетов.
        
        Args:
            session: Сессия БД
            project_id: ID проекта
            start_date: Начальная дата
            end_date: Конечная дата
            
        Returns:
            Список словарей с агрегированными данными
        """
        conditions = [WorkEntry.project_id == project_id]
        
        if start_date:
            conditions.append(WorkEntry.date >= start_date)
        if end_date:
            conditions.append(WorkEntry.date <= end_date)
        
        query = select(
            WorkType.name.label("work_type"),
            WorkType.unit.label("unit"),
            func.sum(WorkEntry.quantity).label("total_quantity"),
            func.sum(WorkEntry.sum_total).label("total_sum")
        ).select_from(
            WorkEntry.__table__.join(WorkType.__table__)
        ).where(
            and_(*conditions)
        ).group_by(
            WorkType.name, WorkType.unit
        ).order_by(WorkType.name)
        
        result = await session.execute(query)
        
        summary_data = []
        for row in result:
            summary_data.append({
                "work_type": row.work_type,
                "unit": row.unit,
                "total_quantity": float(row.total_quantity),
                "total_sum": float(row.total_sum)
            })
        
        return summary_data

    @staticmethod
    async def get_by_id(session, entry_id: int) -> Optional[WorkEntry]:
        """
        Получает запись по ID.
        
        Args:
            session: Сессия БД
            entry_id: ID записи
            
        Returns:
            Запись или None
        """
        query = select(WorkEntry).where(
            WorkEntry.entry_id == entry_id
        ).options(
            selectinload(WorkEntry.work_type),
            selectinload(WorkEntry.project)
        )
        
        result = await session.execute(query)
        return result.scalar_one_or_none()

    @staticmethod
    async def update(
        session,
        entry_id: int,
        **kwargs
    ) -> Optional[WorkEntry]:
        """
        Обновляет запись о работе.
        
        Args:
            session: Сессия БД
            entry_id: ID записи
            **kwargs: Поля для обновления
            
        Returns:
            Обновленная запись или None
        """
        entry = await EntryDAO.get_by_id(session, entry_id)
        if not entry:
            return None
        
        # Обновляем только переданные поля
        for key, value in kwargs.items():
            if hasattr(entry, key):
                setattr(entry, key, value)
        
        await session.commit()
        await session.refresh(entry)
        return entry

    @staticmethod
    async def delete(session, entry_id: int) -> bool:
        """
        Удаляет запись о работе.
        
        Args:
            session: Сессия БД
            entry_id: ID записи
            
        Returns:
            True если удалена, False если не найдена
        """
        entry = await EntryDAO.get_by_id(session, entry_id)
        if not entry:
            return False
        
        await session.delete(entry)
        await session.commit()
        return True

    @staticmethod
    async def get_user_stats(
        session,
        user_id: int,
        company_id: Optional[int] = None,
        days: int = 30
    ) -> dict:
        """
        Получает статистику пользователя.
        
        Args:
            session: Сессия БД
            user_id: Telegram ID пользователя
            company_id: ID компании
            days: Количество дней для статистики
            
        Returns:
            Словарь со статистикой
        """
        start_date = datetime.date.today() - datetime.timedelta(days=days)
        
        conditions = [
            WorkEntry.created_by == user_id,
            WorkEntry.date >= start_date
        ]
        
        if company_id:
            conditions.append(WorkEntry.company_id == company_id)
        
        # Общая статистика
        stats_query = select(
            func.count(WorkEntry.entry_id).label("total_entries"),
            func.sum(WorkEntry.sum_total).label("total_sum"),
            func.sum(WorkEntry.quantity).label("total_quantity")
        ).where(and_(*conditions))
        
        result = await session.execute(stats_query)
        stats = result.first()
        
        return {
            "total_entries": stats.total_entries or 0,
            "total_sum": float(stats.total_sum or 0),
            "total_quantity": float(stats.total_quantity or 0),
            "period_days": days
        }

# Примеры использования:
"""
# Создание записи
entry = await EntryDAO.create(
    session=session,
    project_id=1,
    work_type_id=1,
    date=datetime.date.today(),
    quantity=8.0,
    description="Установка оборудования",
    sum_total=8000.0,
    created_by=*********
)

# Получение последних записей
recent_entries = await EntryDAO.get_recent_by_user(
    session=session,
    user_id=*********,
    limit=10
)

# Получение записей за период
entries = await EntryDAO.get_by_user_and_date_range(
    session=session,
    user_id=*********,
    start_date=datetime.date(2023, 1, 1),
    end_date=datetime.date(2023, 1, 31)
)

# Данные для экспорта
export_data = await EntryDAO.get_for_export(
    session=session,
    project_id=1,
    start_date=datetime.date(2023, 1, 1),
    end_date=datetime.date(2023, 1, 31)
)

# Сводка по типам работ
summary = await EntryDAO.get_summary_by_work_types(
    session=session,
    project_id=1
)

# Обновление записи
updated_entry = await EntryDAO.update(
    session=session,
    entry_id=1,
    description="Обновленное описание",
    quantity=10.0
)

# Удаление записи
deleted = await EntryDAO.delete(session=session, entry_id=1)

# Статистика пользователя
stats = await EntryDAO.get_user_stats(
    session=session,
    user_id=*********,
    days=30
)
"""
