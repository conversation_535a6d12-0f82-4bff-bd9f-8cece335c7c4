# 📝 СТАНДАРТЫ КОДИРОВАНИЯ WORKLOG MVP v2.0

## 🎯 Общие принципы

### Философия кода
1. **Читаемость превыше всего** - код читается чаще, чем пишется
2. **Простота и ясность** - избегаем сложных конструкций
3. **Консистентность** - единый стиль во всём проекте
4. **Самодокументируемость** - код должен объяснять сам себя
5. **Тестируемость** - каждая функция должна легко тестироваться

## 🐍 Python стандарты

### Форматирование кода
```python
# ✅ Правильно
def create_work_entry(
    user: User,
    project_id: int,
    description: str,
    quantity: float,
    work_type: str
) -> WorkEntry:
    """
    Создаёт новую запись о работе.
    
    Args:
        user: Пользователь, создающий запись
        project_id: ID проекта
        description: Описание работы
        quantity: Количество работы
        work_type: Тип работы (hourly/piece)
        
    Returns:
        WorkEntry: Созданная запись о работе
        
    Raises:
        ValidationError: При некорректных данных
        EntityNotFoundError: Если проект не найден
    """
    pass

# ❌ Неправильно
def create_work_entry(user,project_id,description,quantity,work_type):
    pass
```

### Именование

#### Переменные и функции
```python
# ✅ Правильно
user_telegram_id = 123456789
project_hourly_rate = 500.0
work_entries_count = 10

def get_user_work_entries(user: User) -> List[WorkEntry]:
    pass

def calculate_total_amount(quantity: float, rate: float) -> Decimal:
    pass

# ❌ Неправильно
uid = 123456789
phr = 500.0
cnt = 10

def getUserWorkEntries(user):
    pass

def calc_total(q, r):
    pass
```

#### Классы
```python
# ✅ Правильно
class WorkEntryService:
    """Сервис для работы с записями о работе."""
    pass

class ProjectAssignment:
    """Назначение рабочего на проект."""
    pass

# ❌ Неправильно
class workEntryService:
    pass

class project_assignment:
    pass
```

#### Константы
```python
# ✅ Правильно
MAX_WORK_HOURS_PER_DAY = 24
DEFAULT_HOURLY_RATE = Decimal("500.00")
SUPPORTED_WORK_TYPES = ["hourly", "piece"]

# ❌ Неправильно
max_hours = 24
default_rate = 500
work_types = ["hourly", "piece"]
```

### Комментарии на русском языке
```python
# ✅ Правильно
class WorkService:
    """Сервис для управления записями о работе."""
    
    async def create_work_entry(self, user: User, **kwargs) -> WorkEntry:
        """
        Создаёт новую запись о работе.
        
        Проверяет права пользователя, валидирует данные
        и сохраняет запись в базу данных.
        """
        # Проверяем права доступа к проекту
        if not await self._check_project_access(user, kwargs['project_id']):
            raise InsufficientPermissionsError("Нет доступа к проекту")
        
        # Валидируем входные данные
        self._validate_work_entry_data(**kwargs)
        
        # Создаём запись
        work_entry = WorkEntry(**kwargs)
        self.session.add(work_entry)
        await self.session.commit()
        
        return work_entry

# ❌ Неправильно
class WorkService:
    async def create_work_entry(self, user, **kwargs):
        # check access
        if not await self._check_project_access(user, kwargs['project_id']):
            raise InsufficientPermissionsError("No access")
        # validate and save
        work_entry = WorkEntry(**kwargs)
        self.session.add(work_entry)
        await self.session.commit()
        return work_entry
```

## ⌨️ Стандарты inline-клавиатур

### Единый подход к кнопкам

#### Текст кнопок
```python
# ✅ Правильно - единый стиль с эмодзи
BUTTON_TEXTS = {
    # Основные действия
    "add_work": "➕ Добавить работу",
    "edit_work": "✏️ Редактировать",
    "delete_work": "🗑️ Удалить",
    "view_work": "👁️ Просмотреть",
    
    # Навигация
    "back": "🔙 Назад",
    "main_menu": "🏠 Главное меню",
    "cancel": "❌ Отменить",
    
    # Подтверждения
    "confirm": "✅ Подтвердить",
    "save": "💾 Сохранить",
    
    # Роли
    "role_admin": "👑 Администратор",
    "role_director": "👨‍💼 Директор", 
    "role_worker": "👷 Рабочий",
    
    # Отчёты и экспорт
    "create_report": "📊 Создать отчёт",
    "export_excel": "📄 Экспорт Excel",
    "export_pdf": "📋 Экспорт PDF"
}

# ❌ Неправильно - разный стиль
buttons = {
    "add": "Добавить работу",
    "edit": "✏️ Edit",
    "delete": "🗑️🗑️ УДАЛИТЬ",
    "view": "view work"
}
```

#### Callback данные
```python
# ✅ Правильно - структурированный формат
def create_callback_data(action: str, entity: str, entity_id: int = None, extra: str = None) -> str:
    """
    Создаёт структурированные callback данные.
    
    Формат: "action:entity:id:extra"
    """
    parts = [action, entity]
    if entity_id is not None:
        parts.append(str(entity_id))
    if extra:
        parts.append(extra)
    return ":".join(parts)

# Примеры использования
"work:add"                    # Добавить работу
"work:edit:123"              # Редактировать работу 123
"project:select:456"         # Выбрать проект 456
"report:export:excel:789"    # Экспорт отчёта 789 в Excel
"role:select:worker"         # Выбрать роль рабочего

# ❌ Неправильно - неструктурированные данные
"add_work"
"edit_work_123"
"project456"
"export_excel_report_789"
```

#### Создание клавиатур
```python
# ✅ Правильно - стандартизированный подход
def create_work_actions_keyboard() -> ReplyKeyboardMarkup:
    """Создаёт клавиатуру действий для записи о работе."""
    keyboard = [
        [
            KeyboardButton(text=get_text("buttons.edit_work")),
            KeyboardButton(text=get_text("buttons.delete_work"))
        ],
        [
            KeyboardButton(text=get_text("buttons.back"))
        ]
    ]
    return ReplyKeyboardMarkup(
        keyboard=keyboard,
        resize_keyboard=True,
        one_time_keyboard=False
    )

# ❌ Неправильно - хардкод текстов
def create_work_actions_keyboard():
    keyboard = [
        [
            KeyboardButton(text="Edit"),
            KeyboardButton(text="Delete")
        ]
    ]
    return ReplyKeyboardMarkup(keyboard=keyboard)
```

### Правила использования кнопок

1. **Только reply-клавиатуры** - удобный интерфейс с текстовыми кнопками
2. **Максимум 3 кнопки в ряду** - для удобства на мобильных
3. **Всегда есть кнопка "Назад"** - для навигации
4. **Логическая группировка** - похожие действия рядом
5. **Эмодзи по смыслу** - не для украшения

## 🌍 Стандарты локализации

### Структура текстов
```python
# ✅ Правильно - иерархическая структура
TEXTS = {
    "common": {
        "confirm": "✅ Подтвердить",
        "cancel": "❌ Отменить",
        "back": "🔙 Назад",
        "save": "💾 Сохранить",
        "error_occurred": "❌ Произошла ошибка"
    },
    "worker": {
        "menu_title": "👷 Меню рабочего",
        "add_work": "➕ Добавить работу",
        "my_work": "📋 Мои работы",
        "my_projects": "🏗️ Мои проекты"
    },
    "director": {
        "menu_title": "👨‍💼 Меню директора",
        "manage_projects": "🏗️ Управление проектами",
        "manage_workers": "👷 Управление рабочими",
        "create_reports": "📊 Создать отчёты"
    },
    "validation": {
        "required_field": "❌ Поле обязательно для заполнения",
        "invalid_number": "❌ Введите корректное число",
        "invalid_date": "❌ Введите корректную дату"
    }
}

# ❌ Неправильно - плоская структура
TEXTS = {
    "confirm": "Подтвердить",
    "worker_menu": "Меню рабочего",
    "director_menu": "Меню директора",
    "error": "Ошибка"
}
```

### Использование текстов
```python
# ✅ Правильно
from src.localization.texts import get_text

async def show_worker_menu(callback: CallbackQuery):
    """Показывает меню рабочего."""
    text = get_text("worker.menu_title", default="👷 Меню рабочего")
    keyboard = create_worker_menu_keyboard()
    
    await callback.message.edit_text(
        text=text,
        reply_markup=keyboard
    )

# ❌ Неправильно - хардкод текстов
async def show_worker_menu(callback):
    await callback.message.edit_text(
        text="Меню рабочего",
        reply_markup=keyboard
    )
```

## 🔄 Стандарты FSM

### Именование состояний
```python
# ✅ Правильно - описательные имена
class AddWorkStates(StatesGroup):
    """Состояния для добавления записи о работе."""
    
    selecting_project = State()      # Выбор проекта
    selecting_work_type = State()    # Выбор типа работы
    entering_description = State()   # Ввод описания
    entering_quantity = State()      # Ввод количества
    confirming_entry = State()       # Подтверждение записи

# ❌ Неправильно - сокращённые имена
class AddWorkStates(StatesGroup):
    proj = State()
    type = State()
    desc = State()
    qty = State()
    conf = State()
```

### Обработка состояний
```python
# ✅ Правильно - полная обработка
@router.callback_query(F.data == "work:add")
@require_role("ROLE_WORKER", "ROLE_DIRECTOR", "ROLE_ADMIN")
async def start_add_work(
    callback: CallbackQuery,
    state: FSMContext,
    user: User,
    session: AsyncSession
):
    """Начинает процесс добавления записи о работе."""
    try:
        # Получаем доступные проекты
        project_service = ProjectService(session)
        projects = await project_service.get_projects_for_user(user)
        
        if not projects:
            await callback.answer(
                get_text("errors.no_projects", default="❌ Нет доступных проектов"),
                show_alert=True
            )
            return
        
        # Создаём клавиатуру с проектами
        keyboard = create_projects_keyboard(projects, "work:project")
        
        # Устанавливаем состояние
        await state.set_state(AddWorkStates.selecting_project)
        
        # Отправляем сообщение
        await callback.message.edit_text(
            text=get_text("work.select_project", default="🏗️ Выберите проект:"),
            reply_markup=keyboard
        )
        
        await callback.answer()
        logger.info(f"Пользователь {user.id} начал добавление работы")
        
    except Exception as e:
        logger.error(f"Ошибка в start_add_work для пользователя {user.id}: {e}")
        await callback.answer(
            get_text("common.error_occurred"),
            show_alert=True
        )

# ❌ Неправильно - минимальная обработка
@router.callback_query(F.data == "work:add")
async def start_add_work(callback, state):
    await state.set_state(AddWorkStates.selecting_project)
    await callback.message.edit_text("Выберите проект")
```

## 🧪 Стандарты тестирования

### Именование тестов
```python
# ✅ Правильно - описательные имена
class TestWorkService:
    """Тесты для сервиса работы с записями."""
    
    async def test_create_work_entry_success(self):
        """Тест успешного создания записи о работе."""
        pass
    
    async def test_create_work_entry_invalid_quantity_raises_error(self):
        """Тест создания записи с некорректным количеством вызывает ошибку."""
        pass
    
    async def test_get_user_work_entries_returns_only_user_entries(self):
        """Тест получения записей возвращает только записи пользователя."""
        pass

# ❌ Неправильно - неясные имена
class TestWorkService:
    async def test_create(self):
        pass
    
    async def test_invalid_qty(self):
        pass
    
    async def test_get_entries(self):
        pass
```

### Структура тестов
```python
# ✅ Правильно - AAA паттерн (Arrange, Act, Assert)
async def test_create_work_entry_success(self, work_service, test_user, test_project):
    """Тест успешного создания записи о работе."""
    # Arrange - подготовка данных
    work_data = {
        "project_id": test_project.id,
        "description": "Тестовая работа",
        "quantity": 8.0,
        "work_type": "hourly",
        "rate": 500.0,
        "date": date.today()
    }
    
    # Act - выполнение действия
    work_entry = await work_service.create_work_entry(
        user=test_user,
        **work_data
    )
    
    # Assert - проверка результата
    assert work_entry is not None
    assert work_entry.user_id == test_user.id
    assert work_entry.project_id == test_project.id
    assert work_entry.description == "Тестовая работа"
    assert float(work_entry.quantity) == 8.0
    assert float(work_entry.total_amount) == 4000.0  # 8 * 500

# ❌ Неправильно - всё в одной куче
async def test_create_work_entry(self):
    work_entry = await work_service.create_work_entry(user, project_id=1, description="test", quantity=8, work_type="hourly", rate=500, date=date.today())
    assert work_entry.quantity == 8
```

## 📊 Стандарты логирования

### Уровни логирования
```python
# ✅ Правильно - правильные уровни
logger.debug("Получены данные для создания записи: %s", work_data)
logger.info("Пользователь %s создал запись о работе %s", user.id, work_entry.id)
logger.warning("Пользователь %s пытается получить доступ к чужому проекту %s", user.id, project_id)
logger.error("Ошибка при создании записи о работе для пользователя %s: %s", user.id, str(e))
logger.critical("Критическая ошибка подключения к базе данных: %s", str(e))

# ❌ Неправильно - неправильные уровни
logger.error("Пользователь создал запись")  # Это info, не error
logger.info("Критическая ошибка БД")        # Это critical, не info
```

### Формат сообщений
```python
# ✅ Правильно - структурированные сообщения
logger.info(
    "Создана запись о работе: user_id=%s, project_id=%s, amount=%s",
    user.id, project.id, work_entry.total_amount
)

logger.error(
    "Ошибка валидации при создании записи: user_id=%s, error=%s, data=%s",
    user.id, str(e), work_data
)

# ❌ Неправильно - неструктурированные сообщения
logger.info(f"User {user.id} created work entry")
logger.error("Error: " + str(e))
```

## 🔒 Стандарты безопасности

### Валидация входных данных
```python
# ✅ Правильно - полная валидация
def validate_work_entry_data(
    description: str,
    quantity: float,
    rate: float,
    work_type: str
) -> None:
    """Валидирует данные записи о работе."""
    if not description or len(description.strip()) < 3:
        raise ValidationError("Описание должно содержать минимум 3 символа")
    
    if quantity <= 0 or quantity > 24:
        raise ValidationError("Количество должно быть от 0.1 до 24 часов")
    
    if rate <= 0:
        raise ValidationError("Ставка должна быть больше 0")
    
    if work_type not in ["hourly", "piece"]:
        raise ValidationError("Неверный тип работы")

# ❌ Неправильно - минимальная валидация
def validate_work_entry_data(description, quantity, rate, work_type):
    if not description:
        raise ValidationError("No description")
```

### Проверка прав доступа
```python
# ✅ Правильно - явная проверка прав
@require_role("ROLE_DIRECTOR", "ROLE_ADMIN")
async def create_project(
    callback: CallbackQuery,
    user: User,
    session: AsyncSession
):
    """Создаёт новый проект. Доступно только директорам и админам."""
    # Дополнительная проверка на уровне бизнес-логики
    if not user.company_id:
        raise InsufficientPermissionsError("Пользователь не привязан к компании")
    
    # Основная логика...

# ❌ Неправильно - отсутствие проверок
async def create_project(callback, user, session):
    # Создаём проект без проверки прав
    pass
```
