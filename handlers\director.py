"""
Обработчики команд директора (только Reply-клавиатуры)

Управление рабочими, отчеты, экспорт/импорт данных и типы работ.
"""
from aiogram import Router, types, F
from aiogram.filters import Command
from aiogram.fsm.context import FSMContext
from states import DirectorStates
from services.company_service import CompanyService
from services.token_service import TokenService
from services.auth_service import AuthService
from services.report_service import ReportService
from services.export_service import ExportService
from services.work_type_service import WorkTypeService
from services.import_service import ImportService
from middleware.rbac_middleware import require_permission, require_role
from keyboards.director import (
    create_director_menu, create_workers_management_keyboard,
    create_reports_keyboard, create_export_import_keyboard,
    create_work_types_keyboard, create_info_keyboard
)
from keyboards.common import create_back_keyboard, create_yes_no_keyboard

router = Router()


# ===== ФУНКЦИИ ДЛЯ REPLY-КЛАВИАТУР =====

async def show_companies_management(message: types.Message):
    """Показать меню управления компаниями"""
    from keyboards.director import create_companies_management_keyboard

    # Получаем информацию о компаниях пользователя
    user_id = message.from_user.id
    try:
        companies = await CompanyService.get_user_companies(user_id)
        active_companies = [c for c in companies if not c.get('is_deleted', False)]

        companies_info = ""
        if active_companies:
            companies_info = f"\n📊 <b>Ваши компании:</b> {len(active_companies)}\n"
            for i, company in enumerate(active_companies[:3], 1):
                status = "🟢" if company.get('is_active') else "⚪"
                companies_info += f"{i}. {status} {company['name']}\n"
            if len(active_companies) > 3:
                companies_info += f"... и ещё {len(active_companies) - 3}\n"
        else:
            companies_info = "\n💡 У вас пока нет компаний.\n"
    except Exception:
        companies_info = "\n⚠️ Ошибка загрузки данных.\n"

    keyboard = create_companies_management_keyboard()
    await message.answer(
        f"🏢 <b>Управление компаниями</b>{companies_info}\n"
        f"Выберите действие:",
        reply_markup=keyboard,
        parse_mode="HTML"
    )


async def show_workers_management(message: types.Message):
    """Показать меню управления рабочими"""
    from keyboards.director import create_workers_management_keyboard

    # Получаем информацию о рабочих
    user_id = message.from_user.id
    try:
        # Получаем активную компанию (заглушка - нужно реализовать)
        active_company_id = 999  # TODO: получать из user_data

        workers = await AuthService.get_company_workers(active_company_id)
        active_workers = [w for w in workers if w.get('is_active', True)]

        workers_info = ""
        if active_workers:
            workers_info = f"\n👷 <b>Активных рабочих:</b> {len(active_workers)}\n"
            for i, worker in enumerate(active_workers[:3], 1):
                workers_info += f"{i}. {worker.get('display_name', 'Без имени')}\n"
            if len(active_workers) > 3:
                workers_info += f"... и ещё {len(active_workers) - 3}\n"
        else:
            workers_info = "\n💡 В компании пока нет рабочих.\n"
    except Exception:
        workers_info = "\n⚠️ Ошибка загрузки данных.\n"

    keyboard = create_workers_management_keyboard()
    await message.answer(
        f"👷 <b>Управление рабочими</b>{workers_info}\n"
        f"Выберите действие:",
        reply_markup=keyboard,
        parse_mode="HTML"
    )


async def show_reports(message: types.Message):
    """Показать меню отчетов"""
    from keyboards.director import create_reports_keyboard

    # Получаем информацию для отчетов
    try:
        # TODO: Получить статистику для отчетов
        reports_info = (
            "\n📈 <b>Доступные отчеты:</b>\n"
            "• По датам - статистика за период\n"
            "• По рабочим - индивидуальная статистика\n"
            "• По проектам - сводка по проектам\n"
        )
    except Exception:
        reports_info = "\n⚠️ Ошибка загрузки данных.\n"

    keyboard = create_reports_keyboard()
    await message.answer(
        f"📊 <b>Просмотр отчётов</b>{reports_info}\n"
        f"Выберите тип отчета:",
        reply_markup=keyboard,
        parse_mode="HTML"
    )


async def show_export_import(message: types.Message):
    """Показать меню экспорта/импорта"""
    from keyboards.director import create_export_import_keyboard

    # Получаем информацию для экспорта/импорта
    try:
        export_info = (
            "\n📊 <b>Доступные форматы:</b>\n"
            "• Excel - настраиваемые столбцы и фильтры\n"
            "• PDF - часовые листы с фиксированным шаблоном\n"
            "• Импорт типов работ из Excel\n"
        )
    except Exception:
        export_info = "\n⚠️ Ошибка загрузки данных.\n"

    keyboard = create_export_import_keyboard()
    await message.answer(
        f"📁 <b>Экспорт / Импорт данных</b>{export_info}\n"
        f"Выберите действие:",
        reply_markup=keyboard,
        parse_mode="HTML"
    )


async def show_work_types_management(message: types.Message):
    """Показать меню управления типами работ"""
    from keyboards.director import create_work_types_keyboard

    # Получаем информацию о типах работ через WorkTypeService
    try:
        company_id = 999  # Виртуальная компания для суперпользователя
        stats = await WorkTypeService.get_work_types_statistics(company_id)

        work_types_info = (
            f"\n📊 <b>Статистика:</b>\n"
            f"• Активных типов работ: <b>{stats['active_count']}</b>\n"
            f"• Средняя ставка: <b>{stats['average_rate']:.2f} €</b>\n\n"
            f"🛠️ <b>Доступные действия:</b>\n"
            f"• Просмотр списка типов работ\n"
            f"• Редактирование существующих типов\n"
            f"• Добавление новых типов работ\n"
            f"• Импорт из Excel файла\n"
        )
    except Exception as e:
        work_types_info = f"\n⚠️ Ошибка загрузки данных: {str(e)}\n"

    keyboard = create_work_types_keyboard()
    await message.answer(
        f"🛠️ <b>Редактирование типов работ</b>{work_types_info}\n"
        f"Выберите действие:",
        reply_markup=keyboard,
        parse_mode="HTML"
    )


async def show_work_types(message: types.Message):
    """Показать меню типов работ (устаревшая функция)"""
    await show_work_types_management(message)


async def show_info(message: types.Message):
    """Показать информационную панель"""
    keyboard = create_info_keyboard()

    await message.answer(
        "ℹ️ <b>Информация</b>\n\n"
        "Информационная панель директора:",
        reply_markup=keyboard,
        parse_mode="HTML"
    )


async def show_director_menu(message: types.Message):
    """Показать главное меню директора"""
    keyboard = create_director_menu()

    await message.answer(
        "📋 <b>Панель директора</b>\n\n"
        "Добро пожаловать в панель управления!\n"
        "Выберите раздел:",
        reply_markup=keyboard,
        parse_mode="HTML"
    )


# ===== ОБРАБОТЧИКИ REPLY-КНОПОК =====

# === УПРАВЛЕНИЕ КОМПАНИЯМИ ===

@router.message(F.text == "➕ Добавить компанию")
async def handle_add_company_button(message: types.Message, state: FSMContext, **kwargs):
    """Обработчик кнопки 'Добавить компанию'"""
    from keyboards.common import create_cancel_keyboard

    await message.answer(
        "🏢 <b>Создание новой компании</b>\n\n"
        "Введите название компании (3-100 символов):",
        reply_markup=create_cancel_keyboard(),
        parse_mode="HTML"
    )
    await state.set_state(DirectorStates.waiting_company_name)


@router.message(F.text == "🔁 Сменить активную компанию")
async def handle_switch_company_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Сменить активную компанию'"""
    user_data = kwargs.get('user_data', {})
    user_id = message.from_user.id

    try:
        # Получаем компании пользователя
        companies = await CompanyService.get_user_companies(user_id)
        active_company_id = user_data.get('active_company_id')

        if len(companies) <= 1:
            await message.answer(
                "📋 <b>Смена компании</b>\n\n"
                "У вас только одна компания.\n"
                "Создайте дополнительные компании для переключения.",
                parse_mode="HTML"
            )
            return

        companies_text = ""
        for i, company in enumerate(companies, 1):
            status = "🟢 Активная" if company['id'] == active_company_id else "⚪"
            companies_text += f"{i}. {status} <b>{company['name']}</b>\n"

        await message.answer(
            f"🔁 <b>Смена активной компании</b>\n\n"
            f"Ваши компании:\n{companies_text}\n"
            f"🚧 Функция смены компании в разработке",
            parse_mode="HTML"
        )
    except Exception as e:
        await message.answer("❌ Ошибка при получении списка компаний")


@router.message(F.text == "🗑️ Удалить компанию (soft delete)")
async def handle_delete_company_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Удалить компанию'"""
    await message.answer("🚧 Функция удаления компании в разработке")


@router.message(F.text == "📋 Список компаний")
async def handle_companies_list_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Список компаний'"""
    user_id = message.from_user.id

    try:
        companies = await CompanyService.get_user_companies(user_id)
        active_companies = [c for c in companies if not c.get('is_deleted', False)]

        if not active_companies:
            await message.answer(
                "📋 <b>Список компаний</b>\n\n"
                "У вас пока нет компаний.\n\n"
                "➕ Создайте первую компанию!",
                parse_mode="HTML"
            )
            return

        companies_text = ""
        for i, company in enumerate(active_companies, 1):
            status = "🟢 Активная" if company.get('is_active') else "⚪ Неактивная"
            companies_text += (
                f"{i}. {status}\n"
                f"   🏢 <b>{company['name']}</b>\n"
                f"   🆔 ID: <code>{company['id']}</code>\n"
            )
            if company.get('address'):
                companies_text += f"   🏠 Адрес: {company['address']}\n"
            if company.get('business_id'):
                companies_text += f"   🆔 Бизнес ID: {company['business_id']}\n"
            companies_text += f"   📅 Создана: {company.get('created_at', 'Неизвестно')}\n\n"

        await message.answer(
            f"📋 <b>Список ваших компаний</b>\n\n"
            f"📊 Всего: <b>{len(active_companies)}</b>\n\n"
            f"{companies_text}",
            parse_mode="HTML"
        )

    except Exception as e:
        await message.answer("❌ Ошибка при получении списка компаний")


@router.message(F.text == "🔙 Назад")
async def handle_back_button(message: types.Message, state: FSMContext, **kwargs):
    """Обработчик кнопки 'Назад'"""
    # Очищаем состояние если есть
    await state.clear()
    # Возвращаемся в главное меню директора
    await show_director_menu(message)


# === УПРАВЛЕНИЕ РАБОЧИМИ ===

@router.message(F.text == "➕ Добавить рабочего")
async def handle_add_worker_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Добавить рабочего'"""
    user_data = kwargs.get('user_data', {})
    active_company_id = user_data.get('active_company_id')

    if not active_company_id:
        await message.answer(
            "❌ <b>Нет активной компании</b>\n\n"
            "Для добавления рабочих необходимо выбрать активную компанию.\n"
            "Перейдите в 'Управление компаниями' и создайте или выберите компанию.",
            parse_mode="HTML"
        )
        return

    try:
        # Создаем токен для регистрации рабочего
        token_data = await TokenService.create_token(
            role='worker',
            company_id=active_company_id,
            created_by=message.from_user.id,
            expires_in_hours=168  # 7 дней
        )

        if not token_data:
            await message.answer("❌ Ошибка при создании токена для рабочего")
            return

        # Формируем ссылку для регистрации
        bot_username = (await message.bot.get_me()).username
        registration_link = f"https://t.me/{bot_username}?start={token_data['token']}"

        await message.answer(
            f"🔗 <b>Токен для регистрации рабочего создан!</b>\n\n"
            f"📋 <b>Токен:</b> <code>{token_data['token']}</code>\n"
            f"⏰ <b>Действителен до:</b> {token_data['expires_at']}\n\n"
            f"🔗 <b>Ссылка для регистрации:</b>\n"
            f"{registration_link}\n\n"
            f"💡 Отправьте эту ссылку рабочему для регистрации в системе.",
            parse_mode="HTML"
        )

    except Exception as e:
        await message.answer(f"❌ Ошибка при создании токена: {str(e)}")


@router.message(F.text == "📋 Список рабочих")
async def handle_workers_list_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Список рабочих'"""
    user_data = kwargs.get('user_data', {})
    active_company_id = user_data.get('active_company_id')

    if not active_company_id:
        await message.answer(
            "❌ <b>Нет активной компании</b>\n\n"
            "Для просмотра рабочих необходимо выбрать активную компанию.",
            parse_mode="HTML"
        )
        return

    try:
        # Получаем рабочих компании
        workers = await AuthService.get_company_workers(active_company_id)

        if not workers:
            await message.answer(
                "👷 <b>Список рабочих</b>\n\n"
                "В вашей компании пока нет рабочих.\n\n"
                "💡 Используйте 'Добавить рабочего' для создания токена регистрации.",
                parse_mode="HTML"
            )
            return

        workers_text = ""
        for i, worker in enumerate(workers, 1):
            status = "🟢 Активен" if worker.get('is_active', True) else "🔴 Заблокирован"
            workers_text += (
                f"{i}. {status}\n"
                f"   👤 <b>{worker.get('display_name', 'Без имени')}</b>\n"
                f"   🆔 ID: <code>{worker['user_id']}</code>\n"
                f"   📅 Регистрация: {worker.get('created_at', 'Неизвестно')}\n"
            )

            # Показываем последнюю активность если есть
            if worker.get('last_activity'):
                workers_text += f"   ⏰ Последняя активность: {worker['last_activity']}\n"

            workers_text += "\n"

        await message.answer(
            f"👷 <b>Список рабочих компании</b>\n\n"
            f"📊 Всего рабочих: <b>{len(workers)}</b>\n\n"
            f"{workers_text}"
            f"💡 Для управления рабочим используйте команды в меню.",
            parse_mode="HTML"
        )

    except Exception as e:
        await message.answer("❌ Ошибка при получении списка рабочих")


@router.message(F.text == "👷 Управление рабочими")
async def handle_workers_management_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Управление рабочими'"""
    await show_workers_management(message)


@router.message(F.text == "🔗 Создать токен рабочего")
async def handle_create_worker_token_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Создать токен рабочего'"""
    # Дублирует функциональность "Добавить рабочего"
    await handle_add_worker_button(message, **kwargs)


@router.message(F.text == "📊 Просмотр отчётов")
async def handle_reports_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Просмотр отчётов'"""
    await show_reports(message)


@router.message(F.text == "📊 Отчёт по дате")
async def handle_date_report_button(message: types.Message, state: FSMContext, **kwargs):
    """Обработчик кнопки 'Отчёт по дате'"""
    from keyboards.common import create_cancel_keyboard

    await message.answer(
        "📅 <b>Отчёт по дате</b>\n\n"
        "Введите диапазон дат в формате:\n"
        "<code>01.01.2024-31.01.2024</code>\n\n"
        "Или выберите готовый период:\n"
        "• <b>сегодня</b> - за сегодняшний день\n"
        "• <b>вчера</b> - за вчерашний день\n"
        "• <b>неделя</b> - за последние 7 дней\n"
        "• <b>месяц</b> - за последние 30 дней",
        reply_markup=create_cancel_keyboard(),
        parse_mode="HTML"
    )
    await state.set_state(DirectorStates.selecting_report_period)


@router.message(F.text == "👷 Отчёт по рабочему")
async def handle_worker_report_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Отчёт по рабочему'"""
    user_data = kwargs.get('user_data', {})
    active_company_id = user_data.get('active_company_id', 999)

    try:
        # Получаем список рабочих компании
        workers = await AuthService.get_company_workers(active_company_id)

        if not workers:
            await message.answer(
                "👷 <b>Отчёт по рабочему</b>\n\n"
                "В компании нет рабочих для создания отчета.",
                parse_mode="HTML"
            )
            return

        workers_text = ""
        for i, worker in enumerate(workers[:10], 1):  # Показываем первых 10
            workers_text += f"{i}. {worker.get('display_name', 'Без имени')} (ID: {worker['user_id']})\n"

        if len(workers) > 10:
            workers_text += f"... и ещё {len(workers) - 10} рабочих\n"

        await message.answer(
            f"👷 <b>Отчёт по рабочему</b>\n\n"
            f"📊 Всего рабочих: <b>{len(workers)}</b>\n\n"
            f"<b>Список рабочих:</b>\n{workers_text}\n"
            f"💡 Для получения детального отчета по конкретному рабочему "
            f"будет реализована функция выбора из списка.",
            parse_mode="HTML"
        )

    except Exception as e:
        await message.answer("❌ Ошибка при получении списка рабочих для отчета")


@router.message(F.text == "🏗️ Отчёт по проекту")
async def handle_project_report_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Отчёт по проекту'"""
    user_data = kwargs.get('user_data', {})
    active_company_id = user_data.get('active_company_id', 999)

    try:
        # Получаем проекты компании (заглушка)
        # TODO: Реализовать ProjectService.get_company_projects()

        await message.answer(
            f"🏗️ <b>Отчёт по проекту</b>\n\n"
            f"🏢 <b>Компания:</b> ID {active_company_id}\n\n"
            f"📊 <b>Статистика проектов:</b>\n"
            f"• Активных проектов: <b>0</b>\n"
            f"• Завершенных проектов: <b>0</b>\n"
            f"• Общее время по всем проектам: <b>0.0 ч</b>\n"
            f"• Общая сумма: <b>0.00 €</b>\n\n"
            f"💡 Детальные данные будут доступны после реализации ProjectService.",
            parse_mode="HTML"
        )

    except Exception as e:
        await message.answer("❌ Ошибка при генерации отчета по проектам")


@router.message(F.text == "📁 Экспорт / Импорт данных")
async def handle_export_import_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Экспорт / Импорт данных'"""
    await show_export_import(message)


@router.message(F.text == "📤 Экспорт Excel")
async def handle_export_excel_button(message: types.Message, state: FSMContext, **kwargs):
    """Обработчик кнопки 'Экспорт Excel'"""
    user_data = kwargs.get('user_data', {})
    active_company_id = user_data.get('active_company_id')

    if not active_company_id:
        await message.answer(
            "❌ <b>Нет активной компании</b>\n\n"
            "Для экспорта данных необходимо выбрать активную компанию.",
            parse_mode="HTML"
        )
        return

    # Начинаем FSM сценарий экспорта Excel
    await state.update_data(export_format="Excel")
    await message.answer(
        "📊 <b>Экспорт в Excel</b>\n\n"
        "Выберите период для экспорта:\n"
        "• <b>сегодня</b> - за сегодняшний день\n"
        "• <b>неделя</b> - за последние 7 дней\n"
        "• <b>месяц</b> - за последние 30 дней\n"
        "• <b>01.01.2024-31.01.2024</b> - диапазон дат",
        parse_mode="HTML"
    )
    await state.set_state(DirectorStates.selecting_report_period)


@router.message(F.text == "📤 Экспорт PDF")
async def handle_export_pdf_button(message: types.Message, state: FSMContext, **kwargs):
    """Обработчик кнопки 'Экспорт PDF'"""
    user_data = kwargs.get('user_data', {})
    active_company_id = user_data.get('active_company_id')

    if not active_company_id:
        await message.answer(
            "❌ <b>Нет активной компании</b>\n\n"
            "Для экспорта данных необходимо выбрать активную компанию.",
            parse_mode="HTML"
        )
        return

    # Начинаем FSM сценарий экспорта PDF
    await state.update_data(export_format="PDF")
    await message.answer(
        "📄 <b>Экспорт в PDF</b>\n\n"
        "PDF экспорт создает часовые листы с фиксированным шаблоном.\n\n"
        "Выберите период для экспорта:\n"
        "• <b>сегодня</b> - за сегодняшний день\n"
        "• <b>неделя</b> - за последние 7 дней\n"
        "• <b>месяц</b> - за последние 30 дней\n"
        "• <b>01.01.2024-31.01.2024</b> - диапазон дат",
        parse_mode="HTML"
    )
    await state.set_state(DirectorStates.selecting_report_period)


@router.message(F.text == "📥 Импорт типов работ")
async def handle_import_work_types_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Импорт типов работ'"""
    user_data = kwargs.get('user_data', {})
    active_company_id = user_data.get('active_company_id')

    if not active_company_id:
        await message.answer(
            "❌ <b>Нет активной компании</b>\n\n"
            "Для импорта типов работ необходимо выбрать активную компанию.",
            parse_mode="HTML"
        )
        return

    await message.answer(
        "📥 <b>Импорт типов работ</b>\n\n"
        "📋 <b>Инструкция:</b>\n"
        "1. Подготовьте Excel файл с типами работ\n"
        "2. Формат: Название | Единица | Ставка\n"
        "3. Отправьте файл в чат\n\n"
        "📄 <b>Пример:</b>\n"
        "Монтаж | час | 25.50\n"
        "Демонтаж | м² | 15.00\n\n"
        "💡 Функция будет реализована после создания ImportService.",
        parse_mode="HTML"
    )


@router.message(F.text == "🛠️ Редактирование типов работ")
async def handle_work_types_management_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Редактирование типов работ'"""
    await show_work_types_management(message)


@router.message(F.text == "📋 Список типов работ")
async def handle_work_types_list_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Список типов работ'"""
    user_data = kwargs.get('user_data', {})
    active_company_id = user_data.get('active_company_id', 999)

    try:
        # Получаем типы работ компании через WorkTypeService
        work_types = await WorkTypeService.get_company_work_types(active_company_id)

        if not work_types:
            await message.answer(
                f"📋 <b>Список типов работ</b>\n\n"
                f"🏢 <b>Компания:</b> ID {active_company_id}\n\n"
                f"📊 В компании пока нет типов работ.\n"
                f"Создайте первый тип работы через меню редактирования!",
                parse_mode="HTML"
            )
            return

        work_types_text = ""
        for i, wt in enumerate(work_types, 1):
            status = "🟢" if not wt['is_deleted'] else "🔴"
            work_types_text += f"{status} <b>{wt['name']}</b> - {wt['rate']:.2f} €/{wt['unit']}\n"

        await message.answer(
            f"📋 <b>Список типов работ</b>\n\n"
            f"🏢 <b>Компания:</b> ID {active_company_id}\n\n"
            f"📊 <b>Типы работ ({len(work_types)}):</b>\n"
            f"{work_types_text}\n"
            f"🟢 - активный, 🔴 - удаленный\n\n"
            f"✅ Данные загружены из базы данных.",
            parse_mode="HTML"
        )

    except Exception as e:
        await message.answer(f"❌ Ошибка при получении списка типов работ: {str(e)}")


@router.message(F.text == "✏️ Редактировать типы работ")
async def handle_edit_work_types_button(message: types.Message, state: FSMContext, **kwargs):
    """Обработчик кнопки 'Редактировать типы работ'"""
    user_data = kwargs.get('user_data', {})
    active_company_id = user_data.get('active_company_id', 999)

    try:
        # Получаем типы работ для редактирования (заглушка)
        work_types = [
            {"id": 1, "name": "Монтаж", "unit": "час", "rate": 25.50},
            {"id": 2, "name": "Демонтаж", "unit": "м²", "rate": 20.00},
            {"id": 3, "name": "Покраска", "unit": "м²", "rate": 15.75}
        ]

        if not work_types:
            await message.answer(
                "📋 <b>Редактирование типов работ</b>\n\n"
                "В компании пока нет типов работ.\n"
                "Создайте первый тип работы!",
                parse_mode="HTML"
            )
            return

        work_types_text = ""
        for i, wt in enumerate(work_types, 1):
            work_types_text += f"{i}. <b>{wt['name']}</b> - {wt['rate']:.2f} €/{wt['unit']}\n"

        await message.answer(
            f"✏️ <b>Редактирование типов работ</b>\n\n"
            f"📋 <b>Доступные типы:</b>\n{work_types_text}\n"
            f"💡 Выберите номер типа работы для редактирования (1-{len(work_types)}):",
            parse_mode="HTML"
        )

        await state.update_data(work_types=work_types)
        await state.set_state(DirectorStates.selecting_work_type_to_edit)

    except Exception as e:
        await message.answer("❌ Ошибка при получении типов работ для редактирования")


@router.message(F.text == "➕ Добавить тип работы")
async def handle_add_work_type_button(message: types.Message, state: FSMContext, **kwargs):
    """Обработчик кнопки 'Добавить тип работы'"""
    from keyboards.common import create_cancel_keyboard

    await message.answer(
        "➕ <b>Добавление нового типа работы</b>\n\n"
        "Введите название типа работы (3-100 символов):",
        reply_markup=create_cancel_keyboard(),
        parse_mode="HTML"
    )
    await state.set_state(DirectorStates.waiting_work_type_name)


@router.message(F.text == "📥 Импорт из Excel")
async def handle_import_excel_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Импорт из Excel'"""
    # Дублирует функциональность "Импорт типов работ"
    await handle_import_work_types_button(message, **kwargs)


@router.message(F.text == "📤 Экспорт данных")
async def handle_export_data_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Экспорт данных'"""
    from keyboards.director import create_export_format_keyboard
    keyboard = create_export_format_keyboard()
    await message.answer(
        "📤 <b>Экспорт данных</b>\n\n"
        "Выберите формат экспорта:",
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.message(F.text == "📥 Импорт данных")
async def handle_import_data_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Импорт данных'"""
    # TODO: Реализовать импорт данных
    await message.answer("🚧 Функция импорта данных в разработке")


@router.message(F.text == "📊 Отчёты по времени")
async def handle_time_reports_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Отчёты по времени'"""
    # TODO: Реализовать отчёты по времени
    await message.answer("🚧 Функция отчётов по времени в разработке")


@router.message(F.text == "📈 Статистика работ")
async def handle_work_statistics_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Статистика работ'"""
    # TODO: Реализовать статистику работ
    await message.answer("🚧 Функция статистики работ в разработке")


@router.message(F.text == "🏢 Управление компаниями")
async def handle_companies_management_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Управление компаниями'"""
    await show_companies_management(message)


@router.message(F.text == "🛠️ Настройки проектов")
async def handle_project_settings_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Настройки проектов'"""
    # TODO: Реализовать настройки проектов
    await message.answer("🚧 Функция настроек проектов в разработке")


@router.message(F.text == "👤 Мой профиль")
async def handle_my_profile_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Мой профиль'"""
    # TODO: Реализовать профиль
    await message.answer("🚧 Функция профиля в разработке")


@router.message(F.text == "⚙️ Настройки")
async def handle_settings_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Настройки'"""
    # TODO: Реализовать настройки
    await message.answer("🚧 Функция настроек в разработке")


@router.message(F.text == "📋 Информационная панель")
async def handle_info_panel_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Информационная панель'"""
    keyboard = create_director_menu()
    await message.answer(
        "ℹ️ <b>Информационная панель директора</b>\n\n"
        "👤 <b>Профиль:</b> Директор\n"
        "🏢 <b>Компания:</b> Тестовая компания\n"
        "👷 <b>Рабочих:</b> 0\n"
        "📊 <b>Проектов:</b> 0\n\n"
        "📅 <b>Последнее действие:</b> сейчас\n"
        "🤖 <b>Версия:</b> v2.0 MVP",
        reply_markup=keyboard,
        parse_mode="HTML"
    )


# ===== FSM ОБРАБОТЧИКИ ДЛЯ УПРАВЛЕНИЯ КОМПАНИЯМИ =====

@router.message(DirectorStates.waiting_company_name)
async def process_company_name(message: types.Message, state: FSMContext, **kwargs):
    """Обработка ввода названия компании"""
    from keyboards.common import create_cancel_keyboard

    if message.text.lower() == "отмена":
        await state.clear()
        await show_companies_management(message)
        return

    company_name = message.text.strip()

    # Валидация названия
    if not company_name:
        await message.answer("❌ Название компании не может быть пустым.")
        return

    if len(company_name) < 3:
        await message.answer("❌ Название компании должно содержать минимум 3 символа.")
        return

    if len(company_name) > 100:
        await message.answer("❌ Название компании не должно превышать 100 символов.")
        return

    # Проверяем уникальность названия
    user_id = message.from_user.id
    try:
        existing_company = await CompanyService.get_company_by_name(user_id, company_name)
        if existing_company:
            await message.answer("❌ Компания с таким названием уже существует.")
            return
    except Exception as e:
        await message.answer("❌ Ошибка при проверке названия компании.")
        return

    await state.update_data(company_name=company_name)
    await message.answer(
        "🏠 <b>Адрес компании</b>\n\n"
        "Введите адрес компании (необязательно):\n"
        "Можете написать 'пропустить' для пропуска.",
        reply_markup=create_cancel_keyboard(),
        parse_mode="HTML"
    )
    await state.set_state(DirectorStates.waiting_company_address)


@router.message(DirectorStates.waiting_company_address)
async def process_company_address(message: types.Message, state: FSMContext, **kwargs):
    """Обработка ввода адреса компании"""
    from keyboards.common import create_cancel_keyboard

    if message.text.lower() == "отмена":
        await state.clear()
        await show_companies_management(message)
        return

    address = message.text.strip()
    if address.lower() == "пропустить":
        address = None
    elif len(address) > 255:
        await message.answer("❌ Адрес не должен превышать 255 символов.")
        return

    await state.update_data(company_address=address)
    await message.answer(
        "🆔 <b>Бизнес ID</b>\n\n"
        "Введите бизнес ID компании (Y-tunnus в Финляндии):\n"
        "Можете написать 'пропустить' для пропуска.",
        reply_markup=create_cancel_keyboard(),
        parse_mode="HTML"
    )
    await state.set_state(DirectorStates.waiting_business_id)


@router.message(DirectorStates.waiting_business_id)
async def process_business_id(message: types.Message, state: FSMContext, **kwargs):
    """Обработка ввода бизнес ID"""
    from keyboards.common import create_yes_no_keyboard

    if message.text.lower() == "отмена":
        await state.clear()
        await show_companies_management(message)
        return

    business_id = message.text.strip()
    if business_id.lower() == "пропустить":
        business_id = None
    elif len(business_id) > 50:
        await message.answer("❌ Бизнес ID не должен превышать 50 символов.")
        return

    await state.update_data(business_id=business_id)

    # Показываем сводку для подтверждения
    data = await state.get_data()
    company_name = data.get('company_name')
    company_address = data.get('company_address', 'Не указан')
    business_id_display = business_id or 'Не указан'

    await message.answer(
        f"✅ <b>Подтверждение создания компании</b>\n\n"
        f"📋 <b>Название:</b> {company_name}\n"
        f"🏠 <b>Адрес:</b> {company_address}\n"
        f"🆔 <b>Бизнес ID:</b> {business_id_display}\n\n"
        f"Создать компанию?",
        reply_markup=create_yes_no_keyboard(),
        parse_mode="HTML"
    )
    await state.set_state(DirectorStates.confirming_company_creation)


@router.message(DirectorStates.confirming_company_creation)
async def process_company_creation_confirmation(message: types.Message, state: FSMContext, **kwargs):
    """Подтверждение создания компании"""
    if message.text.lower() == "отмена":
        await state.clear()
        await show_companies_management(message)
        return

    if message.text.lower() not in ["да", "yes"]:
        if message.text.lower() in ["нет", "no"]:
            await state.clear()
            await message.answer("❌ Создание компании отменено.")
            await show_companies_management(message)
            return
        else:
            await message.answer("Пожалуйста, ответьте 'Да' или 'Нет'.")
            return

    # Создаем компанию
    data = await state.get_data()
    user_id = message.from_user.id

    try:
        company = await CompanyService.create_company(
            name=data.get('company_name'),
            address=data.get('company_address'),
            business_id=data.get('business_id'),
            created_by=user_id
        )

        await state.clear()
        await message.answer(
            f"🎉 <b>Компания успешно создана!</b>\n\n"
            f"📋 <b>Название:</b> {company['name']}\n"
            f"🆔 <b>ID:</b> {company['id']}\n\n"
            f"Компания автоматически установлена как активная.",
            parse_mode="HTML"
        )
        await show_companies_management(message)

    except Exception as e:
        await message.answer(f"❌ Ошибка при создании компании: {str(e)}")
        await state.clear()
        await show_companies_management(message)


# ===== FSM ОБРАБОТЧИКИ ДЛЯ ОТЧЕТОВ =====

@router.message(DirectorStates.selecting_report_period)
async def process_report_period(message: types.Message, state: FSMContext, **kwargs):
    """Обработка выбора периода для отчета"""
    from datetime import datetime, timedelta

    if message.text.lower() == "отмена":
        await state.clear()
        await show_reports(message)
        return

    text = message.text.lower().strip()
    start_date = None
    end_date = None

    try:
        # Обработка готовых периодов
        if text == "сегодня":
            start_date = end_date = datetime.now().date()
        elif text == "вчера":
            yesterday = datetime.now().date() - timedelta(days=1)
            start_date = end_date = yesterday
        elif text == "неделя":
            end_date = datetime.now().date()
            start_date = end_date - timedelta(days=7)
        elif text == "месяц":
            end_date = datetime.now().date()
            start_date = end_date - timedelta(days=30)
        else:
            # Обработка диапазона дат
            if "-" in text:
                date_parts = text.split("-")
                if len(date_parts) == 2:
                    start_str, end_str = date_parts
                    start_date = datetime.strptime(start_str.strip(), "%d.%m.%Y").date()
                    end_date = datetime.strptime(end_str.strip(), "%d.%m.%Y").date()
                else:
                    raise ValueError("Неверный формат диапазона")
            else:
                # Одна дата
                start_date = end_date = datetime.strptime(text, "%d.%m.%Y").date()

        if start_date > end_date:
            await message.answer("❌ Начальная дата не может быть больше конечной.")
            return

        # Проверяем, это отчет или экспорт
        data = await state.get_data()
        export_format = data.get('export_format')

        if export_format:
            # Это экспорт
            await generate_export_file(message, start_date, end_date, export_format, **kwargs)
        else:
            # Это отчет
            await generate_date_report(message, start_date, end_date, **kwargs)

        await state.clear()

    except ValueError as e:
        await message.answer(
            "❌ Неверный формат даты.\n\n"
            "Используйте:\n"
            "• <code>01.01.2024</code> - одна дата\n"
            "• <code>01.01.2024-31.01.2024</code> - диапазон\n"
            "• <b>сегодня</b>, <b>вчера</b>, <b>неделя</b>, <b>месяц</b>",
            parse_mode="HTML"
        )


async def generate_date_report(message: types.Message, start_date, end_date, **kwargs):
    """Генерация отчета по датам"""
    user_data = kwargs.get('user_data', {})
    active_company_id = user_data.get('active_company_id', 999)  # Виртуальная компания для суперпользователя

    try:
        # Получаем данные для отчета через ReportService
        report_data = await ReportService.get_date_report(
            company_id=active_company_id,
            start_date=start_date,
            end_date=end_date
        )

        period_text = ""
        if start_date == end_date:
            period_text = f"за {start_date.strftime('%d.%m.%Y')}"
        else:
            period_text = f"с {start_date.strftime('%d.%m.%Y')} по {end_date.strftime('%d.%m.%Y')}"

        # Извлекаем данные из отчета
        summary = report_data.get('summary', {})
        total_entries = summary.get('total_entries', 0)
        total_hours = summary.get('total_hours', 0.0)
        total_amount = summary.get('total_amount', 0.0)
        workers_count = summary.get('workers_count', 0)
        projects_count = summary.get('projects_count', 0)
        work_types_count = summary.get('work_types_count', 0)

        # Формируем детальную информацию
        details_text = ""
        if total_entries > 0:
            details_text = (f"\n📋 <b>Детали:</b>\n"
                          f"• Проектов: <b>{projects_count}</b>\n"
                          f"• Типов работ: <b>{work_types_count}</b>\n")

            # Топ рабочих
            workers = report_data.get('workers', {})
            if workers:
                top_workers = sorted(workers.items(), key=lambda x: x[1]['hours'], reverse=True)[:3]
                details_text += f"\n👷 <b>Топ рабочих:</b>\n"
                for worker_id, worker_data in top_workers:
                    details_text += f"• {worker_data['name']}: {worker_data['hours']:.1f} ч\n"

        await message.answer(
            f"📊 <b>Отчёт по дате</b>\n\n"
            f"📅 <b>Период:</b> {period_text}\n"
            f"🏢 <b>Компания:</b> ID {active_company_id}\n\n"
            f"📈 <b>Статистика:</b>\n"
            f"• Записей работ: <b>{total_entries}</b>\n"
            f"• Общее время: <b>{total_hours:.1f} ч</b>\n"
            f"• Общая сумма: <b>{total_amount:.2f} €</b>\n"
            f"• Рабочих участвовало: <b>{workers_count}</b>\n"
            f"{details_text}\n"
            f"✅ Отчет сгенерирован через ReportService.",
            parse_mode="HTML"
        )

    except Exception as e:
        await message.answer(f"❌ Ошибка при генерации отчета: {str(e)}")


async def generate_export_file(message: types.Message, start_date, end_date, export_format, **kwargs):
    """Генерация файла экспорта"""
    user_data = kwargs.get('user_data', {})
    active_company_id = user_data.get('active_company_id', 999)

    try:
        period_text = ""
        if start_date == end_date:
            period_text = f"за {start_date.strftime('%d.%m.%Y')}"
        else:
            period_text = f"с {start_date.strftime('%d.%m.%Y')} по {end_date.strftime('%d.%m.%Y')}"

        await message.answer("⏳ Генерирую файл экспорта...")

        # Получаем данные для экспорта через ReportService
        report_data = await ReportService.get_date_report(
            company_id=active_company_id,
            start_date=start_date,
            end_date=end_date
        )

        # Проверяем доступность форматов
        available_formats = ExportService.get_available_formats()

        if export_format == "Excel":
            if not available_formats['excel']:
                await message.answer(
                    "❌ <b>Excel экспорт недоступен</b>\n\n"
                    "Для экспорта в Excel необходимо установить библиотеку openpyxl:\n"
                    "<code>pip install openpyxl</code>",
                    parse_mode="HTML"
                )
                return

            # Генерируем Excel файл
            file_path = await ExportService.export_to_excel(
                report_data=report_data,
                filename_prefix=f"worklog_report_{active_company_id}"
            )

            if file_path:
                await message.answer(
                    f"📊 <b>Экспорт Excel завершен</b>\n\n"
                    f"📅 <b>Период:</b> {period_text}\n"
                    f"🏢 <b>Компания:</b> ID {active_company_id}\n\n"
                    f"✅ Файл создан: <code>{file_path}</code>\n"
                    f"📋 Записей: {report_data.get('summary', {}).get('total_entries', 0)}",
                    parse_mode="HTML"
                )
            else:
                await message.answer("❌ Ошибка при создании Excel файла")

        elif export_format == "PDF":
            if not available_formats['pdf']:
                await message.answer(
                    "❌ <b>PDF экспорт недоступен</b>\n\n"
                    "Для экспорта в PDF необходимо установить библиотеку reportlab:\n"
                    "<code>pip install reportlab</code>",
                    parse_mode="HTML"
                )
                return

            # Генерируем PDF файл
            file_path = await ExportService.export_to_pdf(
                report_data=report_data,
                filename_prefix=f"worklog_report_{active_company_id}",
                company_name="TestCompany"
            )

            if file_path:
                await message.answer(
                    f"📄 <b>Экспорт PDF завершен</b>\n\n"
                    f"📅 <b>Период:</b> {period_text}\n"
                    f"🏢 <b>Компания:</b> ID {active_company_id}\n\n"
                    f"✅ Файл создан: <code>{file_path}</code>\n"
                    f"📋 Записей: {report_data.get('summary', {}).get('total_entries', 0)}",
                    parse_mode="HTML"
                )
            else:
                await message.answer("❌ Ошибка при создании PDF файла")

    except Exception as e:
        await message.answer(f"❌ Ошибка при генерации файла экспорта: {str(e)}")


# ===== FSM ОБРАБОТЧИКИ ДЛЯ ТИПОВ РАБОТ =====

@router.message(DirectorStates.selecting_work_type_to_edit)
async def process_work_type_selection(message: types.Message, state: FSMContext, **kwargs):
    """Обработка выбора типа работы для редактирования"""
    if message.text.lower() == "отмена":
        await state.clear()
        await show_work_types_management(message)
        return

    try:
        selection = int(message.text.strip())
        data = await state.get_data()
        work_types = data.get('work_types', [])

        if selection < 1 or selection > len(work_types):
            await message.answer(f"❌ Выберите номер от 1 до {len(work_types)}")
            return

        selected_work_type = work_types[selection - 1]
        await state.update_data(selected_work_type=selected_work_type)

        await message.answer(
            f"✏️ <b>Редактирование типа работы</b>\n\n"
            f"📋 <b>Выбран:</b> {selected_work_type['name']}\n"
            f"📏 <b>Единица:</b> {selected_work_type['unit']}\n"
            f"💰 <b>Ставка:</b> {selected_work_type['rate']:.2f} €\n\n"
            f"Что хотите изменить?\n"
            f"• <b>название</b> - изменить название\n"
            f"• <b>единица</b> - изменить единицу измерения\n"
            f"• <b>ставка</b> - изменить ставку\n"
            f"• <b>удалить</b> - удалить тип работы",
            parse_mode="HTML"
        )
        await state.set_state(DirectorStates.editing_work_type_field)

    except ValueError:
        await message.answer("❌ Введите номер типа работы (число)")


@router.message(DirectorStates.editing_work_type_field)
async def process_work_type_field_edit(message: types.Message, state: FSMContext, **kwargs):
    """Обработка выбора поля для редактирования"""
    if message.text.lower() == "отмена":
        await state.clear()
        await show_work_types_management(message)
        return

    field = message.text.lower().strip()
    data = await state.get_data()
    selected_work_type = data.get('selected_work_type')

    if field == "название":
        await message.answer(
            f"✏️ <b>Изменение названия</b>\n\n"
            f"Текущее название: <b>{selected_work_type['name']}</b>\n\n"
            f"Введите новое название:",
            parse_mode="HTML"
        )
        await state.update_data(editing_field="name")
        await state.set_state(DirectorStates.waiting_work_type_name)

    elif field == "единица":
        await message.answer(
            f"📏 <b>Изменение единицы измерения</b>\n\n"
            f"Текущая единица: <b>{selected_work_type['unit']}</b>\n\n"
            f"Введите новую единицу (час, м², шт, кг и т.д.):",
            parse_mode="HTML"
        )
        await state.update_data(editing_field="unit")
        await state.set_state(DirectorStates.waiting_work_type_unit)

    elif field == "ставка":
        await message.answer(
            f"💰 <b>Изменение ставки</b>\n\n"
            f"Текущая ставка: <b>{selected_work_type['rate']:.2f} €</b>\n\n"
            f"Введите новую ставку (число с точкой, например: 25.50):",
            parse_mode="HTML"
        )
        await state.update_data(editing_field="rate")
        await state.set_state(DirectorStates.waiting_work_type_rate)

    elif field == "удалить":
        await message.answer(
            f"🗑️ <b>Удаление типа работы</b>\n\n"
            f"Вы действительно хотите удалить тип работы:\n"
            f"<b>{selected_work_type['name']}</b> ({selected_work_type['rate']:.2f} €/{selected_work_type['unit']})\n\n"
            f"Ответьте 'Да' для подтверждения или 'Нет' для отмены:",
            parse_mode="HTML"
        )
        await state.set_state(DirectorStates.confirming_work_type_deletion)

    else:
        await message.answer(
            "❌ Неверный выбор.\n\n"
            "Выберите одно из:\n"
            "• <b>название</b>\n"
            "• <b>единица</b>\n"
            "• <b>ставка</b>\n"
            "• <b>удалить</b>",
            parse_mode="HTML"
        )


@router.message(DirectorStates.waiting_work_type_name)
async def process_work_type_name_input(message: types.Message, state: FSMContext, **kwargs):
    """Обработка ввода названия типа работы (создание или редактирование)"""
    if message.text.lower() == "отмена":
        await state.clear()
        await show_work_types_management(message)
        return

    new_name = message.text.strip()

    if not new_name:
        await message.answer("❌ Название не может быть пустым.")
        return

    if len(new_name) < 3:
        await message.answer("❌ Название должно содержать минимум 3 символа.")
        return

    if len(new_name) > 100:
        await message.answer("❌ Название не должно превышать 100 символов.")
        return

    data = await state.get_data()
    editing_field = data.get('editing_field')

    if editing_field == "name":
        # Редактирование существующего типа
        await message.answer(
            f"✅ <b>Название изменено!</b>\n\n"
            f"Новое название: <b>{new_name}</b>\n\n"
            f"💡 Изменения будут сохранены после реализации WorkTypeService.",
            parse_mode="HTML"
        )
        await state.clear()
        await show_work_types_management(message)
    else:
        # Создание нового типа
        await state.update_data(work_type_name=new_name)
        await message.answer(
            f"📏 <b>Единица измерения</b>\n\n"
            f"Название: <b>{new_name}</b>\n\n"
            f"Введите единицу измерения (час, м², шт, кг и т.д.):",
            parse_mode="HTML"
        )
        await state.set_state(DirectorStates.waiting_work_type_unit)


@router.message(DirectorStates.waiting_work_type_unit)
async def process_work_type_unit_input(message: types.Message, state: FSMContext, **kwargs):
    """Обработка ввода единицы измерения (создание или редактирование)"""
    if message.text.lower() == "отмена":
        await state.clear()
        await show_work_types_management(message)
        return

    new_unit = message.text.strip()

    if not new_unit:
        await message.answer("❌ Единица измерения не может быть пустой.")
        return

    if len(new_unit) > 20:
        await message.answer("❌ Единица измерения не должна превышать 20 символов.")
        return

    data = await state.get_data()
    editing_field = data.get('editing_field')

    if editing_field == "unit":
        # Редактирование существующего типа
        await message.answer(
            f"✅ <b>Единица измерения изменена!</b>\n\n"
            f"Новая единица: <b>{new_unit}</b>\n\n"
            f"💡 Изменения будут сохранены после реализации WorkTypeService.",
            parse_mode="HTML"
        )
        await state.clear()
        await show_work_types_management(message)
    else:
        # Создание нового типа
        work_type_name = data.get('work_type_name')
        await state.update_data(work_type_unit=new_unit)
        await message.answer(
            f"💰 <b>Ставка</b>\n\n"
            f"Название: <b>{work_type_name}</b>\n"
            f"Единица: <b>{new_unit}</b>\n\n"
            f"Введите ставку (число с точкой, например: 25.50):",
            parse_mode="HTML"
        )
        await state.set_state(DirectorStates.waiting_work_type_rate)


@router.message(DirectorStates.waiting_work_type_rate)
async def process_work_type_rate_input(message: types.Message, state: FSMContext, **kwargs):
    """Обработка ввода ставки (создание или редактирование)"""
    if message.text.lower() == "отмена":
        await state.clear()
        await show_work_types_management(message)
        return

    try:
        new_rate = float(message.text.strip().replace(',', '.'))

        if new_rate <= 0:
            await message.answer("❌ Ставка должна быть больше нуля.")
            return

        if new_rate > 1000:
            await message.answer("❌ Ставка не должна превышать 1000 €.")
            return

        data = await state.get_data()
        editing_field = data.get('editing_field')

        if editing_field == "rate":
            # Редактирование существующего типа
            await message.answer(
                f"✅ <b>Ставка изменена!</b>\n\n"
                f"Новая ставка: <b>{new_rate:.2f} €</b>\n\n"
                f"💡 Изменения будут сохранены после реализации WorkTypeService.",
                parse_mode="HTML"
            )
            await state.clear()
            await show_work_types_management(message)
        else:
            # Создание нового типа через WorkTypeService
            work_type_name = data.get('work_type_name')
            work_type_unit = data.get('work_type_unit')
            company_id = 999  # Виртуальная компания для суперпользователя
            created_by = message.from_user.id

            # Валидируем данные
            validation = await WorkTypeService.validate_work_type_data(
                work_type_name, work_type_unit, new_rate
            )

            if not validation['is_valid']:
                await message.answer(
                    f"❌ <b>Ошибка валидации:</b>\n\n"
                    f"{'• ' + chr(10).join(validation['errors'])}",
                    parse_mode="HTML"
                )
                return

            # Создаем тип работы
            result = await WorkTypeService.create_work_type(
                company_id=company_id,
                name=validation['cleaned_data']['name'],
                unit=validation['cleaned_data']['unit'],
                rate=validation['cleaned_data']['rate'],
                created_by=created_by
            )

            if result:
                await message.answer(
                    f"✅ <b>Новый тип работы создан!</b>\n\n"
                    f"📋 <b>Название:</b> {result['name']}\n"
                    f"📏 <b>Единица:</b> {result['unit']}\n"
                    f"💰 <b>Ставка:</b> {result['rate']:.2f} €\n"
                    f"🆔 <b>ID:</b> {result['id']}\n\n"
                    f"✅ Тип работы успешно сохранен в базе данных.",
                    parse_mode="HTML"
                )
            else:
                await message.answer(
                    f"❌ <b>Ошибка создания типа работы</b>\n\n"
                    f"Возможно, тип работы с названием '{work_type_name}' уже существует.",
                    parse_mode="HTML"
                )

            await state.clear()
            await show_work_types_management(message)

    except ValueError:
        await message.answer("❌ Введите корректную ставку (число, например: 25.50)")


@router.message(DirectorStates.confirming_work_type_deletion)
async def process_work_type_deletion_confirmation(message: types.Message, state: FSMContext, **kwargs):
    """Подтверждение удаления типа работы"""
    if message.text.lower() == "отмена":
        await state.clear()
        await show_work_types_management(message)
        return

    response = message.text.lower().strip()

    if response in ["да", "yes"]:
        data = await state.get_data()
        selected_work_type = data.get('selected_work_type')

        # TODO: Удалить через WorkTypeService
        await message.answer(
            f"✅ <b>Тип работы удален!</b>\n\n"
            f"Удален: <b>{selected_work_type['name']}</b>\n\n"
            f"💡 Изменения будут сохранены после реализации WorkTypeService.",
            parse_mode="HTML"
        )

        await state.clear()
        await show_work_types_management(message)

    elif response in ["нет", "no"]:
        await message.answer("❌ Удаление отменено.")
        await state.clear()
        await show_work_types_management(message)

    else:
        await message.answer("Пожалуйста, ответьте 'Да' или 'Нет'.")
