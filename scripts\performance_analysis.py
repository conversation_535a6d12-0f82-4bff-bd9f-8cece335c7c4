"""
Анализ производительности и оптимизация базы данных

Проверяет производительность запросов и предлагает оптимизации.
"""
import asyncio
import sys
import time
from pathlib import Path
from datetime import date, timedelta

# Добавляем корневую директорию в путь
sys.path.append(str(Path(__file__).parent.parent))

from db.database import async_session, engine
from db.models import WorkEntry, User, Project, WorkType, Company, UserCompanyRole
from services.report_service import ReportService
from services.work_type_service import WorkTypeService
from sqlalchemy import select, text, func, and_
from sqlalchemy.orm import joinedload


class PerformanceAnalyzer:
    """Анализатор производительности"""

    def __init__(self):
        self.results = []

    async def run_analysis(self):
        """Запуск полного анализа производительности"""
        print("🔍 АНАЛИЗ ПРОИЗВОДИТЕЛЬНОСТИ БАЗЫ ДАННЫХ")
        print("=" * 50)
        
        await self.analyze_database_structure()
        await self.analyze_query_performance()
        await self.analyze_service_performance()
        await self.suggest_optimizations()
        
        print("\n✅ Анализ производительности завершен!")

    async def analyze_database_structure(self):
        """Анализ структуры базы данных"""
        print("\n📊 АНАЛИЗ СТРУКТУРЫ БАЗЫ ДАННЫХ")
        print("-" * 30)
        
        async with async_session() as session:
            # Проверяем размеры таблиц
            tables = [
                ('users', 'user_id'),
                ('companies', 'id'),
                ('projects', 'project_id'),
                ('work_types', 'id'),
                ('work_entries', 'id'),
                ('user_company_roles', 'id')
            ]
            
            for table_name, pk_column in tables:
                try:
                    result = await session.execute(
                        text(f"SELECT COUNT(*) FROM {table_name}")
                    )
                    count = result.scalar()
                    print(f"📋 {table_name}: {count} записей")
                except Exception as e:
                    print(f"❌ Ошибка при анализе таблицы {table_name}: {e}")

    async def analyze_query_performance(self):
        """Анализ производительности запросов"""
        print("\n⚡ АНАЛИЗ ПРОИЗВОДИТЕЛЬНОСТИ ЗАПРОСОВ")
        print("-" * 35)
        
        # Тест 1: Простой запрос пользователей
        await self.time_query(
            "Получение всех пользователей",
            lambda session: session.execute(select(User))
        )
        
        # Тест 2: Запрос с JOIN
        await self.time_query(
            "Получение записей работ с JOIN",
            lambda session: session.execute(
                select(WorkEntry).options(
                    joinedload(WorkEntry.user),
                    joinedload(WorkEntry.project),
                    joinedload(WorkEntry.work_type)
                ).limit(100)
            )
        )
        
        # Тест 3: Агрегация данных
        await self.time_query(
            "Агрегация записей работ",
            lambda session: session.execute(
                select(
                    func.count(WorkEntry.id),
                    func.sum(WorkEntry.hours),
                    func.sum(WorkEntry.total_amount)
                ).where(WorkEntry.is_deleted == False)
            )
        )
        
        # Тест 4: Фильтрация по дате
        start_date = date.today() - timedelta(days=30)
        end_date = date.today()
        
        await self.time_query(
            "Фильтрация по дате (30 дней)",
            lambda session: session.execute(
                select(WorkEntry).where(
                    and_(
                        WorkEntry.work_date >= start_date,
                        WorkEntry.work_date <= end_date,
                        WorkEntry.is_deleted == False
                    )
                )
            )
        )

    async def time_query(self, description: str, query_func):
        """Измерение времени выполнения запроса"""
        async with async_session() as session:
            try:
                start_time = time.time()
                result = await query_func(session)
                end_time = time.time()
                
                execution_time = (end_time - start_time) * 1000  # в миллисекундах
                
                # Получаем количество результатов
                try:
                    if hasattr(result, 'scalars'):
                        rows = len(result.scalars().all())
                    elif hasattr(result, 'first'):
                        first_result = result.first()
                        rows = 1 if first_result else 0
                    else:
                        rows = "N/A"
                except:
                    rows = "N/A"
                
                print(f"⏱️  {description}: {execution_time:.2f}ms ({rows} строк)")
                
                self.results.append({
                    'query': description,
                    'time_ms': execution_time,
                    'rows': rows
                })
                
            except Exception as e:
                print(f"❌ Ошибка в запросе '{description}': {e}")

    async def analyze_service_performance(self):
        """Анализ производительности сервисов"""
        print("\n🔧 АНАЛИЗ ПРОИЗВОДИТЕЛЬНОСТИ СЕРВИСОВ")
        print("-" * 35)
        
        # Тест ReportService
        start_time = time.time()
        try:
            report = await ReportService.get_date_report(
                company_id=1,
                start_date=date.today() - timedelta(days=7),
                end_date=date.today()
            )
            end_time = time.time()
            execution_time = (end_time - start_time) * 1000
            print(f"📊 ReportService.get_date_report: {execution_time:.2f}ms")
            print(f"   Записей в отчете: {report['summary']['total_entries']}")
        except Exception as e:
            print(f"❌ Ошибка в ReportService: {e}")
        
        # Тест WorkTypeService
        start_time = time.time()
        try:
            work_types = await WorkTypeService.get_company_work_types(1)
            end_time = time.time()
            execution_time = (end_time - start_time) * 1000
            print(f"🛠️  WorkTypeService.get_company_work_types: {execution_time:.2f}ms")
            print(f"   Типов работ: {len(work_types)}")
        except Exception as e:
            print(f"❌ Ошибка в WorkTypeService: {e}")

    async def suggest_optimizations(self):
        """Предложения по оптимизации"""
        print("\n💡 РЕКОМЕНДАЦИИ ПО ОПТИМИЗАЦИИ")
        print("-" * 30)
        
        # Анализируем результаты
        slow_queries = [r for r in self.results if r['time_ms'] > 100]
        
        if slow_queries:
            print("⚠️  Медленные запросы (>100ms):")
            for query in slow_queries:
                print(f"   • {query['query']}: {query['time_ms']:.2f}ms")
        else:
            print("✅ Все запросы выполняются быстро (<100ms)")
        
        print("\n📋 ОБЩИЕ РЕКОМЕНДАЦИИ:")
        
        # Проверяем наличие индексов
        await self.check_indexes()
        
        print("\n🔧 РЕКОМЕНДУЕМЫЕ ОПТИМИЗАЦИИ:")
        print("1. Добавить индексы на часто используемые поля")
        print("2. Использовать пагинацию для больших результатов")
        print("3. Кэшировать часто запрашиваемые данные")
        print("4. Оптимизировать JOIN запросы")
        print("5. Использовать EXPLAIN ANALYZE для анализа планов запросов")

    async def check_indexes(self):
        """Проверка существующих индексов"""
        print("\n🔍 АНАЛИЗ ИНДЕКСОВ:")
        
        async with async_session() as session:
            try:
                # Получаем информацию об индексах
                result = await session.execute(text("""
                    SELECT 
                        schemaname,
                        tablename,
                        indexname,
                        indexdef
                    FROM pg_indexes 
                    WHERE schemaname = 'public'
                    ORDER BY tablename, indexname
                """))
                
                indexes = result.fetchall()
                
                current_table = None
                for index in indexes:
                    if index.tablename != current_table:
                        print(f"\n📋 Таблица: {index.tablename}")
                        current_table = index.tablename
                    
                    index_type = "PRIMARY KEY" if "pkey" in index.indexname else "INDEX"
                    print(f"   • {index.indexname} ({index_type})")
                
            except Exception as e:
                print(f"❌ Ошибка при получении информации об индексах: {e}")

    async def create_recommended_indexes(self):
        """Создание рекомендуемых индексов"""
        print("\n🔨 СОЗДАНИЕ РЕКОМЕНДУЕМЫХ ИНДЕКСОВ")
        print("-" * 35)
        
        recommended_indexes = [
            # Индексы для work_entries (используем правильные названия полей)
            "CREATE INDEX IF NOT EXISTS idx_work_entries_company_date ON work_entries(company_id, date)",
            "CREATE INDEX IF NOT EXISTS idx_work_entries_user_date ON work_entries(user_id, date)",
            "CREATE INDEX IF NOT EXISTS idx_work_entries_project_date ON work_entries(project_id, date)",
            "CREATE INDEX IF NOT EXISTS idx_work_entries_is_deleted ON work_entries(is_deleted)",

            # Индексы для work_types
            "CREATE INDEX IF NOT EXISTS idx_work_types_company ON work_types(company_id)",
            "CREATE INDEX IF NOT EXISTS idx_work_types_company_deleted ON work_types(company_id, is_deleted)",
            "CREATE INDEX IF NOT EXISTS idx_work_types_project ON work_types(project_id)",

            # Индексы для projects
            "CREATE INDEX IF NOT EXISTS idx_projects_company ON projects(company_id)",
            "CREATE INDEX IF NOT EXISTS idx_projects_company_deleted ON projects(company_id, is_deleted)",

            # Индексы для user_company_roles
            "CREATE INDEX IF NOT EXISTS idx_user_company_roles_company ON user_company_roles(company_id)",
            "CREATE INDEX IF NOT EXISTS idx_user_company_roles_user ON user_company_roles(user_id)",
            "CREATE INDEX IF NOT EXISTS idx_user_company_roles_role ON user_company_roles(role)",
        ]
        
        async with async_session() as session:
            for index_sql in recommended_indexes:
                try:
                    await session.execute(text(index_sql))
                    index_name = index_sql.split("idx_")[1].split(" ")[0]
                    print(f"✅ Создан индекс: idx_{index_name}")
                except Exception as e:
                    print(f"❌ Ошибка создания индекса: {e}")
            
            await session.commit()
            print("\n✅ Все рекомендуемые индексы созданы!")


async def main():
    """Главная функция"""
    analyzer = PerformanceAnalyzer()
    await analyzer.run_analysis()
    
    # Спрашиваем пользователя о создании индексов
    print("\n" + "=" * 50)
    create_indexes = input("Создать рекомендуемые индексы? (y/n): ").lower().strip()
    
    if create_indexes == 'y':
        await analyzer.create_recommended_indexes()
    else:
        print("Индексы не созданы. Вы можете создать их позже.")


if __name__ == "__main__":
    asyncio.run(main())
