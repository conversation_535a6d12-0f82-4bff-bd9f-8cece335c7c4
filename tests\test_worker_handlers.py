"""
Тесты для обработчиков рабочего (CHECKPOINT 7).

Проверяет основные функции обработчиков worker.py, work_entry.py, worker_project.py.
"""
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from aiogram.types import Message, User as TgUser
from aiogram.fsm.context import FSMContext

from handlers.worker import (
    handle_add_work_button,
    handle_list_work_button,
    handle_worker_report_button,
    handle_select_project_button
)
from handlers.work_entry import start_add_work_flow, process_date_choice
from handlers.worker_project import start_new_project_flow
from keyboards.worker import create_worker_menu
from states import WorkerAddWorkStates, WorkerListStates, WorkerProjectStates


class TestWorkerHandlers:
    """Тесты для основных обработчиков рабочего."""

    @pytest.fixture
    def mock_message(self):
        """Создает мок сообщения."""
        message = MagicMock(spec=Message)
        message.from_user = MagicMock(spec=TgUser)
        message.from_user.id = 12345
        message.text = "Тестовое сообщение"
        message.answer = AsyncMock()
        return message

    @pytest.fixture
    def mock_state(self):
        """Создает мок состояния FSM."""
        state = MagicMock(spec=FSMContext)
        state.clear = AsyncMock()
        state.set_state = AsyncMock()
        state.update_data = AsyncMock()
        state.get_data = AsyncMock(return_value={})
        return state

    @pytest.mark.asyncio
    async def test_handle_add_work_button_no_active_project(self, mock_message, mock_state):
        """Тест кнопки добавления работы без активного проекта."""
        with patch('handlers.worker.get_session') as mock_get_session:
            # Мокаем пользователя без активного проекта
            mock_session = AsyncMock()
            mock_user = MagicMock()
            mock_user.active_project_id = None
            mock_session.get.return_value = mock_user
            mock_get_session.return_value.__aenter__.return_value = mock_session
            
            await handle_add_work_button(
                mock_message, 
                mock_state, 
                user_permissions={"can_add_work": True}
            )
            
            # Проверяем, что отправлено сообщение об ошибке
            mock_message.answer.assert_called()
            call_args = mock_message.answer.call_args[0][0]
            assert "нет активного проекта" in call_args

    @pytest.mark.asyncio
    async def test_handle_add_work_button_with_active_project(self, mock_message, mock_state):
        """Тест кнопки добавления работы с активным проектом."""
        with patch('handlers.worker.get_session') as mock_get_session, \
             patch('handlers.worker.start_add_work_flow') as mock_start_flow:
            
            # Мокаем пользователя с активным проектом
            mock_session = AsyncMock()
            mock_user = MagicMock()
            mock_user.active_project_id = 1
            mock_session.get.return_value = mock_user
            mock_get_session.return_value.__aenter__.return_value = mock_session
            
            await handle_add_work_button(
                mock_message, 
                mock_state, 
                user_permissions={"can_add_work": True}
            )
            
            # Проверяем, что вызван start_add_work_flow
            mock_start_flow.assert_called_once_with(mock_message, mock_state)

    @pytest.mark.asyncio
    async def test_handle_list_work_button(self, mock_message, mock_state):
        """Тест кнопки просмотра записей."""
        await handle_list_work_button(
            mock_message, 
            mock_state, 
            user_permissions={"can_add_work": True}
        )
        
        # Проверяем, что состояние очищено и установлено новое
        mock_state.clear.assert_called_once()
        mock_state.set_state.assert_called_once_with(WorkerListStates.selecting_filter)
        
        # Проверяем, что отправлено сообщение с клавиатурой
        mock_message.answer.assert_called_once()
        call_args = mock_message.answer.call_args
        assert "Выберите фильтр" in call_args[0][0]

    @pytest.mark.asyncio
    async def test_handle_worker_report_button(self, mock_message, mock_state):
        """Тест кнопки генерации отчета."""
        with patch('handlers.worker.get_session') as mock_get_session, \
             patch('handlers.worker.WorkerReportService.generate_worker_report') as mock_report:
            
            # Мокаем данные
            mock_session = AsyncMock()
            mock_user = MagicMock()
            mock_user.active_company_id = 1
            mock_session.get.return_value = mock_user
            mock_get_session.return_value.__aenter__.return_value = mock_session
            
            mock_report.return_value = {
                "statistics": {
                    "total_entries": 10,
                    "total_sum_formatted": "1000,00 €",
                    "average_sum_formatted": "100,00 €",
                    "work_types_count": 3
                },
                "projects": [],
                "work_types": []
            }
            
            await handle_worker_report_button(
                mock_message, 
                mock_state, 
                user_permissions={"can_view_own_reports": True}
            )
            
            # Проверяем, что отчет был сгенерирован
            mock_report.assert_called_once()
            mock_message.answer.assert_called()

    @pytest.mark.asyncio
    async def test_handle_select_project_button_no_company(self, mock_message, mock_state):
        """Тест выбора проекта без компании."""
        with patch('handlers.worker.get_session') as mock_get_session:
            # Мокаем пользователя без компании
            mock_session = AsyncMock()
            mock_user = MagicMock()
            mock_user.active_company_id = None
            mock_session.get.return_value = mock_user
            mock_get_session.return_value.__aenter__.return_value = mock_session
            
            await handle_select_project_button(
                mock_message, 
                mock_state, 
                user_permissions={"can_manage_projects": True}
            )
            
            # Проверяем сообщение об ошибке
            mock_message.answer.assert_called()
            call_args = mock_message.answer.call_args[0][0]
            assert "не привязаны к компании" in call_args


class TestWorkEntryHandlers:
    """Тесты для обработчиков записей о работе."""

    @pytest.fixture
    def mock_message(self):
        """Создает мок сообщения."""
        message = MagicMock(spec=Message)
        message.from_user = MagicMock(spec=TgUser)
        message.from_user.id = 12345
        message.text = "Сегодня"
        message.answer = AsyncMock()
        return message

    @pytest.fixture
    def mock_state(self):
        """Создает мок состояния FSM."""
        state = MagicMock(spec=FSMContext)
        state.clear = AsyncMock()
        state.set_state = AsyncMock()
        state.update_data = AsyncMock()
        state.get_data = AsyncMock(return_value={
            "active_project_id": 1,
            "company_id": 1
        })
        return state

    @pytest.mark.asyncio
    async def test_start_add_work_flow_success(self, mock_message, mock_state):
        """Тест успешного запуска добавления работы."""
        with patch('handlers.work_entry.get_session') as mock_get_session:
            # Мокаем пользователя с активным проектом
            mock_session = AsyncMock()
            mock_user = MagicMock()
            mock_user.active_project_id = 1
            mock_user.active_company_id = 1
            mock_session.get.return_value = mock_user
            mock_get_session.return_value.__aenter__.return_value = mock_session
            
            await start_add_work_flow(mock_message, mock_state)
            
            # Проверяем, что состояние очищено и установлено
            mock_state.clear.assert_called_once()
            mock_state.update_data.assert_called_once()
            mock_state.set_state.assert_called_once_with(WorkerAddWorkStates.waiting_for_date_choice)
            
            # Проверяем отправку сообщения
            mock_message.answer.assert_called_once()

    @pytest.mark.asyncio
    async def test_process_date_choice_today(self, mock_message, mock_state):
        """Тест выбора сегодняшней даты."""
        mock_message.text = "Сегодня"
        
        with patch('handlers.work_entry.go_to_work_type_step') as mock_go_to_step:
            await process_date_choice(mock_message, mock_state)
            
            # Проверяем, что данные обновлены и переход к следующему шагу
            mock_state.update_data.assert_called_once()
            mock_go_to_step.assert_called_once_with(mock_message, mock_state)

    @pytest.mark.asyncio
    async def test_process_date_choice_cancel(self, mock_message, mock_state):
        """Тест отмены добавления работы."""
        mock_message.text = "🔙 Отмена"
        
        await process_date_choice(mock_message, mock_state)
        
        # Проверяем, что состояние очищено
        mock_state.clear.assert_called_once()
        
        # Проверяем отправку сообщения об отмене
        mock_message.answer.assert_called_once()
        call_args = mock_message.answer.call_args[0][0]
        assert "отменено" in call_args


class TestWorkerProjectHandlers:
    """Тесты для обработчиков проектов рабочего."""

    @pytest.fixture
    def mock_message(self):
        """Создает мок сообщения."""
        message = MagicMock(spec=Message)
        message.from_user = MagicMock(spec=TgUser)
        message.from_user.id = 12345
        message.text = "Тестовый проект"
        message.answer = AsyncMock()
        return message

    @pytest.fixture
    def mock_state(self):
        """Создает мок состояния FSM."""
        state = MagicMock(spec=FSMContext)
        state.clear = AsyncMock()
        state.set_state = AsyncMock()
        state.update_data = AsyncMock()
        state.get_data = AsyncMock(return_value={})
        return state

    @pytest.mark.asyncio
    async def test_start_new_project_flow(self, mock_message, mock_state):
        """Тест запуска создания нового проекта."""
        await start_new_project_flow(mock_message, mock_state)
        
        # Проверяем, что состояние очищено и установлено
        mock_state.clear.assert_called_once()
        mock_state.set_state.assert_called_once_with(WorkerProjectStates.creating_project_name)
        
        # Проверяем отправку сообщения
        mock_message.answer.assert_called_once()
        call_args = mock_message.answer.call_args[0][0]
        assert "Создание нового проекта" in call_args


class TestWorkerKeyboards:
    """Тесты для клавиатур рабочего."""

    def test_create_worker_menu(self):
        """Тест создания главного меню рабочего."""
        keyboard = create_worker_menu()
        
        # Проверяем, что клавиатура создана
        assert keyboard is not None
        assert keyboard.resize_keyboard is True
        
        # Проверяем наличие основных кнопок
        buttons_text = []
        for row in keyboard.keyboard:
            for button in row:
                buttons_text.append(button.text)
        
        expected_buttons = [
            "📝 Добавить работу",
            "📋 Мои записи",
            "📊 Мой отчёт",
            "🏗️ Выбрать проект",
            "➕ Новый проект",
            "✏️ Редактировать проект",
            "📤 Экспорт данных",
            "ℹ️ Инфо"
        ]
        
        for expected_button in expected_buttons:
            assert expected_button in buttons_text


if __name__ == "__main__":
    pytest.main([__file__])
