"""
Worklog Bot v2.0 - Telegram-бот для учета рабочего времени в Финляндии

Точка входа в приложение.
"""
import asyncio
import logging
import os
from aiogram import Bo<PERSON>, Dispatcher
from aiogram.fsm.storage.memory import MemoryStorage
from aiogram.fsm.storage.redis import RedisStorage
from dotenv import load_dotenv

from db.database import init_db
from handlers import register_all_handlers
from middleware.rbac_middleware import RBACMiddleware

# Глобальная переменная для доступа к диспетчеру
dp = None

# Загрузка переменных окружения
load_dotenv()

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def main():
    """Главная функция запуска бота"""
    
    # Получение переменных окружения
    bot_token = os.getenv("BOT_TOKEN")
    redis_url = os.getenv("REDIS_URL", "redis://localhost:6379/0")
    
    if not bot_token:
        raise ValueError("BOT_TOKEN не найден в переменных окружения")
    
    # Инициализация бота и диспетчера
    bot = Bot(token=bot_token)
    
    # Настройка storage для FSM
    try:
        # Попытка подключения к Redis
        import redis.asyncio as redis
        redis_client = redis.from_url(redis_url)
        await redis_client.ping()  # Проверка подключения
        await redis_client.close()

        storage = RedisStorage.from_url(redis_url)
        logger.info("Используется Redis storage")
    except Exception as e:
        # Fallback на MemoryStorage если Redis недоступен
        logger.warning(f"Redis недоступен ({e}), используется MemoryStorage")
        storage = MemoryStorage()

    dp = Dispatcher(storage=storage)

    # Регистрация middleware
    rbac_middleware = RBACMiddleware()
    dp.message.middleware(rbac_middleware)
    dp.callback_query.middleware(rbac_middleware)

    # Сохраняем ссылку на диспетчер глобально
    globals()['dp'] = dp
    
    # Инициализация базы данных
    await init_db()
    
    # Регистрация всех обработчиков
    register_all_handlers(dp)
    
    logger.info("🚀 Worklog Bot v2.0 запущен!")
    
    try:
        # Запуск polling
        await dp.start_polling(bot)
    finally:
        await bot.session.close()
        await storage.close()


if __name__ == "__main__":
    asyncio.run(main())
