"""
Тесты для WorkEntryService (CHECKPOINT 7).

Проверяет основные функции сервиса записей о работе.
"""
import pytest
from datetime import date, datetime
from decimal import Decimal
from unittest.mock import AsyncMock, MagicMock

from services.work_entry_service import WorkEntryService
from services.calculator import calculate_sum, validate_numeric_input, format_currency
from utils.exceptions import ValidationError, NotFoundError


class TestWorkEntryService:
    """Тесты для WorkEntryService."""

    @pytest.mark.asyncio
    async def test_create_work_entry_success(self):
        """Тест успешного создания записи о работе."""
        # Мокаем сессию и модели
        mock_session = AsyncMock()
        mock_work_type = MagicMock()
        mock_work_type.hourly_rate = Decimal('25.50')
        mock_work_type.value = Decimal('25.50')
        
        # Мокаем результат запроса типа работы
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = mock_work_type
        mock_session.execute.return_value = mock_result
        
        # Мокаем созданную запись
        mock_entry = MagicMock()
        mock_entry.entry_id = 123
        mock_session.add = MagicMock()
        mock_session.refresh = AsyncMock()
        
        # Вызываем метод
        result = await WorkEntryService.create_work_entry(
            session=mock_session,
            user_id=1,
            project_id=1,
            work_type_id=1,
            date=date.today(),
            quantity=8.0,
            description="Тестовая работа",
            company_id=1
        )
        
        # Проверяем, что методы были вызваны
        assert mock_session.add.called
        assert mock_session.commit.called
        assert mock_session.refresh.called

    @pytest.mark.asyncio
    async def test_create_work_entry_work_type_not_found(self):
        """Тест создания записи с несуществующим типом работы."""
        mock_session = AsyncMock()
        
        # Мокаем отсутствие типа работы
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result
        
        # Проверяем, что выбрасывается исключение
        with pytest.raises(NotFoundError):
            await WorkEntryService.create_work_entry(
                session=mock_session,
                user_id=1,
                project_id=1,
                work_type_id=999,
                date=date.today(),
                quantity=8.0,
                description="Тестовая работа",
                company_id=1
            )

    @pytest.mark.asyncio
    async def test_get_user_work_entries(self):
        """Тест получения записей пользователя."""
        mock_session = AsyncMock()
        
        # Мокаем записи
        mock_entries = [MagicMock(), MagicMock()]
        mock_result = AsyncMock()
        mock_result.scalars.return_value.all.return_value = mock_entries
        mock_session.execute.return_value = mock_result
        
        # Вызываем метод
        result = await WorkEntryService.get_user_work_entries(
            session=mock_session,
            user_id=1
        )
        
        # Проверяем результат
        assert len(result) == 2
        assert mock_session.execute.called

    @pytest.mark.asyncio
    async def test_get_work_entry_by_id_success(self):
        """Тест успешного получения записи по ID."""
        mock_session = AsyncMock()
        mock_entry = MagicMock()
        mock_entry.entry_id = 123
        
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = mock_entry
        mock_session.execute.return_value = mock_result
        
        result = await WorkEntryService.get_work_entry_by_id(
            session=mock_session,
            entry_id=123,
            user_id=1
        )
        
        assert result == mock_entry
        assert mock_session.execute.called

    @pytest.mark.asyncio
    async def test_get_work_entry_by_id_not_found(self):
        """Тест получения несуществующей записи."""
        mock_session = AsyncMock()
        
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result
        
        result = await WorkEntryService.get_work_entry_by_id(
            session=mock_session,
            entry_id=999,
            user_id=1
        )
        
        assert result is None

    @pytest.mark.asyncio
    async def test_delete_work_entry_success(self):
        """Тест успешного удаления записи."""
        mock_session = AsyncMock()
        mock_entry = MagicMock()
        mock_entry.is_deleted = False
        
        # Мокаем получение записи
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = mock_entry
        mock_session.execute.return_value = mock_result
        
        result = await WorkEntryService.delete_work_entry(
            session=mock_session,
            entry_id=123,
            user_id=1
        )
        
        assert result is True
        assert mock_entry.is_deleted is True
        assert mock_session.commit.called

    @pytest.mark.asyncio
    async def test_delete_work_entry_not_found(self):
        """Тест удаления несуществующей записи."""
        mock_session = AsyncMock()
        
        # Мокаем отсутствие записи
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result
        
        with pytest.raises(NotFoundError):
            await WorkEntryService.delete_work_entry(
                session=mock_session,
                entry_id=999,
                user_id=1
            )


class TestCalculator:
    """Тесты для модуля calculator."""

    def test_calculate_sum_per_unit(self):
        """Тест расчета суммы для ставки за единицу."""
        mock_work_type = MagicMock()
        mock_work_type.rate_type = 'per_unit'
        mock_work_type.value = 25.50
        mock_work_type.hourly_rate = 25.50
        
        result = calculate_sum(mock_work_type, 8.0)
        assert result == 204.0  # 25.50 * 8.0

    def test_calculate_sum_fixed(self):
        """Тест расчета суммы для фиксированной ставки."""
        mock_work_type = MagicMock()
        mock_work_type.rate_type = 'fixed'
        mock_work_type.value = 100.0
        mock_work_type.hourly_rate = 100.0
        
        result = calculate_sum(mock_work_type, 8.0)
        assert result == 100.0  # Фиксированная ставка не зависит от количества

    def test_validate_numeric_input_valid(self):
        """Тест валидации корректного числового ввода."""
        result = validate_numeric_input("8.5", "количество")
        assert result == 8.5

    def test_validate_numeric_input_finnish_format(self):
        """Тест валидации финляндского формата (запятая)."""
        result = validate_numeric_input("8,5", "количество")
        assert result == 8.5

    def test_validate_numeric_input_invalid(self):
        """Тест валидации некорректного ввода."""
        with pytest.raises(ValueError):
            validate_numeric_input("abc", "количество")

    def test_validate_numeric_input_negative(self):
        """Тест валидации отрицательного числа."""
        with pytest.raises(ValueError):
            validate_numeric_input("-5", "количество")

    def test_format_currency(self):
        """Тест форматирования валюты."""
        result = format_currency(123.45)
        assert result == "123,45 €"

    def test_format_currency_round(self):
        """Тест форматирования валюты с округлением."""
        result = format_currency(123.456)
        assert result == "123,46 €"


if __name__ == "__main__":
    pytest.main([__file__])
