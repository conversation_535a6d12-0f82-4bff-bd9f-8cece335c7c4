"""
Сервис для работы с проектами.

Создание, редактирование и управление проектами компаний.
"""
import logging
from typing import Optional, Dict, Any, List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete
from sqlalchemy.orm import selectinload

from db.models import Project, Company, UserCompanyRole, User, WorkType
from db.database import async_session

logger = logging.getLogger(__name__)


class ProjectService:
    """Сервис для работы с проектами"""
    

    
    @staticmethod
    async def get_project(project_id: int) -> Optional[Dict[str, Any]]:
        """
        Получает проект по ID
        
        Args:
            project_id: ID проекта
            
        Returns:
            Данные проекта или None
        """
        async with async_session() as session:
            try:
                result = await session.execute(
                    select(Project)
                    .where(Project.id == project_id)
                    .where(Project.is_deleted == False)
                )
                project = result.scalar_one_or_none()
                
                if not project:
                    return None
                
                return {
                    'id': project.id,
                    'company_id': project.company_id,
                    'name': project.name,
                    'address': project.address,
                    'is_deleted': project.is_deleted,
                    'created_at': project.created_at,
                    'updated_at': project.updated_at
                }
                
            except Exception as e:
                logger.error(f"Ошибка при получении проекта {project_id}: {e}")
                return None
    

    
    @staticmethod
    async def update_project(
        project_id: int,
        name: Optional[str] = None,
        address: Optional[str] = None
    ) -> bool:
        """
        Обновляет данные проекта
        
        Args:
            project_id: ID проекта
            name: Новое название (опционально)
            address: Новый адрес (опционально)
            
        Returns:
            True если обновление успешно
        """
        async with async_session() as session:
            try:
                update_data = {}
                if name is not None:
                    update_data['name'] = name
                if address is not None:
                    update_data['address'] = address
                
                if not update_data:
                    return True
                
                result = await session.execute(
                    update(Project)
                    .where(Project.id == project_id)
                    .where(Project.is_deleted == False)
                    .values(**update_data)
                )
                
                if result.rowcount == 0:
                    return False
                
                await session.commit()
                logger.info(f"Обновлен проект {project_id}")
                return True
                
            except Exception as e:
                logger.error(f"Ошибка при обновлении проекта {project_id}: {e}")
                await session.rollback()
                return False
    
    @staticmethod
    async def delete_project(project_id: int) -> bool:
        """
        Удаляет проект (soft delete)
        
        Args:
            project_id: ID проекта
            
        Returns:
            True если удаление успешно
        """
        async with async_session() as session:
            try:
                result = await session.execute(
                    update(Project)
                    .where(Project.id == project_id)
                    .values(is_deleted=True)
                )
                
                if result.rowcount == 0:
                    return False
                
                await session.commit()
                logger.info(f"Удален проект {project_id}")
                return True
                
            except Exception as e:
                logger.error(f"Ошибка при удалении проекта {project_id}: {e}")
                await session.rollback()
                return False
    
    @staticmethod
    async def get_user_projects(user_id: int) -> List[Dict[str, Any]]:
        """
        Получает проекты пользователя через его компании
        
        Args:
            user_id: ID пользователя
            
        Returns:
            Список проектов пользователя
        """
        async with async_session() as session:
            try:
                result = await session.execute(
                    select(Project)
                    .join(UserCompanyRole, Project.company_id == UserCompanyRole.company_id)
                    .where(UserCompanyRole.user_id == user_id)
                    .where(Project.is_deleted == False)
                    .order_by(Project.name)
                )
                projects = result.scalars().all()
                
                return [
                    {
                        'id': project.id,
                        'company_id': project.company_id,
                        'name': project.name,
                        'address': project.address,
                        'is_deleted': project.is_deleted,
                        'created_at': project.created_at,
                        'updated_at': project.updated_at
                    }
                    for project in projects
                ]
                
            except Exception as e:
                logger.error(f"Ошибка при получении проектов пользователя {user_id}: {e}")
                return []

    @staticmethod
    async def create_project(
        session: AsyncSession,
        name: str,
        address: Optional[str],
        created_by: int,
        company_id: int
    ) -> Project:
        """
        Создает новый проект (для рабочего).

        Args:
            session: Сессия БД
            name: Название проекта
            address: Адрес проекта
            created_by: ID создателя
            company_id: ID компании

        Returns:
            Созданный проект
        """
        try:
            project = Project(
                name=name,
                address=address,
                company_id=company_id
            )
            session.add(project)
            await session.commit()
            await session.refresh(project)

            logger.info(f"Создан проект {project.project_id} пользователем {created_by}")
            return project

        except Exception as e:
            await session.rollback()
            logger.error(f"Ошибка создания проекта: {e}")
            raise

    @staticmethod
    async def get_company_projects(
        session: AsyncSession,
        company_id: int
    ) -> List[Project]:
        """
        Получает проекты компании (для рабочего).

        Args:
            session: Сессия БД
            company_id: ID компании

        Returns:
            Список проектов
        """
        try:
            result = await session.execute(
                select(Project).where(
                    Project.company_id == company_id,
                    Project.is_deleted == False
                ).order_by(Project.name)
            )
            return list(result.scalars().all())

        except Exception as e:
            logger.error(f"Ошибка получения проектов компании {company_id}: {e}")
            return []

    @staticmethod
    async def get_project_by_name(
        session: AsyncSession,
        name: str,
        company_id: int
    ) -> Optional[Project]:
        """
        Получает проект по названию в компании.

        Args:
            session: Сессия БД
            name: Название проекта
            company_id: ID компании

        Returns:
            Проект или None
        """
        try:
            result = await session.execute(
                select(Project).where(
                    Project.name == name,
                    Project.company_id == company_id,
                    Project.is_deleted == False
                )
            )
            return result.scalar_one_or_none()

        except Exception as e:
            logger.error(f"Ошибка поиска проекта {name}: {e}")
            return None

    @staticmethod
    async def set_active_project(
        session: AsyncSession,
        user_id: int,
        project_id: int
    ) -> bool:
        """
        Устанавливает активный проект для пользователя.

        Args:
            session: Сессия БД
            user_id: ID пользователя
            project_id: ID проекта

        Returns:
            True если успешно
        """
        try:
            result = await session.execute(
                update(User).where(
                    User.user_id == user_id
                ).values(active_project_id=project_id)
            )

            if result.rowcount > 0:
                await session.commit()
                logger.info(f"Установлен активный проект {project_id} для пользователя {user_id}")
                return True
            return False

        except Exception as e:
            await session.rollback()
            logger.error(f"Ошибка установки активного проекта: {e}")
            return False

    @staticmethod
    async def create_work_type(
        session: AsyncSession,
        project_id: int,
        name: str,
        unit: str,
        rate_type: str,
        value: float
    ) -> WorkType:
        """
        Создает тип работы для проекта.

        Args:
            session: Сессия БД
            project_id: ID проекта
            name: Название типа работы
            unit: Единица измерения
            rate_type: Тип ставки ('fixed' или 'per_unit')
            value: Значение ставки

        Returns:
            Созданный тип работы
        """
        try:
            work_type = WorkType(
                project_id=project_id,
                name=name,
                unit=unit,
                rate_type=rate_type,
                value=value,
                hourly_rate=value  # Для совместимости
            )
            session.add(work_type)
            await session.commit()
            await session.refresh(work_type)

            logger.info(f"Создан тип работы {work_type.work_type_id} для проекта {project_id}")
            return work_type

        except Exception as e:
            await session.rollback()
            logger.error(f"Ошибка создания типа работы: {e}")
            raise
