"""
Сервис для работы с проектами.

Создание, редактирование и управление проектами компаний.
"""
import logging
from typing import Optional, Dict, Any, List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete
from sqlalchemy.orm import selectinload

from db.models import Project, Company, UserCompanyRole
from db.database import async_session

logger = logging.getLogger(__name__)


class ProjectService:
    """Сервис для работы с проектами"""
    
    @staticmethod
    async def create_project(
        company_id: int,
        name: str,
        address: str,
        created_by_user_id: int
    ) -> Optional[int]:
        """
        Создает новый проект
        
        Args:
            company_id: ID компании
            name: Название проекта
            address: Адрес проекта
            created_by_user_id: ID пользователя-создателя
            
        Returns:
            ID созданного проекта или None при ошибке
        """
        async with async_session() as session:
            try:
                project = Project(
                    company_id=company_id,
                    name=name,
                    address=address
                )
                session.add(project)
                await session.flush()
                
                project_id = project.id
                await session.commit()
                
                logger.info(f"Создан проект {project_id} для компании {company_id}")
                return project_id
                
            except Exception as e:
                logger.error(f"Ошибка при создании проекта: {e}")
                await session.rollback()
                return None
    
    @staticmethod
    async def get_project(project_id: int) -> Optional[Dict[str, Any]]:
        """
        Получает проект по ID
        
        Args:
            project_id: ID проекта
            
        Returns:
            Данные проекта или None
        """
        async with async_session() as session:
            try:
                result = await session.execute(
                    select(Project)
                    .where(Project.id == project_id)
                    .where(Project.is_deleted == False)
                )
                project = result.scalar_one_or_none()
                
                if not project:
                    return None
                
                return {
                    'id': project.id,
                    'company_id': project.company_id,
                    'name': project.name,
                    'address': project.address,
                    'is_deleted': project.is_deleted,
                    'created_at': project.created_at,
                    'updated_at': project.updated_at
                }
                
            except Exception as e:
                logger.error(f"Ошибка при получении проекта {project_id}: {e}")
                return None
    
    @staticmethod
    async def get_company_projects(company_id: int) -> List[Dict[str, Any]]:
        """
        Получает все проекты компании
        
        Args:
            company_id: ID компании
            
        Returns:
            Список проектов компании
        """
        async with async_session() as session:
            try:
                result = await session.execute(
                    select(Project)
                    .where(Project.company_id == company_id)
                    .where(Project.is_deleted == False)
                    .order_by(Project.name)
                )
                projects = result.scalars().all()
                
                return [
                    {
                        'id': project.id,
                        'company_id': project.company_id,
                        'name': project.name,
                        'address': project.address,
                        'is_deleted': project.is_deleted,
                        'created_at': project.created_at,
                        'updated_at': project.updated_at
                    }
                    for project in projects
                ]
                
            except Exception as e:
                logger.error(f"Ошибка при получении проектов компании {company_id}: {e}")
                return []
    
    @staticmethod
    async def update_project(
        project_id: int,
        name: Optional[str] = None,
        address: Optional[str] = None
    ) -> bool:
        """
        Обновляет данные проекта
        
        Args:
            project_id: ID проекта
            name: Новое название (опционально)
            address: Новый адрес (опционально)
            
        Returns:
            True если обновление успешно
        """
        async with async_session() as session:
            try:
                update_data = {}
                if name is not None:
                    update_data['name'] = name
                if address is not None:
                    update_data['address'] = address
                
                if not update_data:
                    return True
                
                result = await session.execute(
                    update(Project)
                    .where(Project.id == project_id)
                    .where(Project.is_deleted == False)
                    .values(**update_data)
                )
                
                if result.rowcount == 0:
                    return False
                
                await session.commit()
                logger.info(f"Обновлен проект {project_id}")
                return True
                
            except Exception as e:
                logger.error(f"Ошибка при обновлении проекта {project_id}: {e}")
                await session.rollback()
                return False
    
    @staticmethod
    async def delete_project(project_id: int) -> bool:
        """
        Удаляет проект (soft delete)
        
        Args:
            project_id: ID проекта
            
        Returns:
            True если удаление успешно
        """
        async with async_session() as session:
            try:
                result = await session.execute(
                    update(Project)
                    .where(Project.id == project_id)
                    .values(is_deleted=True)
                )
                
                if result.rowcount == 0:
                    return False
                
                await session.commit()
                logger.info(f"Удален проект {project_id}")
                return True
                
            except Exception as e:
                logger.error(f"Ошибка при удалении проекта {project_id}: {e}")
                await session.rollback()
                return False
    
    @staticmethod
    async def get_user_projects(user_id: int) -> List[Dict[str, Any]]:
        """
        Получает проекты пользователя через его компании
        
        Args:
            user_id: ID пользователя
            
        Returns:
            Список проектов пользователя
        """
        async with async_session() as session:
            try:
                result = await session.execute(
                    select(Project)
                    .join(UserCompanyRole, Project.company_id == UserCompanyRole.company_id)
                    .where(UserCompanyRole.user_id == user_id)
                    .where(Project.is_deleted == False)
                    .order_by(Project.name)
                )
                projects = result.scalars().all()
                
                return [
                    {
                        'id': project.id,
                        'company_id': project.company_id,
                        'name': project.name,
                        'address': project.address,
                        'is_deleted': project.is_deleted,
                        'created_at': project.created_at,
                        'updated_at': project.updated_at
                    }
                    for project in projects
                ]
                
            except Exception as e:
                logger.error(f"Ошибка при получении проектов пользователя {user_id}: {e}")
                return []
