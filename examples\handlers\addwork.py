"""
Обработчики для добавления записей о работе.

Команда: /addwork
FSM: AddWorkStates (7 шагов)
Права: @require_permission("can_add_work")

Логика:
1. waiting_for_date_choice - выбор даты (сегодня/ввести дату)
2. waiting_for_date_input - ввод конкретной даты
3. waiting_for_work_type - выбор типа работы
4. waiting_for_description - ввод описания
5. waiting_for_quantity - ввод количества
6. confirming_entry - подтверждение записи

Зависимости:
- Требует установленный активный проект (/setproject)
- Требует типы работ в проекте (/newproject)

ВАЖНО: Этот файл адаптирован для aiogram v3.x
Для aiogram v2.x используйте: from aiogram.dispatcher import FSMContext
"""
import logging
import datetime
from aiogram import types, Router, F
from aiogram.fsm.context import FSMContext
from aiogram.filters import Command

from db.dao.entry_dao import EntryDAO
from db.dao.user_dao import UserDAO
from db.dao.work_type_dao import WorkTypeDAO
from db.session import async_session
from keyboards import create_main_menu
from keyboards.project import yes_no_keyboard
from localization.texts import get_text
from services.calculator import calculate_sum
from utils.date_helpers import validate_date_range
from states import AddWorkStates
from config import MAX_DESCRIPTION_LENGTH
from middleware import require_permission

logger = logging.getLogger(__name__)

# Создаем роутер для aiogram v3.x
router = Router()


def register_handlers_addwork(dp) -> None:
    """
    Регистрирует обработчики для команды /addwork.

    Для aiogram v3.x: dp.include_router(router)
    Для aiogram v2.x: используйте старый способ регистрации
    """
    # aiogram v3.x - роутер уже содержит все обработчики
    dp.include_router(router)
    logger.info("Обработчики addwork зарегистрированы")


# Альтернативная функция для aiogram v2.x (DEPRECATED)
def register_handlers_addwork_v2(dp) -> None:
    """DEPRECATED: Для aiogram v2.x"""
    dp.register_message_handler(cmd_add_work, commands=["addwork"], state="*")
    dp.register_message_handler(process_date_choice, state=AddWorkStates.waiting_for_date_choice)
    dp.register_message_handler(process_date_input, state=AddWorkStates.waiting_for_date_input)
    dp.register_message_handler(process_work_type, state=AddWorkStates.waiting_for_work_type)
    dp.register_message_handler(process_description, state=AddWorkStates.waiting_for_description)
    dp.register_message_handler(process_quantity, state=AddWorkStates.waiting_for_quantity)
    dp.register_message_handler(process_confirmation, state=AddWorkStates.confirming_entry)
    logger.info("Обработчики addwork зарегистрированы (v2.x)")


# aiogram v3.x обработчики
@router.message(Command("addwork"))
@require_permission("can_add_work")
async def cmd_add_work(message: types.Message, state: FSMContext, **kwargs) -> None:
    """
    Начало сценария добавления работы.

    Проверяет:
    - Наличие активного проекта у пользователя
    - Права доступа через декоратор @require_permission

    Показывает:
    - Клавиатуру выбора даты: "Сегодня" / "Ввести дату"

    aiogram v3.x: @router.message(Command("addwork"))
    aiogram v2.x: dp.register_message_handler(cmd_add_work, commands=["addwork"])
    """
    user_id = message.from_user.id
    await state.clear()  # v3.x: clear() вместо finish()

    try:
        async with async_session() as session:
            user = await UserDAO.get_by_id(session, user_id)
            if not user or not user.active_project_id:
                await message.answer(get_text("no_active_project"), reply_markup=create_main_menu())
                return
            await state.update_data(active_project_id=user.active_project_id)

        keyboard = types.ReplyKeyboardMarkup(resize_keyboard=True, one_time_keyboard=True)
        keyboard.add(get_text("today"), get_text("enter_date"))
        keyboard.add(get_text("cancel"))

        await message.answer(get_text("choose_date"), reply_markup=keyboard)
        await state.set_state(AddWorkStates.waiting_for_date_choice)  # v3.x: set_state()

    except Exception as e:
        logger.exception(f"Ошибка при запуске /addwork для user {user_id}: {e}")
        await message.answer(get_text("error_try_again"), reply_markup=create_main_menu())
        await state.clear()  # v3.x: clear() вместо finish()


@router.message(AddWorkStates.waiting_for_date_choice)
async def process_date_choice(message: types.Message, state: FSMContext) -> None:
    """
    Обработка выбора даты (сегодня/ввести).

    Варианты:
    - "Сегодня" → устанавливает текущую дату
    - "Ввести дату" → переходит к вводу даты
    - "Отмена" → завершает сценарий

    aiogram v3.x: @router.message(AddWorkStates.waiting_for_date_choice)
    aiogram v2.x: @dp.register_message_handler(process_date_choice, state=AddWorkStates.waiting_for_date_choice)
    """
    choice = message.text.lower()

    if choice == get_text("cancel").lower():
        await message.answer(get_text("cancel_add"), reply_markup=create_main_menu())
        await state.finish()
        return

    if choice == get_text("today").lower():
        date_iso = datetime.date.today().isoformat()
        await state.update_data(date=date_iso)
        await go_to_work_type_step(message, state)
    elif choice == get_text("enter_date").lower():
        cancel_kb = types.ReplyKeyboardMarkup(resize_keyboard=True).add(get_text("cancel"))
        await message.answer(get_text("enter_date_format"), reply_markup=cancel_kb)
        await AddWorkStates.waiting_for_date_input.set()
    else:
        await message.answer(get_text("follow_instructions_or_cancel"))


async def process_date_input(message: types.Message, state: FSMContext) -> None:
    """
    Обработка введенной вручную даты.
    
    Валидирует:
    - Формат даты (через validate_date_range)
    - Корректность даты
    """
    if message.text.lower() == get_text("cancel").lower():
        await message.answer(get_text("cancel_add"), reply_markup=create_main_menu())
        await state.finish()
        return

    is_valid, error_msg, dates = validate_date_range(f"{message.text.strip()}-{message.text.strip()}")
    if not is_valid:
        await message.answer(get_text("invalid_date", error=error_msg))
        return
    
    date_iso = dates[0].isoformat()
    await state.update_data(date=date_iso)
    await go_to_work_type_step(message, state)


async def go_to_work_type_step(message: types.Message, state: FSMContext) -> None:
    """
    Переход к шагу выбора типа работ.
    
    Получает:
    - Все типы работ активного проекта
    - Создает клавиатуру с названиями типов работ
    """
    try:
        data = await state.get_data()
        project_id = data.get("active_project_id")

        async with async_session() as session:
            work_types = await WorkTypeDAO.get_by_project_id(session, project_id)

        if not work_types:
            await message.answer(get_text("no_work_types_in_project"), reply_markup=create_main_menu())
            await state.finish()
            return

        work_type_map = {wt.name: wt for wt in work_types}
        await state.update_data(work_type_map=work_type_map)

        keyboard = types.ReplyKeyboardMarkup(resize_keyboard=True, one_time_keyboard=True)
        for name in work_type_map.keys():
            keyboard.add(types.KeyboardButton(text=name))
        keyboard.add(get_text("cancel"))

        await message.answer(get_text("choose_work_type"), reply_markup=keyboard)
        await AddWorkStates.waiting_for_work_type.set()

    except Exception as e:
        logger.exception(f"Ошибка при переходе к выбору типа работ: {e}")
        await message.answer(get_text("error_try_again"), reply_markup=create_main_menu())
        await state.finish()


async def process_work_type(message: types.Message, state: FSMContext) -> None:
    """
    Обработка выбора типа работы.
    
    Сохраняет:
    - ID типа работы
    - Название типа работы
    - Единицу измерения
    """
    if message.text.lower() == get_text("cancel").lower():
        await state.finish()
        await message.answer(get_text("cancel_add"), reply_markup=create_main_menu())
        return

    work_type_name = message.text.strip()
    fsm_data = await state.get_data()
    work_type_map = fsm_data.get('work_type_map', {})

    selected_work_type = work_type_map.get(work_type_name)

    if not selected_work_type:
        await message.answer(get_text("invalid_work_type"))
        return

    await state.update_data(
        work_type_id=selected_work_type.id,
        work_type_name=selected_work_type.name,
        unit=selected_work_type.unit
    )

    cancel_kb = types.ReplyKeyboardMarkup(resize_keyboard=True).add(get_text("cancel"))
    await message.answer(get_text("enter_description"), reply_markup=cancel_kb)
    await AddWorkStates.waiting_for_description.set()


async def process_description(message: types.Message, state: FSMContext) -> None:
    """
    Обработка ввода описания работы.
    
    Валидирует:
    - Непустое описание
    - Длина не превышает MAX_DESCRIPTION_LENGTH
    """
    if message.text.lower() == get_text("cancel").lower():
        await state.finish()
        await message.answer(get_text("cancel_add"), reply_markup=create_main_menu())
        return

    description = message.text.strip()
    if not description:
        await message.answer(get_text("empty_description"))
        return

    if len(description) > MAX_DESCRIPTION_LENGTH:
        await message.answer(get_text("description_too_long", max_length=MAX_DESCRIPTION_LENGTH))
        return

    await state.update_data(description=description)
    data = await state.get_data()
    unit = data.get('unit', '')

    cancel_kb = types.ReplyKeyboardMarkup(resize_keyboard=True).add(get_text("cancel"))
    await message.answer(get_text("enter_quantity", unit=unit), reply_markup=cancel_kb)
    await AddWorkStates.waiting_for_quantity.set()


async def process_quantity(message: types.Message, state: FSMContext) -> None:
    """
    Обработка количества и расчет суммы.
    
    Валидирует:
    - Положительное число
    - Корректный формат (поддерживает запятую как разделитель)
    
    Рассчитывает:
    - Общую сумму через calculate_sum()
    """
    if message.text.lower() == get_text("cancel").lower():
        await state.finish()
        await message.answer(get_text("cancel_add"), reply_markup=create_main_menu())
        return

    try:
        quantity_str = message.text.replace(",", ".").strip()
        quantity = float(quantity_str)
        if quantity <= 0:
            raise ValueError("Количество должно быть положительным.")
    except (ValueError, TypeError):
        await message.answer(get_text("invalid_quantity_format"))
        return

    fsm_data = await state.get_data()
    work_type_id = fsm_data.get('work_type_id')

    async with async_session() as session:
        work_type = await WorkTypeDAO.get_by_id(session, work_type_id)
        if not work_type:
            await message.answer(get_text("error_try_again"), reply_markup=create_main_menu())
            await state.finish()
            return
        # Используем ставку из типа работы
        total_sum = calculate_sum(work_type, quantity)

    await state.update_data(quantity=quantity, sum=total_sum)
    
    confirm_text = get_text(
        "confirm_entry",
        date=datetime.datetime.fromisoformat(fsm_data.get('date')).strftime('%d.%m.%Y'),
        work_type=fsm_data.get('work_type_name'),
        description=fsm_data.get('description'),
        quantity=quantity,
        unit=fsm_data.get('unit'),
        total_sum=round(total_sum, 2)
    )

    await message.answer(confirm_text, reply_markup=yes_no_keyboard())
    await AddWorkStates.confirming_entry.set()


async def process_confirmation(message: types.Message, state: FSMContext) -> None:
    """
    Подтверждение и сохранение записи.
    
    Создает:
    - Запись в БД через EntryDAO.create()
    - Все данные из FSM состояния
    """
    if message.text.lower() != get_text("yes").lower():
        await message.answer(get_text("cancel_entry"), reply_markup=create_main_menu())
        await state.finish()
        return

    data = await state.get_data()
    
    try:
        final_date = datetime.datetime.fromisoformat(data['date']).date()

        async with async_session() as session:
            await EntryDAO.create(
                session=session,
                project_id=data['active_project_id'],
                work_type_id=data['work_type_id'],
                date=final_date,
                quantity=data['quantity'],
                description=data['description'],
                sum_total=data['sum']
            )
        await message.answer(get_text("entry_added"), reply_markup=create_main_menu())

    except Exception as e:
        logger.exception(f"Ошибка сохранения записи для user {message.from_user.id}: {e}")
        await message.answer(get_text("error_saving"), reply_markup=create_main_menu())

    finally:
        await state.finish()
