"""
Обработчики FSM для добавления записей о работе (адаптировано из examples).

7-шаговый FSM сценарий добавления работы:
1. waiting_for_date_choice - выбор даты (сегодня/ввести дату)
2. waiting_for_date_input - ввод конкретной даты
3. waiting_for_work_type - выбор типа работы
4. waiting_for_description - ввод описания
5. waiting_for_quantity - ввод количества
6. confirming_entry - подтверждение записи

Адаптировано под aiogram v3 и Reply-клавиатуры.
"""
import logging
from datetime import datetime, date
from aiogram import Router, F, types
from aiogram.fsm.context import FSMContext

from db.session import get_session
from db.models import User, WorkType
from services.work_entry_service import WorkEntryService
from services.calculator import calculate_sum, validate_numeric_input, format_currency
from keyboards.worker import create_worker_menu, create_date_choice_keyboard, create_cancel_keyboard, create_work_type_selection_keyboard, create_confirmation_keyboard
from states import WorkerAddWorkStates
from utils.date_helpers import parse_finnish_date, validate_date_range
from utils.exceptions import ValidationError
from sqlalchemy import select

logger = logging.getLogger(__name__)

# Создаем роутер для aiogram v3
router = Router()

# Константы
MAX_DESCRIPTION_LENGTH = 500


async def start_add_work_flow(message: types.Message, state: FSMContext):
    """
    Начинает FSM сценарий добавления работы.
    Вызывается из handlers/worker.py
    """
    user_id = message.from_user.id
    await state.clear()

    try:
        async with get_session() as session:
            user = await session.get(User, user_id)
            if not user or not user.active_project_id:
                await message.answer(
                    "❌ У вас нет активного проекта.",
                    reply_markup=create_worker_menu()
                )
                return
            
            await state.update_data(
                active_project_id=user.active_project_id,
                company_id=user.active_company_id
            )

        keyboard = create_date_choice_keyboard()

        await message.answer(
            "📅 Выберите дату работы:",
            reply_markup=keyboard
        )
        await state.set_state(WorkerAddWorkStates.waiting_for_date_choice)

    except Exception as e:
        logger.error(f"Ошибка при запуске добавления работы для user {user_id}: {e}")
        await message.answer(
            "❌ Произошла ошибка. Попробуйте позже.",
            reply_markup=create_worker_menu()
        )


@router.message(WorkerAddWorkStates.waiting_for_date_choice)
async def process_date_choice(message: types.Message, state: FSMContext):
    """
    Обработка выбора даты (сегодня/ввести).
    """
    choice = message.text

    if choice == "🔙 Отмена":
        await message.answer(
            "❌ Добавление работы отменено.",
            reply_markup=create_worker_menu()
        )
        await state.clear()
        return

    if choice == "Сегодня":
        work_date = date.today()
        await state.update_data(work_date=work_date)
        await go_to_work_type_step(message, state)
        
    elif choice == "Ввести дату":
        keyboard = create_cancel_keyboard()
        
        await message.answer(
            "📅 Введите дату в формате ДД.ММ.ГГГГ\n"
            "Например: 15.01.2024",
            reply_markup=keyboard
        )
        await state.set_state(WorkerAddWorkStates.waiting_for_date_input)
    else:
        await message.answer(
            "❌ Пожалуйста, выберите один из предложенных вариантов."
        )


@router.message(WorkerAddWorkStates.waiting_for_date_input)
async def process_date_input(message: types.Message, state: FSMContext):
    """
    Обработка введенной вручную даты.
    """
    if message.text == "🔙 Отмена":
        await message.answer(
            "❌ Добавление работы отменено.",
            reply_markup=create_worker_menu()
        )
        await state.clear()
        return

    try:
        # Парсим финляндский формат даты
        work_date = parse_finnish_date(message.text.strip())
        await state.update_data(work_date=work_date)
        await go_to_work_type_step(message, state)
        
    except ValueError as e:
        await message.answer(
            f"❌ Неверный формат даты: {e}\n"
            "Введите дату в формате ДД.ММ.ГГГГ (например: 15.01.2024)"
        )


async def go_to_work_type_step(message: types.Message, state: FSMContext):
    """
    Переход к шагу выбора типа работ.
    """
    try:
        data = await state.get_data()
        project_id = data.get("active_project_id")

        async with get_session() as session:
            # Получаем типы работ проекта
            from sqlalchemy import select
            result = await session.execute(
                select(WorkType).where(
                    WorkType.project_id == project_id,
                    WorkType.is_deleted == False
                )
            )
            work_types = list(result.scalars().all())

        if not work_types:
            await message.answer(
                "❌ В проекте нет типов работ.\n"
                "Обратитесь к директору для добавления типов работ.",
                reply_markup=create_worker_menu()
            )
            await state.clear()
            return

        # Создаем клавиатуру с типами работ
        keyboard = create_work_type_selection_keyboard(work_types)
        work_type_map = {work_type.name: work_type for work_type in work_types}

        await state.update_data(work_type_map=work_type_map)

        await message.answer(
            "🔧 Выберите тип работы:",
            reply_markup=keyboard
        )
        await state.set_state(WorkerAddWorkStates.waiting_for_work_type)

    except Exception as e:
        logger.error(f"Ошибка при переходе к выбору типа работ: {e}")
        await message.answer(
            "❌ Произошла ошибка. Попробуйте позже.",
            reply_markup=create_worker_menu()
        )
        await state.clear()


@router.message(WorkerAddWorkStates.waiting_for_work_type)
async def process_work_type(message: types.Message, state: FSMContext):
    """
    Обработка выбора типа работы.
    """
    if message.text == "🔙 Отмена":
        await message.answer(
            "❌ Добавление работы отменено.",
            reply_markup=create_worker_menu()
        )
        await state.clear()
        return

    work_type_name = message.text.strip()
    data = await state.get_data()
    work_type_map = data.get('work_type_map', {})

    selected_work_type = work_type_map.get(work_type_name)

    if not selected_work_type:
        await message.answer(
            "❌ Неверный тип работы. Выберите из предложенных вариантов."
        )
        return

    await state.update_data(
        work_type_id=selected_work_type.work_type_id,
        work_type_name=selected_work_type.name,
        unit=selected_work_type.unit,
        work_type_obj=selected_work_type
    )

    keyboard = create_cancel_keyboard()
    
    await message.answer(
        "📝 Опишите выполненную работу:",
        reply_markup=keyboard
    )
    await state.set_state(WorkerAddWorkStates.waiting_for_description)


@router.message(WorkerAddWorkStates.waiting_for_description)
async def process_description(message: types.Message, state: FSMContext):
    """
    Обработка ввода описания работы.
    """
    if message.text == "🔙 Отмена":
        await message.answer(
            "❌ Добавление работы отменено.",
            reply_markup=create_worker_menu()
        )
        await state.clear()
        return

    description = message.text.strip()
    
    if not description:
        await message.answer(
            "❌ Описание не может быть пустым. Введите описание работы."
        )
        return

    if len(description) > MAX_DESCRIPTION_LENGTH:
        await message.answer(
            f"❌ Описание слишком длинное (максимум {MAX_DESCRIPTION_LENGTH} символов).\n"
            f"Текущая длина: {len(description)} символов."
        )
        return

    await state.update_data(description=description)
    data = await state.get_data()
    unit = data.get('unit', '')

    keyboard = create_cancel_keyboard()
    
    await message.answer(
        f"🔢 Введите количество ({unit}):\n"
        "Можно использовать запятую как разделитель (например: 8,5)",
        reply_markup=keyboard
    )
    await state.set_state(WorkerAddWorkStates.waiting_for_quantity)


@router.message(WorkerAddWorkStates.waiting_for_quantity)
async def process_quantity(message: types.Message, state: FSMContext):
    """
    Обработка количества и расчет суммы.
    """
    if message.text == "🔙 Отмена":
        await message.answer(
            "❌ Добавление работы отменено.",
            reply_markup=create_worker_menu()
        )
        await state.clear()
        return

    try:
        quantity = validate_numeric_input(message.text, "количество")
        
        if quantity <= 0:
            raise ValueError("Количество должно быть положительным числом")
            
    except ValueError as e:
        await message.answer(f"❌ {e}")
        return

    data = await state.get_data()
    work_type_obj = data.get('work_type_obj')

    try:
        # Рассчитываем сумму
        total_sum = calculate_sum(work_type_obj, quantity)
        
        await state.update_data(quantity=quantity, total_sum=total_sum)
        
        # Формируем подтверждение
        work_date = data.get('work_date')
        confirm_text = (
            "✅ **Подтвердите запись:**\n\n"
            f"📅 **Дата:** {work_date.strftime('%d.%m.%Y')}\n"
            f"🔧 **Тип работы:** {data.get('work_type_name')}\n"
            f"📝 **Описание:** {data.get('description')}\n"
            f"🔢 **Количество:** {quantity} {data.get('unit')}\n"
            f"💰 **Сумма:** {format_currency(total_sum)}\n\n"
            "Всё верно?"
        )

        keyboard = create_confirmation_keyboard()

        await message.answer(
            confirm_text,
            reply_markup=keyboard,
            parse_mode="Markdown"
        )
        await state.set_state(WorkerAddWorkStates.confirming_entry)

    except Exception as e:
        logger.error(f"Ошибка расчета суммы: {e}")
        await message.answer(
            "❌ Ошибка при расчете суммы. Попробуйте позже.",
            reply_markup=create_worker_menu()
        )
        await state.clear()


@router.message(WorkerAddWorkStates.confirming_entry)
async def process_confirmation(message: types.Message, state: FSMContext):
    """
    Подтверждение и сохранение записи.
    """
    if message.text != "✅ Сохранить":
        await message.answer(
            "❌ Добавление работы отменено.",
            reply_markup=create_worker_menu()
        )
        await state.clear()
        return

    user_id = message.from_user.id
    data = await state.get_data()
    
    try:
        async with get_session() as session:
            # Создаем запись о работе
            work_entry = await WorkEntryService.create_work_entry(
                session=session,
                user_id=user_id,
                project_id=data['active_project_id'],
                work_type_id=data['work_type_id'],
                date=data['work_date'],
                quantity=data['quantity'],
                description=data['description'],
                company_id=data['company_id']
            )
            
        await message.answer(
            f"✅ **Запись сохранена!**\n\n"
            f"💰 Сумма: {format_currency(data['total_sum'])}\n"
            f"📋 ID записи: {work_entry.entry_id}",
            reply_markup=create_worker_menu(),
            parse_mode="Markdown"
        )
        
        logger.info(f"Создана запись о работе {work_entry.entry_id} для пользователя {user_id}")

    except Exception as e:
        logger.error(f"Ошибка сохранения записи для user {user_id}: {e}")
        await message.answer(
            "❌ Ошибка при сохранении записи. Попробуйте позже.",
            reply_markup=create_worker_menu()
        )

    finally:
        await state.clear()
