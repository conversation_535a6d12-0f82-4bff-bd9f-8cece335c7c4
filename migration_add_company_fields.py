"""
Миграция для добавления полей business_id и address в таблицу companies

Добавляет недостающие столбцы в существующую таблицу companies.
"""
import asyncio
import asyncpg
import os
from dotenv import load_dotenv

load_dotenv()

async def run_migration():
    """Выполнение миграции"""
    database_url = os.getenv("DATABASE_URL")

    if not database_url:
        print("❌ DATABASE_URL не найден в .env файле")
        return

    # Преобразуем URL для asyncpg (убираем +asyncpg)
    if "+asyncpg" in database_url:
        database_url = database_url.replace("postgresql+asyncpg://", "postgresql://")

    try:
        # Подключение к базе данных
        conn = await asyncpg.connect(database_url)
        print("✅ Подключение к PostgreSQL установлено")
        
        # Проверяем существование столбцов
        business_id_exists = await conn.fetchval("""
            SELECT EXISTS (
                SELECT 1 FROM information_schema.columns 
                WHERE table_name = 'companies' AND column_name = 'business_id'
            )
        """)
        
        address_exists = await conn.fetchval("""
            SELECT EXISTS (
                SELECT 1 FROM information_schema.columns
                WHERE table_name = 'companies' AND column_name = 'address'
            )
        """)

        is_deleted_exists = await conn.fetchval("""
            SELECT EXISTS (
                SELECT 1 FROM information_schema.columns
                WHERE table_name = 'companies' AND column_name = 'is_deleted'
            )
        """)

        updated_at_exists = await conn.fetchval("""
            SELECT EXISTS (
                SELECT 1 FROM information_schema.columns
                WHERE table_name = 'companies' AND column_name = 'updated_at'
            )
        """)

        print(f"📋 business_id существует: {business_id_exists}")
        print(f"📋 address существует: {address_exists}")
        print(f"📋 is_deleted существует: {is_deleted_exists}")
        print(f"📋 updated_at существует: {updated_at_exists}")
        
        # Добавляем business_id если не существует
        if not business_id_exists:
            await conn.execute("""
                ALTER TABLE companies 
                ADD COLUMN business_id VARCHAR(20)
            """)
            print("✅ Добавлен столбец business_id")
        else:
            print("ℹ️ Столбец business_id уже существует")
        
        # Добавляем address если не существует
        if not address_exists:
            await conn.execute("""
                ALTER TABLE companies
                ADD COLUMN address TEXT
            """)
            print("✅ Добавлен столбец address")
        else:
            print("ℹ️ Столбец address уже существует")

        # Добавляем is_deleted если не существует
        if not is_deleted_exists:
            await conn.execute("""
                ALTER TABLE companies
                ADD COLUMN is_deleted BOOLEAN DEFAULT FALSE
            """)
            print("✅ Добавлен столбец is_deleted")
        else:
            print("ℹ️ Столбец is_deleted уже существует")

        # Добавляем updated_at если не существует
        if not updated_at_exists:
            await conn.execute("""
                ALTER TABLE companies
                ADD COLUMN updated_at TIMESTAMP DEFAULT NOW()
            """)
            print("✅ Добавлен столбец updated_at")
        else:
            print("ℹ️ Столбец updated_at уже существует")
        
        # Проверяем структуру таблицы после миграции
        columns = await conn.fetch("""
            SELECT column_name, data_type, is_nullable 
            FROM information_schema.columns 
            WHERE table_name = 'companies'
            ORDER BY ordinal_position
        """)
        
        print("\n📊 Структура таблицы companies после миграции:")
        for col in columns:
            nullable = "NULL" if col['is_nullable'] == 'YES' else "NOT NULL"
            print(f"  - {col['column_name']}: {col['data_type']} ({nullable})")
        
        await conn.close()
        print("\n🎉 Миграция завершена успешно!")
        
    except Exception as e:
        print(f"❌ Ошибка при выполнении миграции: {e}")

if __name__ == "__main__":
    asyncio.run(run_migration())
