"""
Интеграционные тесты

Тестирует взаимодействие сервисов с реальной базой данных.
"""
import pytest
import asyncio
import tempfile
import os
from datetime import date, timedelta
from pathlib import Path

from db.database import async_session, init_db
from db.models import Company, User, UserCompanyRole, Project, WorkType, WorkEntry
from services.work_type_service import WorkTypeService
from services.report_service import ReportService
from services.export_service import ExportService
from services.import_service import ImportService
from sqlalchemy import select, delete


class TestIntegration:
    """Интеграционные тесты с реальной базой данных"""

    @pytest.fixture(scope="class", autouse=True)
    async def setup_database(self):
        """Настройка тестовой базы данных"""
        await init_db()
        yield
        # Очистка после тестов
        await self.cleanup_test_data()

    @pytest.fixture(autouse=True)
    async def setup_test_data(self):
        """Создание тестовых данных для каждого теста"""
        self.test_company_id = await self.create_test_company()
        self.test_user_id = await self.create_test_user()
        self.test_work_types = await self.create_test_work_types()
        self.test_project_id = await self.create_test_project()
        await self.create_test_work_entries()
        
        yield
        
        # Очистка после каждого теста
        await self.cleanup_test_data()

    async def create_test_company(self) -> int:
        """Создание тестовой компании"""
        async with async_session() as session:
            company = Company(
                name="Тестовая Компания",
                business_id="TEST123",
                address="Тестовый адрес"
            )
            session.add(company)
            await session.commit()
            await session.refresh(company)
            return company.id

    async def create_test_user(self) -> int:
        """Создание тестового пользователя"""
        async with async_session() as session:
            user = User(
                telegram_id=*********,
                display_name="Тестовый Пользователь"
            )
            session.add(user)
            await session.flush()
            
            # Назначаем роль директора
            role = UserCompanyRole(
                user_id=user.user_id,
                company_id=self.test_company_id,
                role='director'
            )
            session.add(role)
            await session.commit()
            return user.user_id

    async def create_test_work_types(self) -> list:
        """Создание тестовых типов работ"""
        work_types_data = [
            {'name': 'Тест Монтаж', 'unit': 'час', 'rate': 25.0},
            {'name': 'Тест Демонтаж', 'unit': 'м²', 'rate': 20.0}
        ]
        
        work_types = []
        for data in work_types_data:
            result = await WorkTypeService.create_work_type(
                company_id=self.test_company_id,
                name=data['name'],
                unit=data['unit'],
                rate=data['rate'],
                created_by=self.test_user_id
            )
            if result:
                work_types.append(result)
        
        return work_types

    async def create_test_project(self) -> int:
        """Создание тестового проекта"""
        async with async_session() as session:
            project = Project(
                company_id=self.test_company_id,
                created_by=self.test_user_id,
                name="Тестовый Проект",
                address="Тестовый адрес проекта"
            )
            session.add(project)
            await session.commit()
            await session.refresh(project)
            return project.project_id

    async def create_test_work_entries(self):
        """Создание тестовых записей работ"""
        async with async_session() as session:
            # Создаем записи за последние 7 дней
            for i in range(7):
                work_date = date.today() - timedelta(days=i)
                
                for work_type in self.test_work_types:
                    entry = WorkEntry(
                        user_id=self.test_user_id,
                        company_id=self.test_company_id,
                        project_id=self.test_project_id,
                        work_type_id=work_type['id'],
                        work_date=work_date,
                        hours=8.0,
                        hourly_rate=work_type['rate'],
                        total_amount=8.0 * work_type['rate'],
                        description=f"Тестовая работа {work_type['name']}"
                    )
                    session.add(entry)
            
            await session.commit()

    async def cleanup_test_data(self):
        """Очистка тестовых данных"""
        async with async_session() as session:
            # Удаляем в правильном порядке (из-за внешних ключей)
            await session.execute(delete(WorkEntry).where(WorkEntry.company_id == getattr(self, 'test_company_id', -1)))
            await session.execute(delete(WorkType).where(WorkType.company_id == getattr(self, 'test_company_id', -1)))
            await session.execute(delete(Project).where(Project.company_id == getattr(self, 'test_company_id', -1)))
            await session.execute(delete(UserCompanyRole).where(UserCompanyRole.company_id == getattr(self, 'test_company_id', -1)))
            await session.execute(delete(User).where(User.telegram_id == *********))
            await session.execute(delete(Company).where(Company.name == "Тестовая Компания"))
            await session.commit()

    @pytest.mark.asyncio
    async def test_work_type_service_integration(self):
        """Интеграционный тест WorkTypeService"""
        # Тест создания типа работы
        result = await WorkTypeService.create_work_type(
            company_id=self.test_company_id,
            name="Интеграционный Тест",
            unit="шт",
            rate=50.0,
            created_by=self.test_user_id
        )
        
        assert result is not None
        assert result['name'] == "Интеграционный Тест"
        assert result['rate'] == 50.0
        
        # Тест получения типов работ
        work_types = await WorkTypeService.get_company_work_types(self.test_company_id)
        assert len(work_types) >= 3  # 2 созданных в setup + 1 новый
        
        # Тест получения по ID
        work_type = await WorkTypeService.get_work_type_by_id(result['id'])
        assert work_type is not None
        assert work_type['name'] == "Интеграционный Тест"
        
        # Тест обновления
        update_result = await WorkTypeService.update_work_type(
            work_type_id=result['id'],
            name="Обновленный Тест",
            rate=75.0
        )
        assert update_result is True
        
        # Проверяем обновление
        updated_work_type = await WorkTypeService.get_work_type_by_id(result['id'])
        assert updated_work_type['name'] == "Обновленный Тест"
        assert updated_work_type['rate'] == 75.0
        
        # Тест удаления
        delete_result = await WorkTypeService.delete_work_type(result['id'])
        assert delete_result is True
        
        # Проверяем мягкое удаление
        deleted_work_type = await WorkTypeService.get_work_type_by_id(result['id'])
        assert deleted_work_type['is_deleted'] is True

    @pytest.mark.asyncio
    async def test_report_service_integration(self):
        """Интеграционный тест ReportService"""
        start_date = date.today() - timedelta(days=7)
        end_date = date.today()
        
        # Тест отчета по датам
        report = await ReportService.get_date_report(
            company_id=self.test_company_id,
            start_date=start_date,
            end_date=end_date
        )
        
        assert 'summary' in report
        assert report['summary']['total_entries'] > 0
        assert report['summary']['total_hours'] > 0
        assert report['summary']['total_amount'] > 0
        assert len(report['entries']) > 0
        
        # Тест отчета по рабочему
        worker_report = await ReportService.get_worker_report(
            company_id=self.test_company_id,
            worker_id=self.test_user_id,
            start_date=start_date,
            end_date=end_date
        )
        
        assert 'worker' in worker_report
        assert worker_report['worker']['id'] == self.test_user_id
        assert worker_report['summary']['total_entries'] > 0
        
        # Тест отчета по проекту
        project_report = await ReportService.get_project_report(
            company_id=self.test_company_id,
            project_id=self.test_project_id,
            start_date=start_date,
            end_date=end_date
        )
        
        assert 'project' in project_report
        assert project_report['project']['id'] == self.test_project_id
        assert project_report['summary']['total_entries'] > 0

    @pytest.mark.asyncio
    async def test_export_service_integration(self):
        """Интеграционный тест ExportService"""
        # Получаем данные для экспорта
        start_date = date.today() - timedelta(days=7)
        end_date = date.today()
        
        report_data = await ReportService.get_date_report(
            company_id=self.test_company_id,
            start_date=start_date,
            end_date=end_date
        )
        
        # Тест экспорта в Excel (если доступен)
        formats = ExportService.get_available_formats()
        
        if formats['excel']:
            excel_file = await ExportService.export_to_excel(report_data)
            assert excel_file is not None
            assert os.path.exists(excel_file)
            assert excel_file.endswith('.xlsx')
            
            # Очищаем файл
            os.remove(excel_file)
        
        if formats['pdf']:
            pdf_file = await ExportService.export_to_pdf(report_data)
            assert pdf_file is not None
            assert os.path.exists(pdf_file)
            assert pdf_file.endswith('.pdf')
            
            # Очищаем файл
            os.remove(pdf_file)

    @pytest.mark.asyncio
    async def test_import_service_integration(self):
        """Интеграционный тест ImportService"""
        if not ImportService.is_available():
            pytest.skip("pandas не установлен")
        
        # Создаем тестовый Excel файл
        import pandas as pd
        
        test_data = pd.DataFrame({
            'Название': ['Импорт Тест 1', 'Импорт Тест 2'],
            'Единица': ['час', 'м²'],
            'Ставка': [30.0, 25.0]
        })
        
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
            test_data.to_excel(tmp_file.name, index=False)
            tmp_file_path = tmp_file.name
        
        try:
            # Тест валидации файла
            validation = await ImportService.validate_excel_file(tmp_file_path)
            assert validation['is_valid'] is True
            assert validation['details']['total_rows'] == 2
            assert validation['details']['valid_rows'] == 2
            
            # Тест импорта
            result = await ImportService.import_work_types_from_excel(
                file_path=tmp_file_path,
                company_id=self.test_company_id,
                created_by=self.test_user_id
            )
            
            assert result['success'] is True
            assert result['imported'] == 2
            assert result['skipped'] == 0
            assert len(result['errors']) == 0
            
            # Проверяем, что типы работ созданы
            work_types = await WorkTypeService.get_company_work_types(self.test_company_id)
            imported_names = [wt['name'] for wt in work_types if wt['name'].startswith('Импорт Тест')]
            assert len(imported_names) == 2
            assert 'Импорт Тест 1' in imported_names
            assert 'Импорт Тест 2' in imported_names
            
        finally:
            # Очищаем временный файл
            os.unlink(tmp_file_path)

    @pytest.mark.asyncio
    async def test_full_workflow_integration(self):
        """Интеграционный тест полного рабочего процесса"""
        # 1. Создаем тип работы
        work_type = await WorkTypeService.create_work_type(
            company_id=self.test_company_id,
            name="Полный Тест",
            unit="день",
            rate=200.0,
            created_by=self.test_user_id
        )
        assert work_type is not None
        
        # 2. Создаем запись работы
        async with async_session() as session:
            entry = WorkEntry(
                user_id=self.test_user_id,
                company_id=self.test_company_id,
                project_id=self.test_project_id,
                work_type_id=work_type['id'],
                work_date=date.today(),
                hours=1.0,
                hourly_rate=work_type['rate'],
                total_amount=1.0 * work_type['rate'],
                description="Полный тест рабочего процесса"
            )
            session.add(entry)
            await session.commit()
        
        # 3. Генерируем отчет
        report = await ReportService.get_date_report(
            company_id=self.test_company_id,
            start_date=date.today(),
            end_date=date.today()
        )
        
        # Проверяем, что новая запись попала в отчет
        assert any(
            entry['work_type'] == "Полный Тест" 
            for entry in report['entries']
        )
        
        # 4. Экспортируем отчет (если доступно)
        formats = ExportService.get_available_formats()
        if formats['excel']:
            excel_file = await ExportService.export_to_excel(report)
            assert excel_file is not None
            os.remove(excel_file)
        
        # 5. Получаем статистику
        stats = await WorkTypeService.get_work_types_statistics(self.test_company_id)
        assert stats['total_count'] >= 3  # 2 из setup + 1 новый
        assert stats['average_rate'] > 0
