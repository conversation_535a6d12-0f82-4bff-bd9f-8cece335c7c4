"""
RBAC Middleware для проверки прав доступа

Проверяет права пользователей перед выполнением команд.
"""
import os
from typing import Callable, Dict, Any, Awaitable, Optional
from aiogram import BaseMiddleware
from aiogram.types import TelegramObject, Message, CallbackQuery
from dotenv import load_dotenv

from services.auth_service import AuthService

load_dotenv()


class RBACMiddleware(BaseMiddleware):
    """Middleware для проверки прав доступа (RBAC)"""

    def __init__(self):
        self.admin_id = int(os.getenv("ADMIN_ID", 0))
        self.development_mode = os.getenv("DEVELOPMENT_MODE", "false").lower() == "true"
        # Хранилище выбранных ролей для режима разработки
        self.selected_roles: Dict[int, str] = {}

    async def __call__(
        self,
        handler: Callable[[TelegramObject, Dict[str, Any]], Awaitable[Any]],
        event: TelegramObject,
        data: Dict[str, Any]
    ) -> Any:
        """Основная логика middleware"""

        # Получаем user_id из события
        user_id = None
        if isinstance(event, (Message, CallbackQuery)):
            user_id = event.from_user.id

        if not user_id:
            return await handler(event, data)

        # Добавляем базовую информацию о пользователе
        data["user_id"] = user_id
        data["is_admin"] = user_id == self.admin_id
        data["development_mode"] = self.development_mode
        data["rbac_middleware"] = self  # Передаем ссылку на middleware

        # ПРИОРИТЕТ: Виртуальные права для суперпользователя в режиме разработки
        if self.development_mode and user_id == self.admin_id:
            selected_role = self.selected_roles.get(user_id, "admin")
            data["selected_role"] = selected_role

            # Эмулируем права выбранной роли (ВСЕГДА приоритет над реальными правами)
            if selected_role in AuthService.ROLE_PERMISSIONS:
                data["user_permissions"] = AuthService.ROLE_PERMISSIONS[selected_role]
                data["user_role"] = selected_role

                # Фиктивные данные для корректной работы декораторов
                data["user_data"] = {
                    "user_id": user_id,
                    "display_name": f"Test {selected_role.title()}",
                    "active_company_id": 999,  # Фиктивная компания
                    "active_project_id": 9999 if selected_role == "worker" else None,  # Фиктивный проект для рабочего
                    "created_at": None,
                    "updated_at": None
                }

                # Логируем использование виртуальных прав
                import logging
                logger = logging.getLogger(__name__)
                logger.info(f"🧪 Суперпользователь {user_id} использует виртуальную роль: {selected_role}")
            else:
                data["user_permissions"] = {}
                data["user_role"] = None
                data["user_data"] = None
        else:
            # Получаем реальные данные пользователя из базы
            user_data = await AuthService.get_user(user_id)
            if user_data:
                data["user_data"] = user_data
                data["user_role"] = await AuthService.get_user_role(user_id)
                # Получаем права для активной компании пользователя
                company_id = user_data.get('active_company_id')
                permissions = await AuthService.get_user_permissions(user_id, company_id)
                data["user_permissions"] = permissions
            else:
                data["user_data"] = None
                data["user_role"] = None
                data["user_permissions"] = {}

        return await handler(event, data)

    def set_selected_role(self, user_id: int, role: str):
        """Установка выбранной роли для режима разработки"""
        self.selected_roles[user_id] = role

    def get_selected_role(self, user_id: int) -> Optional[str]:
        """Получение выбранной роли для режима разработки"""
        return self.selected_roles.get(user_id)


def require_permission(permission: str):
    """
    Декоратор для проверки конкретного права доступа

    Args:
        permission: Название права (например, "can_add_work")
    """
    def decorator(handler_func):
        async def wrapper(event, *args, **kwargs):
            # Получаем права пользователя из данных middleware
            user_permissions = kwargs.get("user_permissions", {})

            # Проверяем наличие права
            if not user_permissions.get(permission, False):
                # Отправляем сообщение об отсутствии прав
                if hasattr(event, 'answer'):
                    await event.answer("❌ У вас недостаточно прав для выполнения этого действия.")
                elif hasattr(event, 'message') and hasattr(event.message, 'answer'):
                    await event.message.answer("❌ У вас недостаточно прав для выполнения этого действия.")
                return

            return await handler_func(event, *args, **kwargs)
        return wrapper
    return decorator


def require_role(required_role: str):
    """
    Декоратор для проверки роли пользователя

    Args:
        required_role: Требуемая роль (admin, director, worker)
    """
    def decorator(handler_func):
        async def wrapper(event, *args, **kwargs):
            # Получаем роль пользователя из данных middleware
            user_role = kwargs.get("user_role")

            # Проверяем роль
            if user_role != required_role:
                # Отправляем сообщение об отсутствии прав
                if hasattr(event, 'answer'):
                    await event.answer(f"❌ Эта функция доступна только для роли: {required_role}")
                elif hasattr(event, 'message') and hasattr(event.message, 'answer'):
                    await event.message.answer(f"❌ Эта функция доступна только для роли: {required_role}")
                return

            return await handler_func(event, *args, **kwargs)
        return wrapper
    return decorator
