"""
Утилиты для навигации между меню.

Управляет историей навигации и возвратом в предыдущие меню.
"""
from typing import Dict, List, Optional, Callable, Any
from aiogram.types import Message
from aiogram.fsm.context import FSMContext


class NavigationManager:
    """Менеджер навигации для управления историей меню"""
    
    def __init__(self):
        # Хранилище истории навигации для каждого пользователя
        # user_id -> [menu_function, menu_function, ...]
        self._navigation_history: Dict[int, List[Callable]] = {}
        
        # Хранилище текущего меню для каждого пользователя
        # user_id -> menu_function
        self._current_menu: Dict[int, Callable] = {}
    
    def push_menu(self, user_id: int, menu_function: Callable):
        """
        Добавляет меню в историю навигации.
        
        Args:
            user_id: ID пользователя
            menu_function: Функция меню для возврата
        """
        if user_id not in self._navigation_history:
            self._navigation_history[user_id] = []
        
        # Добавляем текущее меню в историю, если оно есть
        if user_id in self._current_menu:
            current = self._current_menu[user_id]
            # Избегаем дублирования одного и того же меню
            if not self._navigation_history[user_id] or self._navigation_history[user_id][-1] != current:
                self._navigation_history[user_id].append(current)
        
        # Устанавливаем новое текущее меню
        self._current_menu[user_id] = menu_function
        
        # Ограничиваем историю (максимум 10 уровней)
        if len(self._navigation_history[user_id]) > 10:
            self._navigation_history[user_id] = self._navigation_history[user_id][-10:]
    
    def pop_menu(self, user_id: int) -> Optional[Callable]:
        """
        Возвращает предыдущее меню из истории.
        
        Args:
            user_id: ID пользователя
            
        Returns:
            Функция предыдущего меню или None
        """
        if user_id not in self._navigation_history or not self._navigation_history[user_id]:
            return None
        
        # Получаем предыдущее меню
        previous_menu = self._navigation_history[user_id].pop()
        
        # Устанавливаем его как текущее
        self._current_menu[user_id] = previous_menu
        
        return previous_menu
    
    def get_current_menu(self, user_id: int) -> Optional[Callable]:
        """
        Возвращает текущее меню пользователя.
        
        Args:
            user_id: ID пользователя
            
        Returns:
            Функция текущего меню или None
        """
        return self._current_menu.get(user_id)
    
    def clear_history(self, user_id: int):
        """
        Очищает историю навигации пользователя.
        
        Args:
            user_id: ID пользователя
        """
        if user_id in self._navigation_history:
            del self._navigation_history[user_id]
        if user_id in self._current_menu:
            del self._current_menu[user_id]
    
    def get_history_depth(self, user_id: int) -> int:
        """
        Возвращает глубину истории навигации.
        
        Args:
            user_id: ID пользователя
            
        Returns:
            Количество меню в истории
        """
        if user_id not in self._navigation_history:
            return 0
        return len(self._navigation_history[user_id])


# Глобальный экземпляр менеджера навигации
navigation_manager = NavigationManager()


def push_menu(user_id: int, menu_function: Callable):
    """Добавляет меню в историю навигации"""
    navigation_manager.push_menu(user_id, menu_function)


def pop_menu(user_id: int) -> Optional[Callable]:
    """Возвращает предыдущее меню из истории"""
    return navigation_manager.pop_menu(user_id)


def clear_navigation(user_id: int):
    """Очищает историю навигации"""
    navigation_manager.clear_history(user_id)


def get_navigation_depth(user_id: int) -> int:
    """Возвращает глубину навигации"""
    return navigation_manager.get_history_depth(user_id)


async def go_back(message: Message, **kwargs) -> bool:
    """
    Возвращает в предыдущее меню.
    
    Args:
        message: Сообщение пользователя
        **kwargs: Дополнительные параметры
        
    Returns:
        True если удалось вернуться, False если история пуста
    """
    user_id = message.from_user.id
    previous_menu = pop_menu(user_id)
    
    if previous_menu:
        try:
            # Вызываем предыдущее меню
            await previous_menu(message, **kwargs)
            return True
        except Exception as e:
            # Если произошла ошибка, очищаем историю и возвращаем False
            clear_navigation(user_id)
            return False
    
    return False


# Декоратор для автоматического добавления меню в историю
def track_navigation(menu_function: Callable):
    """
    Декоратор для отслеживания навигации.
    
    Автоматически добавляет меню в историю при вызове.
    """
    async def wrapper(message: Message, *args, **kwargs):
        user_id = message.from_user.id
        
        # Добавляем меню в историю
        push_menu(user_id, menu_function)
        
        # Вызываем оригинальную функцию
        return await menu_function(message, *args, **kwargs)
    
    return wrapper


# Функции для работы с ролевыми меню
async def show_role_main_menu_with_navigation(message: Message, role: str, **kwargs):
    """
    Показывает главное меню роли и очищает историю навигации.
    
    Args:
        message: Сообщение пользователя
        role: Роль пользователя (admin, director, worker)
        **kwargs: Дополнительные параметры
    """
    user_id = message.from_user.id
    
    # Очищаем историю при переходе в главное меню роли
    clear_navigation(user_id)
    
    # Показываем соответствующее меню
    if role == "admin":
        from handlers.common import show_admin_menu
        await show_admin_menu(message)
    elif role == "director":
        from handlers.common import show_director_menu
        await show_director_menu(message)
    elif role == "worker":
        from handlers.common import show_worker_menu
        await show_worker_menu(message)
    else:
        await message.answer("❌ Неизвестная роль")


def get_role_from_middleware(**kwargs) -> Optional[str]:
    """
    Получает роль пользователя из middleware.

    Args:
        **kwargs: Параметры, включая rbac_middleware и user_id

    Returns:
        Роль пользователя или None
    """
    user_id = kwargs.get('user_id')
    rbac_middleware = kwargs.get('rbac_middleware')

    if user_id and rbac_middleware and hasattr(rbac_middleware, 'get_selected_role'):
        return rbac_middleware.get_selected_role(user_id)
    return None
