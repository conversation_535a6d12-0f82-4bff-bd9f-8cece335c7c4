"""
Сервис отчетов для рабочего (адаптировано из examples).

Основные функции:
- generate_worker_report() - генерация отчета рабочего
- get_worker_statistics() - статистика по рабочему
- get_project_summary() - сводка по проекту
- export_worker_data() - экспорт данных рабочего
"""
import logging
from datetime import datetime, date
from typing import List, Optional, Dict, Any
from sqlalchemy import select, and_, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from db.models import WorkEntry, WorkType, Project, User
from services.calculator import calculate_work_statistics, format_currency, format_quantity

logger = logging.getLogger(__name__)


class WorkerReportService:
    """Сервис для генерации отчетов рабочего."""

    @staticmethod
    async def generate_worker_report(
        session: AsyncSession,
        user_id: int,
        company_id: Optional[int] = None,
        project_id: Optional[int] = None,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None
    ) -> Dict[str, Any]:
        """
        Генерирует отчет для рабочего.
        
        Args:
            session: Сессия БД
            user_id: ID пользователя
            company_id: ID компании
            project_id: ID проекта (опционально)
            start_date: Начальная дата
            end_date: Конечная дата
            
        Returns:
            Словарь с данными отчета
        """
        try:
            # Базовый запрос
            query = select(WorkEntry).options(
                selectinload(WorkEntry.work_type),
                selectinload(WorkEntry.project)
            ).where(
                and_(
                    WorkEntry.user_id == user_id,
                    WorkEntry.is_deleted == False
                )
            )
            
            # Фильтрация
            if company_id:
                query = query.where(WorkEntry.company_id == company_id)
            if project_id:
                query = query.where(WorkEntry.project_id == project_id)
            if start_date:
                query = query.where(WorkEntry.date >= start_date)
            if end_date:
                query = query.where(WorkEntry.date <= end_date)
            
            result = await session.execute(query)
            entries = list(result.scalars().all())
            
            # Рассчитываем статистику
            stats = calculate_work_statistics(entries)
            
            # Группируем по проектам
            projects_data = await WorkerReportService._group_by_projects(entries)
            
            # Группируем по типам работ
            work_types_data = await WorkerReportService._group_by_work_types(entries)
            
            # Формируем отчет
            report = {
                "user_id": user_id,
                "period": {
                    "start_date": start_date.isoformat() if start_date else None,
                    "end_date": end_date.isoformat() if end_date else None
                },
                "statistics": {
                    "total_entries": stats["total_entries"],
                    "total_sum": stats["total_sum"],
                    "total_quantity": stats["total_quantity"],
                    "average_sum_per_entry": stats["average_sum_per_entry"],
                    "work_types_count": stats["work_types_count"],
                    "total_sum_formatted": format_currency(stats["total_sum"]),
                    "average_sum_formatted": format_currency(stats["average_sum_per_entry"])
                },
                "projects": projects_data,
                "work_types": work_types_data,
                "entries": [
                    {
                        "entry_id": entry.entry_id,
                        "date": entry.date.isoformat(),
                        "project_name": entry.project.name if entry.project else "",
                        "work_type_name": entry.work_type.name if entry.work_type else "",
                        "description": entry.description,
                        "quantity": float(entry.quantity),
                        "unit": entry.work_type.unit if entry.work_type else "",
                        "amount": float(entry.calculated_amount),
                        "amount_formatted": format_currency(entry.calculated_amount)
                    }
                    for entry in entries
                ]
            }
            
            logger.info(f"Сгенерирован отчет для пользователя {user_id}: {stats['total_entries']} записей")
            return report
            
        except Exception as e:
            logger.error(f"Ошибка генерации отчета для пользователя {user_id}: {e}")
            raise


    @staticmethod
    async def _group_by_projects(entries: List[WorkEntry]) -> List[Dict[str, Any]]:
        """Группирует записи по проектам."""
        projects = {}
        
        for entry in entries:
            project_id = entry.project_id
            project_name = entry.project.name if entry.project else "Неизвестный проект"
            
            if project_id not in projects:
                projects[project_id] = {
                    "project_id": project_id,
                    "project_name": project_name,
                    "entries_count": 0,
                    "total_quantity": 0.0,
                    "total_sum": 0.0,
                    "work_types": set()
                }
            
            projects[project_id]["entries_count"] += 1
            projects[project_id]["total_quantity"] += float(entry.quantity)
            projects[project_id]["total_sum"] += float(entry.calculated_amount)
            
            if entry.work_type:
                projects[project_id]["work_types"].add(entry.work_type.name)
        
        # Преобразуем в список и форматируем
        result = []
        for project_data in projects.values():
            project_data["work_types"] = list(project_data["work_types"])
            project_data["total_sum_formatted"] = format_currency(project_data["total_sum"])
            result.append(project_data)
        
        return sorted(result, key=lambda x: x["total_sum"], reverse=True)


    @staticmethod
    async def _group_by_work_types(entries: List[WorkEntry]) -> List[Dict[str, Any]]:
        """Группирует записи по типам работ."""
        work_types = {}
        
        for entry in entries:
            if not entry.work_type:
                continue
                
            work_type_id = entry.work_type_id
            work_type_name = entry.work_type.name
            unit = entry.work_type.unit
            
            if work_type_id not in work_types:
                work_types[work_type_id] = {
                    "work_type_id": work_type_id,
                    "work_type_name": work_type_name,
                    "unit": unit,
                    "entries_count": 0,
                    "total_quantity": 0.0,
                    "total_sum": 0.0,
                    "average_rate": 0.0
                }
            
            work_types[work_type_id]["entries_count"] += 1
            work_types[work_type_id]["total_quantity"] += float(entry.quantity)
            work_types[work_type_id]["total_sum"] += float(entry.calculated_amount)
        
        # Рассчитываем средние ставки и форматируем
        result = []
        for work_type_data in work_types.values():
            if work_type_data["total_quantity"] > 0:
                work_type_data["average_rate"] = work_type_data["total_sum"] / work_type_data["total_quantity"]
            
            work_type_data["total_sum_formatted"] = format_currency(work_type_data["total_sum"])
            work_type_data["total_quantity_formatted"] = format_quantity(
                work_type_data["total_quantity"], 
                work_type_data["unit"]
            )
            work_type_data["average_rate_formatted"] = format_currency(work_type_data["average_rate"])
            result.append(work_type_data)
        
        return sorted(result, key=lambda x: x["total_sum"], reverse=True)


    @staticmethod
    async def get_worker_statistics(
        session: AsyncSession,
        user_id: int,
        company_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Получает общую статистику по рабочему.
        
        Args:
            session: Сессия БД
            user_id: ID пользователя
            company_id: ID компании
            
        Returns:
            Словарь со статистикой
        """
        try:
            # Общая статистика
            query = select(
                func.count(WorkEntry.entry_id).label('total_entries'),
                func.sum(WorkEntry.calculated_amount).label('total_sum'),
                func.sum(WorkEntry.quantity).label('total_quantity'),
                func.count(func.distinct(WorkEntry.project_id)).label('projects_count'),
                func.count(func.distinct(WorkEntry.work_type_id)).label('work_types_count')
            ).where(
                and_(
                    WorkEntry.user_id == user_id,
                    WorkEntry.is_deleted == False
                )
            )
            
            if company_id:
                query = query.where(WorkEntry.company_id == company_id)
            
            result = await session.execute(query)
            stats = result.first()
            
            # Статистика за текущий месяц
            current_month_start = date.today().replace(day=1)
            month_query = select(
                func.count(WorkEntry.entry_id).label('month_entries'),
                func.sum(WorkEntry.calculated_amount).label('month_sum')
            ).where(
                and_(
                    WorkEntry.user_id == user_id,
                    WorkEntry.is_deleted == False,
                    WorkEntry.date >= current_month_start
                )
            )
            
            if company_id:
                month_query = month_query.where(WorkEntry.company_id == company_id)
            
            month_result = await session.execute(month_query)
            month_stats = month_result.first()
            
            return {
                "total_entries": stats.total_entries or 0,
                "total_sum": float(stats.total_sum or 0),
                "total_quantity": float(stats.total_quantity or 0),
                "projects_count": stats.projects_count or 0,
                "work_types_count": stats.work_types_count or 0,
                "average_sum_per_entry": float(stats.total_sum or 0) / max(stats.total_entries or 1, 1),
                "month_entries": month_stats.month_entries or 0,
                "month_sum": float(month_stats.month_sum or 0),
                # Форматированные значения
                "total_sum_formatted": format_currency(stats.total_sum or 0),
                "month_sum_formatted": format_currency(month_stats.month_sum or 0),
                "average_sum_formatted": format_currency(
                    float(stats.total_sum or 0) / max(stats.total_entries or 1, 1)
                )
            }
            
        except Exception as e:
            logger.error(f"Ошибка получения статистики для пользователя {user_id}: {e}")
            raise


    @staticmethod
    async def get_project_summary(
        session: AsyncSession,
        user_id: int,
        project_id: int
    ) -> Dict[str, Any]:
        """
        Получает сводку по конкретному проекту.
        
        Args:
            session: Сессия БД
            user_id: ID пользователя
            project_id: ID проекта
            
        Returns:
            Словарь со сводкой по проекту
        """
        try:
            # Получаем проект
            project_result = await session.execute(
                select(Project).where(Project.project_id == project_id)
            )
            project = project_result.scalar_one_or_none()
            
            if not project:
                raise ValueError(f"Проект {project_id} не найден")
            
            # Получаем записи по проекту
            entries_result = await session.execute(
                select(WorkEntry).options(
                    selectinload(WorkEntry.work_type)
                ).where(
                    and_(
                        WorkEntry.user_id == user_id,
                        WorkEntry.project_id == project_id,
                        WorkEntry.is_deleted == False
                    )
                )
            )
            entries = list(entries_result.scalars().all())
            
            # Рассчитываем статистику
            stats = calculate_work_statistics(entries)
            
            # Группируем по типам работ
            work_types_data = await WorkerReportService._group_by_work_types(entries)
            
            return {
                "project": {
                    "project_id": project.project_id,
                    "name": project.name,
                    "address": project.address
                },
                "statistics": {
                    **stats,
                    "total_sum_formatted": format_currency(stats["total_sum"]),
                    "average_sum_formatted": format_currency(stats["average_sum_per_entry"])
                },
                "work_types": work_types_data
            }
            
        except Exception as e:
            logger.error(f"Ошибка получения сводки по проекту {project_id}: {e}")
            raise
