# 📋 План реализации функций директора

**Дата создания**: 29.06.2025  
**Статус**: 🎯 ГОТОВ К РЕАЛИЗАЦИИ  
**Checkpoint**: 6 - Команды директора

## 🎯 ЦЕЛЬ

Реализовать полную функциональность директора согласно `role_director.md` с использованием Reply-клавиатур вместо заглушек "в разработке".

## 📋 ТЕКУЩЕЕ СОСТОЯНИЕ

### ✅ Что уже готово
- **Архитектура Reply-клавиатур** — полностью реализована
- **Система виртуальных прав** — работает для тестирования
- **Базовые обработчики** — созданы с заглушками
- **Навигация между меню** — функционирует
- **Права доступа** — настроены через RBAC

### 🔧 Что требует реализации
- **Логика обработчиков** — заменить заглушки на реальную функциональность
- **FSM сценарии** — создание компаний, рабочих, типов работ
- **Интеграция с сервисами** — подключить CompanyService, ProjectService
- **Отчеты и экспорт** — реализовать генерацию файлов

## 🗂️ ПЛАН РЕАЛИЗАЦИИ

### 📊 ЭТАП 1: Управление компаниями (Приоритет: ВЫСОКИЙ)

#### Файлы для изменения:
- `handlers/director.py` — обработчики Reply-кнопок
- `services/company_service.py` — логика работы с компаниями
- `keyboards/director.py` — клавиатуры управления компаниями

#### Функции для реализации:

**1.1 Список компаний**
```python
@router.message(F.text == "📋 Список компаний")
async def handle_companies_list_button(message: types.Message, **kwargs):
    # Получить компании пользователя
    # Показать список с пагинацией
    # Добавить кнопки управления
```

**1.2 Создание компании**
```python
@router.message(F.text == "➕ Создать компанию")
async def handle_create_company_button(message: types.Message, state: FSMContext):
    # Запустить FSM создания компании
    # Запросить название компании
```

**1.3 Переключение активной компании**
```python
@router.message(F.text == "🔄 Сменить компанию")
async def handle_switch_company_button(message: types.Message, **kwargs):
    # Показать список доступных компаний
    # Позволить выбрать активную
```

#### FSM сценарий создания компании:
```python
class CompanyCreationStates(StatesGroup):
    waiting_name = State()
    waiting_address = State()
    waiting_business_id = State()
    confirming = State()
```

### 👷 ЭТАП 2: Управление рабочими (Приоритет: ВЫСОКИЙ)

#### Функции для реализации:

**2.1 Список рабочих**
```python
@router.message(F.text == "📋 Список рабочих")
async def handle_workers_list_button(message: types.Message, **kwargs):
    # Получить рабочих активной компании
    # Показать с группировкой по проектам
    # Добавить статистику
```

**2.2 Создание токена для рабочего**
```python
@router.message(F.text == "🔗 Создать токен рабочего")
async def handle_create_worker_token_button(message: types.Message, **kwargs):
    # Создать токен через TokenService
    # Сгенерировать ссылку регистрации
    # Показать QR-код (опционально)
```

**2.3 Управление правами рабочих**
```python
@router.message(F.text == "⚙️ Права рабочих")
async def handle_worker_permissions_button(message: types.Message, **kwargs):
    # Показать список рабочих
    # Позволить изменить права
    # Заблокировать/разблокировать
```

### 📊 ЭТАП 3: Система отчетов (Приоритет: СРЕДНИЙ)

#### Функции для реализации:

**3.1 Отчет по дате**
```python
@router.message(F.text == "📅 Отчёт по дате")
async def handle_date_report_button(message: types.Message, state: FSMContext):
    # Запросить период
    # Сгенерировать отчет
    # Показать статистику
```

**3.2 Отчет по рабочему**
```python
@router.message(F.text == "👷 Отчёт по рабочему")
async def handle_worker_report_button(message: types.Message, **kwargs):
    # Показать список рабочих
    # Выбрать рабочего
    # Сгенерировать персональный отчет
```

**3.3 Отчет по проекту**
```python
@router.message(F.text == "🏗️ Отчёт по проекту")
async def handle_project_report_button(message: types.Message, **kwargs):
    # Показать список проектов
    # Выбрать проект
    # Сгенерировать отчет по проекту
```

### 📤 ЭТАП 4: Экспорт/Импорт данных (Приоритет: СРЕДНИЙ)

#### Функции для реализации:

**4.1 Экспорт в Excel**
```python
@router.message(F.text == "📊 Экспорт Excel")
async def handle_excel_export_button(message: types.Message, state: FSMContext):
    # Выбрать данные для экспорта
    # Сгенерировать Excel файл
    # Отправить пользователю
```

**4.2 Экспорт в PDF**
```python
@router.message(F.text == "📄 Экспорт PDF")
async def handle_pdf_export_button(message: types.Message, state: FSMContext):
    # Выбрать шаблон отчета
    # Сгенерировать PDF
    # Отправить пользователю
```

**4.3 Импорт типов работ**
```python
@router.message(F.text == "📥 Импорт типов работ")
async def handle_import_work_types_button(message: types.Message, state: FSMContext):
    # Запросить Excel файл
    # Валидировать данные
    # Импортировать в БД
```

### 🛠️ ЭТАП 5: Типы работ (Приоритет: СРЕДНИЙ)

#### Функции для реализации:

**5.1 Список типов работ**
```python
@router.message(F.text == "📋 Список типов работ")
async def handle_work_types_list_button(message: types.Message, **kwargs):
    # Получить типы работ по проектам
    # Показать с группировкой
    # Добавить статистику использования
```

**5.2 Создание типа работы**
```python
@router.message(F.text == "➕ Создать тип работы")
async def handle_create_work_type_button(message: types.Message, state: FSMContext):
    # Запустить FSM создания
    # Запросить название, единицы, ставку
```

**5.3 Редактирование типов работ**
```python
@router.message(F.text == "✏️ Редактировать типы работ")
async def handle_edit_work_types_button(message: types.Message, **kwargs):
    # Показать список типов работ
    # Позволить выбрать для редактирования
    # Запустить FSM редактирования
```

## 🔧 ТЕХНИЧЕСКИЕ ДЕТАЛИ

### Интеграция с сервисами
```python
# В каждом обработчике использовать:
from services.company_service import CompanyService
from services.project_service import ProjectService
from services.work_type_service import WorkTypeService
from services.auth_service import AuthService

# Получение данных пользователя:
user_data = kwargs.get('user_data')
active_company_id = user_data.get('active_company_id')
```

### FSM состояния
```python
# Создать новые состояния в states.py:
class DirectorCompanyStates(StatesGroup):
    creating_name = State()
    creating_address = State()
    creating_business_id = State()
    confirming_creation = State()

class DirectorReportStates(StatesGroup):
    selecting_period = State()
    selecting_worker = State()
    selecting_project = State()
    generating = State()
```

### Обработка ошибок
```python
# В каждом обработчике добавить:
try:
    # Основная логика
    pass
except Exception as e:
    logger.error(f"Ошибка в {handler_name}: {e}")
    await message.answer("❌ Произошла ошибка. Попробуйте позже.")
```

## ✅ КРИТЕРИИ ГОТОВНОСТИ

### Функциональные требования
- [ ] Все кнопки директора работают (не показывают "в разработке")
- [ ] FSM сценарии создания компаний/рабочих/типов работ работают
- [ ] Отчеты генерируются и отображаются корректно
- [ ] Экспорт в Excel/PDF функционирует
- [ ] Импорт типов работ работает
- [ ] Навигация между меню работает плавно

### Технические требования
- [ ] Все обработчики используют Reply-клавиатуры
- [ ] Интеграция с сервисами работает
- [ ] Виртуальные права работают для тестирования
- [ ] Логирование ошибок настроено
- [ ] Код соответствует стандартам проекта

## 🚀 СЛЕДУЮЩИЕ ШАГИ

1. **Начать с ЭТАПА 1** — Управление компаниями
2. **Тестировать каждую функцию** после реализации
3. **Использовать виртуальные права** для безопасного тестирования
4. **Документировать изменения** в CHANGELOG.md
5. **Обновить checkpoint_system.md** при завершении

**Готовность к реализации**: ✅ ВЫСОКАЯ
