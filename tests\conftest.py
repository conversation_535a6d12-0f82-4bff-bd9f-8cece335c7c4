"""
Конфигурация pytest для тестов
"""
import pytest
import sys
import os

# Добавляем корневую директорию проекта в PYTHONPATH
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Отключаем логирование во время тестов
import logging
logging.disable(logging.CRITICAL)

@pytest.fixture(autouse=True)
def disable_debug_prints(monkeypatch):
    """Отключаем print'ы во время тестов"""
    def mock_print(*args, **kwargs):
        pass
    
    monkeypatch.setattr("builtins.print", mock_print)
