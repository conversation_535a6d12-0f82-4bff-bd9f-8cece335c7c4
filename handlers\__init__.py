"""
Handlers package - обработчики команд бота

Регистрация всех роутеров в одном месте.
"""
from aiogram import Dispatcher

from .common import router as common_router
from .admin import router as admin_router
from .director import router as director_router
from .export import router as export_router
from .project import router as project_router
# Новые обработчики для рабочего (CHECKPOINT 7)
from .worker import router as worker_router
from .work_entry import router as work_entry_router
from .worker_project import router as worker_project_router


def register_all_handlers(dp: Dispatcher):
    """Регистрация всех обработчиков"""
    dp.include_router(common_router)
    dp.include_router(admin_router)
    dp.include_router(director_router)
    dp.include_router(export_router)
    dp.include_router(project_router)
    # Регистрация обработчиков рабочего (CHECKPOINT 7)
    dp.include_router(worker_router)
    dp.include_router(work_entry_router)
    dp.include_router(worker_project_router)
