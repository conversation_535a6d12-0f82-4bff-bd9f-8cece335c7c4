"""
Тесты для ImportService

Проверяет все методы сервиса импорта данных.
"""
import pytest
import os
import tempfile
from unittest.mock import patch, AsyncMock, MagicMock
import pandas as pd

from services.import_service import ImportService


class TestImportService:
    """Тесты для ImportService"""

    @pytest.fixture
    def sample_excel_data(self):
        """Тестовые данные Excel"""
        return pd.DataFrame({
            'Название': ['Монтаж', 'Демонтаж', 'Покраска'],
            'Единица': ['час', 'м²', 'м²'],
            'Ставка': [25.50, 20.00, 15.75]
        })

    @pytest.fixture
    def invalid_excel_data(self):
        """Некорректные данные Excel"""
        return pd.DataFrame({
            'Название': ['', 'Демонтаж', 'Покраска'],
            'Единица': ['час', '', 'м²'],
            'Ставка': ['invalid', 20.00, -5.00]
        })

    @pytest.mark.asyncio
    async def test_import_work_types_success(self, sample_excel_data):
        """Тест успешного импорта типов работ"""
        with patch('services.import_service.PANDAS_AVAILABLE', True):
            with patch('services.import_service.pd.read_excel') as mock_read_excel:
                with patch('services.import_service.os.path.exists') as mock_exists:
                    with patch('services.import_service.WorkTypeService.validate_work_type_data') as mock_validate:
                        with patch('services.import_service.WorkTypeService.create_work_type') as mock_create:
                            
                            # Настройка моков
                            mock_exists.return_value = True
                            mock_read_excel.return_value = sample_excel_data
                            
                            # Мок валидации - все данные корректны
                            mock_validate.side_effect = [
                                {'is_valid': True, 'cleaned_data': {'name': 'Монтаж', 'unit': 'час', 'rate': 25.50}},
                                {'is_valid': True, 'cleaned_data': {'name': 'Демонтаж', 'unit': 'м²', 'rate': 20.00}},
                                {'is_valid': True, 'cleaned_data': {'name': 'Покраска', 'unit': 'м²', 'rate': 15.75}}
                            ]
                            
                            # Мок создания - все успешно
                            mock_create.side_effect = [
                                {'id': 1, 'name': 'Монтаж'},
                                {'id': 2, 'name': 'Демонтаж'},
                                {'id': 3, 'name': 'Покраска'}
                            ]
                            
                            result = await ImportService.import_work_types_from_excel(
                                file_path='/tmp/test.xlsx',
                                company_id=1,
                                created_by=123
                            )
                            
                            # Проверки
                            assert result['success'] is True
                            assert result['imported'] == 3
                            assert result['skipped'] == 0
                            assert len(result['errors']) == 0
                            assert result['total_rows'] == 3

    @pytest.mark.asyncio
    async def test_import_work_types_pandas_not_available(self):
        """Тест импорта когда pandas недоступен"""
        with patch('services.import_service.PANDAS_AVAILABLE', False):
            result = await ImportService.import_work_types_from_excel(
                file_path='/tmp/test.xlsx',
                company_id=1,
                created_by=123
            )
            
            # Проверки
            assert result['success'] is False
            assert 'pandas не установлен' in result['error']
            assert result['imported'] == 0

    @pytest.mark.asyncio
    async def test_import_work_types_file_not_found(self):
        """Тест импорта несуществующего файла"""
        with patch('services.import_service.PANDAS_AVAILABLE', True):
            with patch('services.import_service.os.path.exists') as mock_exists:
                
                mock_exists.return_value = False
                
                result = await ImportService.import_work_types_from_excel(
                    file_path='/tmp/nonexistent.xlsx',
                    company_id=1,
                    created_by=123
                )
                
                # Проверки
                assert result['success'] is False
                assert result['error'] == 'Файл не найден'
                assert result['imported'] == 0

    @pytest.mark.asyncio
    async def test_import_work_types_invalid_data(self, invalid_excel_data):
        """Тест импорта с некорректными данными"""
        with patch('services.import_service.PANDAS_AVAILABLE', True):
            with patch('services.import_service.pd.read_excel') as mock_read_excel:
                with patch('services.import_service.os.path.exists') as mock_exists:
                    with patch('services.import_service.WorkTypeService.validate_work_type_data') as mock_validate:
                        with patch('services.import_service.WorkTypeService.create_work_type') as mock_create:
                            
                            # Настройка моков
                            mock_exists.return_value = True
                            mock_read_excel.return_value = invalid_excel_data
                            
                            # Мок валидации - разные результаты
                            mock_validate.side_effect = [
                                {'is_valid': False, 'errors': ['Название не может быть пустым']},
                                {'is_valid': False, 'errors': ['Единица измерения не может быть пустой']},
                                {'is_valid': False, 'errors': ['Ставка должна быть больше нуля']}
                            ]
                            
                            result = await ImportService.import_work_types_from_excel(
                                file_path='/tmp/test.xlsx',
                                company_id=1,
                                created_by=123
                            )
                            
                            # Проверки
                            assert result['success'] is True  # Процесс завершился
                            assert result['imported'] == 0   # Но ничего не импортировано
                            assert result['skipped'] == 2    # 2 строки пропущены (пустая строка + ошибки)
                            assert len(result['errors']) > 0

    @pytest.mark.asyncio
    async def test_import_work_types_missing_columns(self):
        """Тест импорта с отсутствующими столбцами"""
        incomplete_data = pd.DataFrame({
            'Название': ['Монтаж', 'Демонтаж'],
            'Единица': ['час', 'м²']
            # Отсутствует столбец 'Ставка'
        })
        
        with patch('services.import_service.PANDAS_AVAILABLE', True):
            with patch('services.import_service.pd.read_excel') as mock_read_excel:
                with patch('services.import_service.os.path.exists') as mock_exists:
                    
                    mock_exists.return_value = True
                    mock_read_excel.return_value = incomplete_data
                    
                    result = await ImportService.import_work_types_from_excel(
                        file_path='/tmp/test.xlsx',
                        company_id=1,
                        created_by=123
                    )
                    
                    # Проверки
                    assert result['success'] is False
                    assert 'Не найдены обязательные столбцы' in result['error']
                    assert result['imported'] == 0

    @pytest.mark.asyncio
    async def test_import_work_types_empty_file(self):
        """Тест импорта пустого файла"""
        empty_data = pd.DataFrame()
        
        with patch('services.import_service.PANDAS_AVAILABLE', True):
            with patch('services.import_service.pd.read_excel') as mock_read_excel:
                with patch('services.import_service.os.path.exists') as mock_exists:
                    
                    mock_exists.return_value = True
                    mock_read_excel.return_value = empty_data
                    
                    result = await ImportService.import_work_types_from_excel(
                        file_path='/tmp/test.xlsx',
                        company_id=1,
                        created_by=123
                    )
                    
                    # Проверки
                    assert result['success'] is False
                    assert result['error'] == 'Excel файл пустой'
                    assert result['imported'] == 0

    def test_detect_columns_russian(self):
        """Тест определения столбцов на русском языке"""
        columns = ['Название', 'Единица измерения', 'Ставка']
        
        result = ImportService._detect_columns(columns)
        
        # Проверки
        assert result['name'] == 'Название'
        assert result['unit'] == 'Единица измерения'
        assert result['rate'] == 'Ставка'

    def test_detect_columns_english(self):
        """Тест определения столбцов на английском языке"""
        columns = ['name', 'unit', 'rate']
        
        result = ImportService._detect_columns(columns)
        
        # Проверки
        assert result['name'] == 'name'
        assert result['unit'] == 'unit'
        assert result['rate'] == 'rate'

    def test_detect_columns_mixed_case(self):
        """Тест определения столбцов в смешанном регистре"""
        columns = ['НАЗВАНИЕ', 'Ед.Изм.', 'Price']
        
        result = ImportService._detect_columns(columns)
        
        # Проверки
        assert result['name'] == 'НАЗВАНИЕ'
        assert result['unit'] == 'Ед.Изм.'
        assert result['rate'] == 'Price'

    def test_detect_columns_missing(self):
        """Тест определения столбцов с отсутствующими полями"""
        columns = ['Название', 'Описание']  # Нет единицы и ставки
        
        result = ImportService._detect_columns(columns)
        
        # Проверки
        assert result['name'] == 'Название'
        assert result['unit'] is None
        assert result['rate'] is None

    @pytest.mark.asyncio
    async def test_validate_excel_file_success(self, sample_excel_data):
        """Тест успешной валидации Excel файла"""
        with patch('services.import_service.PANDAS_AVAILABLE', True):
            with patch('services.import_service.os.path.exists') as mock_exists:
                with patch('services.import_service.pd.read_excel') as mock_read_excel:
                    with patch('services.import_service.Path') as mock_path:
                        
                        mock_exists.return_value = True
                        mock_read_excel.return_value = sample_excel_data
                        mock_path.return_value.suffix.lower.return_value = '.xlsx'
                        
                        result = await ImportService.validate_excel_file('/tmp/test.xlsx')
                        
                        # Проверки
                        assert result['is_valid'] is True
                        assert result['details']['total_rows'] == 3
                        assert result['details']['valid_rows'] == 3

    @pytest.mark.asyncio
    async def test_validate_excel_file_pandas_not_available(self):
        """Тест валидации файла когда pandas недоступен"""
        with patch('services.import_service.PANDAS_AVAILABLE', False):
            result = await ImportService.validate_excel_file('/tmp/test.xlsx')
            
            # Проверки
            assert result['is_valid'] is False
            assert result['error'] == 'pandas не установлен'

    @pytest.mark.asyncio
    async def test_validate_excel_file_wrong_extension(self):
        """Тест валидации файла с неправильным расширением"""
        with patch('services.import_service.PANDAS_AVAILABLE', True):
            with patch('services.import_service.os.path.exists') as mock_exists:
                with patch('services.import_service.Path') as mock_path:
                    
                    mock_exists.return_value = True
                    mock_path.return_value.suffix.lower.return_value = '.txt'
                    
                    result = await ImportService.validate_excel_file('/tmp/test.txt')
                    
                    # Проверки
                    assert result['is_valid'] is False
                    assert 'Неподдерживаемый формат файла' in result['error']

    def test_get_import_template(self):
        """Тест получения шаблона импорта"""
        result = ImportService.get_import_template()
        
        # Проверки
        assert 'required_columns' in result
        assert 'optional_columns' in result
        assert 'format' in result
        assert 'notes' in result
        
        # Проверка обязательных столбцов
        required_columns = result['required_columns']
        assert len(required_columns) == 3
        
        column_names = [col['name'] for col in required_columns]
        assert 'Название' in column_names
        assert 'Единица' in column_names
        assert 'Ставка' in column_names

    def test_is_available_true(self):
        """Тест проверки доступности импорта когда pandas установлен"""
        with patch('services.import_service.PANDAS_AVAILABLE', True):
            result = ImportService.is_available()
            assert result is True

    def test_is_available_false(self):
        """Тест проверки доступности импорта когда pandas не установлен"""
        with patch('services.import_service.PANDAS_AVAILABLE', False):
            result = ImportService.is_available()
            assert result is False

    @pytest.mark.asyncio
    async def test_import_work_types_exception_handling(self):
        """Тест обработки исключений при импорте"""
        with patch('services.import_service.PANDAS_AVAILABLE', True):
            with patch('services.import_service.os.path.exists') as mock_exists:
                with patch('services.import_service.pd.read_excel') as mock_read_excel:
                    
                    mock_exists.return_value = True
                    mock_read_excel.side_effect = Exception("Pandas error")
                    
                    result = await ImportService.import_work_types_from_excel(
                        file_path='/tmp/test.xlsx',
                        company_id=1,
                        created_by=123
                    )
                    
                    # Проверки
                    assert result['success'] is False
                    assert 'Ошибка чтения Excel файла' in result['error']
                    assert result['imported'] == 0
