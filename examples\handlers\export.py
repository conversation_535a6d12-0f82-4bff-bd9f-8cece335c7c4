"""
Обработчики для экспорта данных.

Команда: /export
FSM: ExportStates (6 шагов)
Права: Deprecated role check (нужно обновить на RBAC)

Логика:
1. choosing_format - выбор формата (Excel/PDF)
2. choosing_project - выбор проекта
3. choosing_period - выбор периода
4. entering_date_range - ввод диапазона дат
5. choosing_columns - выбор столбцов (только для Excel)
6. confirming_export - подтверждение экспорта

Особенности PDF:
- Фиксированный шаблон (без выбора столбцов)
- Настраиваемые параметры бланка
- Поля для подписей
- Логотип компании
"""
import logging
from aiogram import types, Dispatcher
from aiogram.dispatcher import FSMContext
import datetime

from db.dao.entry_dao import EntryDAO
from db.dao.project_dao import ProjectDAO
from db.dao.user_dao import UserDAO
from db.session import async_session
from keyboards import (
    create_main_menu, 
    create_export_format_menu, 
    create_export_period_menu, 
    create_columns_keyboard
)
from localization.texts import get_text
from services.user_service import get_user_role
from services.export_service import ExportService
from states import ExportStates
from utils.date_helpers import get_last_week_range, get_last_month_range, validate_date_range

logger = logging.getLogger(__name__)

def register_handlers_export(dp: Dispatcher):
    """Регистрирует все обработчики для сценария экспорта."""
    dp.register_message_handler(cmd_export, commands=["export"], state="*")
    dp.register_message_handler(process_format_selection, state=ExportStates.choosing_format)
    dp.register_message_handler(process_project_selection, state=ExportStates.choosing_project)
    dp.register_message_handler(process_period_selection, state=ExportStates.choosing_period)
    dp.register_message_handler(process_date_range_for_export, state=ExportStates.entering_date_range)
    dp.register_message_handler(process_column_selection, state=ExportStates.choosing_columns)
    dp.register_message_handler(process_export_confirmation, state=ExportStates.confirming_export)

async def cmd_export(message: types.Message, state: FSMContext):
    """
    Начало сценария экспорта.
    
    Проверяет:
    - Права доступа (deprecated - нужно заменить на RBAC)
    
    Показывает:
    - Выбор формата: "Excel" / "PDF"
    """
    await state.finish()
    role = await get_user_role(message.from_user.id)
    if role not in ["ROLE_WORKER", "ROLE_DIRECTOR"]:
        await message.answer(get_text("access_denied"))
        return
    await message.answer(get_text("export_choose_format"), reply_markup=create_export_format_menu())
    await ExportStates.choosing_format.set()

async def process_format_selection(message: types.Message, state: FSMContext):
    """
    Шаг 1: Выбор формата файла (PDF/Excel).
    
    Форматы:
    - "PDF" - фиксированный шаблон с настраиваемыми параметрами
    - "Excel" - настраиваемые столбцы
    """
    text = message.text
    if text.lower() == get_text("cancel").lower():
        await state.finish()
        await message.answer(get_text("export_cancelled"), reply_markup=create_main_menu())
        return
    if text not in ["PDF", "Excel"]:
        await message.answer(get_text("export_invalid_format"))
        return
    await state.update_data(export_format=text)
    
    # Получаем проекты пользователя
    async with async_session() as session:
        projects = await ProjectDAO.get_by_user_id(session, message.from_user.id)

    if not projects:
        await message.answer(get_text("no_projects_for_export"), reply_markup=create_main_menu())
        await state.finish()
        return
    
    project_map = {p.name: p.project_id for p in projects}
    await state.update_data(project_map=project_map)
    
    keyboard = types.ReplyKeyboardMarkup(resize_keyboard=True, one_time_keyboard=True)
    keyboard.add(*project_map.keys(), get_text("cancel"))
    await message.answer(get_text("export_choose_project"), reply_markup=keyboard)
    await ExportStates.choosing_project.set()

async def process_project_selection(message: types.Message, state: FSMContext):
    """
    Шаг 2: Выбор проекта.
    
    Сохраняет:
    - ID проекта
    - Название проекта
    """
    text = message.text
    if text.lower() == get_text("cancel").lower():
        await state.finish()
        await message.answer(get_text("export_cancelled"), reply_markup=create_main_menu())
        return
    
    fsm_data = await state.get_data()
    project_map = fsm_data.get("project_map", {})

    if text not in project_map:
        await message.answer(get_text("export_invalid_project"))
        return
        
    await state.update_data(project_id=project_map[text], project_name=text)
    await message.answer(get_text("export_choose_period"), reply_markup=create_export_period_menu())
    await ExportStates.choosing_period.set()

async def process_period_selection(message: types.Message, state: FSMContext):
    """
    Шаг 3: Выбор периода.
    
    Периоды:
    - "За всё время" - все записи проекта
    - "За последнюю неделю" - автоматический расчет
    - "За последний месяц" - автоматический расчет
    - "За период" - ручной ввод дат
    """
    text = message.text
    if text.lower() == get_text("cancel").lower():
        await state.finish()
        await message.answer(get_text("export_cancelled"), reply_markup=create_main_menu())
        return

    start_date, end_date = None, None
    if text == "За всё время":
        start_date, end_date = None, None
    elif text == "За последнюю неделю":
        start_date, end_date = get_last_week_range()
    elif text == "За последний месяц":
        start_date, end_date = get_last_month_range()
    elif text == "За период":
        cancel_kb = types.ReplyKeyboardMarkup(resize_keyboard=True).add(get_text("cancel"))
        await message.answer(get_text("export_enter_date_range"), reply_markup=cancel_kb)
        await ExportStates.entering_date_range.set()
        return
    else:
        await message.answer(get_text("export_invalid_period"))
        return

    await state.update_data(start_date=start_date, end_date=end_date)
    
    data = await state.get_data()
    # Для PDF пропускаем выбор столбцов
    if data.get("export_format") == "PDF":
        await state.update_data(selected_columns=["Фиксированный шаблон"]) 
        await go_to_confirmation(message, state)
    else:
        await state.update_data(selected_columns=[])
        await message.answer(
            get_text("export_choose_columns"), 
            reply_markup=create_columns_keyboard([])
        )
        await ExportStates.choosing_columns.set()

async def process_date_range_for_export(message: types.Message, state: FSMContext):
    """
    Шаг 4: Ввод диапазона дат.
    
    Формат: "01.01.2023-31.01.2023"
    Валидация через validate_date_range()
    """
    text = message.text
    if text.lower() == get_text("cancel").lower():
        await state.finish()
        await message.answer(get_text("export_cancelled"), reply_markup=create_main_menu())
        return

    is_valid, error_msg, dates = validate_date_range(text)
    if not is_valid:
        await message.answer(get_text("export_invalid_date_range", error=error_msg))
        return

    start_date, end_date = dates
    await state.update_data(start_date=start_date, end_date=end_date)
    
    data = await state.get_data()
    # Для PDF пропускаем выбор столбцов
    if data.get("export_format") == "PDF":
        await state.update_data(selected_columns=["Фиксированный шаблон"]) 
        await go_to_confirmation(message, state)
    else:
        await state.update_data(selected_columns=[])
        await message.answer(
            get_text("export_choose_columns"), 
            reply_markup=create_columns_keyboard([])
        )
        await ExportStates.choosing_columns.set()

async def process_column_selection(message: types.Message, state: FSMContext):
    """
    Шаг 5: Выбор столбцов (только для Excel).
    
    Столбцы:
    - Дата, Тип работы, Описание, Количество, Ед. изм., Ставка, Сумма
    
    Логика:
    - Клик по столбцу добавляет/убирает галочку
    - "Готово" завершает выбор
    """
    text = message.text
    if text.lower() == get_text("cancel").lower():
        await state.finish()
        await message.answer(get_text("export_cancelled"), reply_markup=create_main_menu())
        return
    
    if text == get_text("done"):
        await go_to_confirmation(message, state)
        return
    
    data = await state.get_data()
    selected_columns = data.get("selected_columns", [])
    
    # Убираем галочку если есть, добавляем если нет
    clean_text = text.replace("✓ ", "")
    if clean_text in selected_columns:
        selected_columns.remove(clean_text)
    else:
        selected_columns.append(clean_text)
    
    await state.update_data(selected_columns=selected_columns)
    await message.answer(
        get_text("export_columns_updated"), 
        reply_markup=create_columns_keyboard(selected_columns)
    )

async def go_to_confirmation(message: types.Message, state: FSMContext):
    """Переход к подтверждению экспорта."""
    data = await state.get_data()
    
    # Формируем сводку
    summary_lines = [
        f"📊 Формат: {data.get('export_format')}",
        f"📁 Проект: {data.get('project_name')}",
    ]
    
    if data.get('start_date') and data.get('end_date'):
        summary_lines.append(f"📅 Период: {data['start_date'].strftime('%d.%m.%Y')} - {data['end_date'].strftime('%d.%m.%Y')}")
    elif data.get('start_date') is None:
        summary_lines.append("📅 Период: За всё время")
    
    if data.get('export_format') == 'Excel':
        columns = data.get('selected_columns', [])
        if columns:
            summary_lines.append(f"📋 Столбцы: {', '.join(columns)}")
        else:
            summary_lines.append("📋 Столбцы: Все")
    else:
        summary_lines.append("📋 Шаблон: Стандартный PDF бланк")
    
    summary = "\n".join(summary_lines)
    
    keyboard = types.ReplyKeyboardMarkup(resize_keyboard=True)
    keyboard.add("✅ Экспортировать", "❌ Отмена")
    
    await message.answer(f"{get_text('export_confirm')}\n\n{summary}", reply_markup=keyboard)
    await ExportStates.confirming_export.set()

async def process_export_confirmation(message: types.Message, state: FSMContext):
    """
    Шаг 6: Подтверждение и генерация файла.
    
    Генерирует:
    - Excel файл через ExportService.export_to_excel()
    - PDF файл через ExportService.export_to_pdf()
    
    PDF параметры:
    - Логотип компании
    - Информация о проекте
    - Детальные записи
    - Поля для подписей
    - Итоговые суммы
    """
    if message.text != "✅ Экспортировать":
        await state.finish()
        await message.answer(get_text("export_cancelled"), reply_markup=create_main_menu())
        return
    
    data = await state.get_data()
    user_id = message.from_user.id
    
    try:
        await message.answer("⏳ Генерирую файл...")
        
        # Получаем данные для экспорта
        async with async_session() as session:
            entries = await EntryDAO.get_for_export(
                session=session,
                project_id=data['project_id'],
                start_date=data.get('start_date'),
                end_date=data.get('end_date'),
                user_id=user_id
            )
        
        if not entries:
            await message.answer(get_text("export_no_data"), reply_markup=create_main_menu())
            await state.finish()
            return
        
        # Генерируем файл
        if data['export_format'] == 'Excel':
            file_path = await ExportService.export_to_excel(
                entries=entries,
                project_name=data['project_name'],
                selected_columns=data.get('selected_columns', []),
                start_date=data.get('start_date'),
                end_date=data.get('end_date')
            )
        else:  # PDF
            file_path = await ExportService.export_to_pdf(
                entries=entries,
                project_name=data['project_name'],
                start_date=data.get('start_date'),
                end_date=data.get('end_date'),
                # PDF параметры бланка
                include_logo=True,
                include_signatures=True,
                include_totals=True,
                company_info=True
            )
        
        # Отправляем файл
        with open(file_path, 'rb') as file:
            if data['export_format'] == 'Excel':
                await message.answer_document(
                    types.InputFile(file, filename=f"worklog_{data['project_name']}.xlsx"),
                    caption=get_text("export_success")
                )
            else:
                await message.answer_document(
                    types.InputFile(file, filename=f"worklog_{data['project_name']}.pdf"),
                    caption=get_text("export_success")
                )
        
        await message.answer(get_text("export_completed"), reply_markup=create_main_menu())
        
    except Exception as e:
        logger.exception(f"Ошибка экспорта для user {user_id}: {e}")
        await message.answer(get_text("export_error"), reply_markup=create_main_menu())
    
    finally:
        await state.finish()
