Отлично, финализируем функционал для роли рабочего:

## ✅ Финальная версия меню **рабочего**

Полностью адаптировано под текущую логику, интерфейсные стандарты и реализацию проекта с учетом финляндской локализации.

---

### 📋 **Главное меню рабочего**

```plaintext
📝 Добавить работу
📋 Мои записи
📊 Мой отчёт
🏗️ Выбрать проект
➕ Новый проект
✏️ Редактировать проект
📤 Экспорт данных
ℹ️ Инфо
```

---

### 🔹 **1. Добавить работу** (`/addwork`)

> Основная функция для ведения учёта рабочего времени

```plaintext
[📅 Сегодня]
[📅 Ввести дату]
[🔙 Отмена]
```

#### FSM состояния (7 шагов):

1. **waiting_for_date_choice** - выбор даты работы
2. **waiting_for_date_input** - ввод конкретной даты (формат: ДД.ММ.ГГГГ)
3. **waiting_for_work_type** - выбор типа работы из активного проекта
4. **waiting_for_description** - описание выполненной работы
5. **waiting_for_quantity** - количество (часы, м², шт и т.д.)
6. **confirming_entry** - подтверждение записи с расчётом суммы
7. **Сохранение** - создание записи в БД

#### Зависимости:
* Требует установленный активный проект (`/setproject`)
* Требует типы работ в проекте
* Права: `can_add_work = True`

#### Пример диалога:
```
Бот: Выберите дату работы:
[📅 Сегодня] [📅 Ввести дату] [🔙 Отмена]

Пользователь: Сегодня

Бот: Выберите тип работы:
[🔨 Монтаж] [⚡ Электромонтаж] [🔧 Сантехника] [🔙 Отмена]

Пользователь: Монтаж

Бот: Опишите выполненную работу:
Пользователь: Установка окон на 2 этаже

Бот: Введите количество (час):
Пользователь: 8,5

Бот: Подтвердите запись:
📅 Дата: 28.06.2024
🔨 Тип: Монтаж  
📝 Описание: Установка окон на 2 этаже
⏱️ Количество: 8,5 час
💰 Ставка: 25,00 €/час
💵 Сумма: 212,50 €

[✅ Да] [❌ Нет]
```

---

### 🔹 **2. Мои записи** (`/list`)

> Просмотр всех записей рабочего с фильтрацией и пагинацией

```plaintext
[📋 Все записи]
[📅 За период]
[🔙 Назад]
```

#### FSM состояния (2 шага):
1. **selecting_filter** - выбор фильтра
2. **entering_date_range** - ввод диапазона дат (формат: ДД.ММ.ГГГГ-ДД.ММ.ГГГГ)

#### Функции:
* **Пагинация** - по 10 записей на страницу
* **Фильтрация** - все записи или за период
* **Inline-кнопки** для каждой записи:

```plaintext
[✏️ Редактировать] [🗑️ Удалить]
```

#### Пример вывода:
```
📋 Записи о работе за всё время
Страница 1 из 3 (всего записей: 25)

1. 26.06.2024 - Монтаж
   Установка дверей в офисе
   6,0 час × 25,00 € = 150,00 €

2. 27.06.2024 - Электромонтаж  
   Прокладка кабеля
   4,5 час × 30,00 € = 135,00 €

💰 Итого по странице: 285,00 €
💰 Общий итог: 2 150,00 €

[⬅️ Назад] [1/3] [Вперед ➡️]
[🔙 В главное меню]
```

---

### 🔹 **3. Мой отчёт** (`/report`)

> Генерация персонального отчёта рабочего

```plaintext
[📊 Отчёт по активному проекту]
[📅 Отчёт за период]
[🔙 Назад]
```

#### Параметры отчёта:
* Фильтрация по активному проекту
* Группировка по типам работ
* Подсчёт общих сумм и количества
* Средние показатели

#### Пример отчёта:
```
📊 Отчёт по проекту: Rakennuskohde Espoo

🔨 Монтаж: 45,5 час - 1 137,50 €
⚡ Электромонтаж: 32,0 час - 960,00 €  
🔧 Сантехника: 18,5 час - 462,50 €

💰 Общая сумма: 2 560,00 €
⏱️ Общее время: 96,0 час
📈 Средняя ставка: 26,67 €/час
📅 Период: 01.06.2024 - 28.06.2024
```

---

### 🔹 **4. Выбрать проект** (`/setproject`)

> Установка активного проекта для работы

```plaintext
[🏗️ Rakennuskohde Helsinki]
[🏢 Toimistoremontti Vantaa]  
[🏠 Kotiremontti Espoo]
[🔙 Назад]
```

#### FSM состояния (1 шаг):
1. **selecting_project** - выбор проекта из доступных

#### Логика:
* Показывает только проекты компании рабочего
* Устанавливает `active_project_id` в таблице `users`
* Обновляет доступные типы работ

#### Зависимости:
* Рабочий должен быть привязан к компании
* В компании должны быть созданы проекты

---

### 🔹 **5. Новый проект** (`/newproject`)

> Создание нового проекта с типами работ

```plaintext
[➕ Создать новый проект]
[📋 Скопировать из существующего]
[🔙 Назад]
```

#### FSM состояния (8 шагов):
1. **creating_project_name** - ввод названия проекта
2. **creating_project_address** - ввод адреса проекта
3. **choosing_copy_source** - выбор источника копирования типов работ
4. **adding_work_type_name** - ввод названия типа работы
5. **adding_work_type_unit** - ввод единицы измерения
6. **adding_work_type_rate_type** - выбор типа ставки
7. **adding_work_type_value** - ввод значения ставки
8. **confirming_add_another_work_type** - подтверждение добавления еще типов

#### Зависимости:
* Права: `can_manage_projects = True`
* Рабочий должен быть привязан к компании
* Уникальность названия в рамках компании

#### Пример диалога создания проекта:
```
Бот: Введите название проекта:
Пользователь: Toimistoremontti Vantaa

Бот: Введите адрес проекта (можно пропустить):
Пользователь: Kielotie 15, 01300 Vantaa

Бот: У вас есть проекты. Скопировать типы работ?
[📋 Скопировать из существующего] [➕ Создать новые] [🔙 Отмена]

Пользователь: Создать новые

Бот: Введите название типа работы:
Пользователь: Монтаж окон

Бот: Введите единицу измерения:
Пользователь: час

Бот: Выберите тип ставки:
[💰 Фиксированная ставка] [📊 Ставка за единицу] [🔙 Отмена]

Пользователь: Ставка за единицу

Бот: Введите ставку за час:
Пользователь: 28,50

Бот: Тип работы добавлен:
🔨 Монтаж окон: 28,50 €/час

Добавить еще один тип работы?
[✅ Да] [❌ Нет]

Пользователь: Да
(цикл повторяется)

Пользователь: Нет

Бот: ✅ Проект "Toimistoremontti Vantaa" создан!
Добавлено типов работ: 3
Проект установлен как активный.
```

#### Циклическое добавление типов работ:
* Можно добавить неограниченное количество типов
* Каждый тип: название → единица → тип ставки → значение
* Валидация на каждом шаге
* Автоматическая установка как активного проекта

#### Типы ставок:
* **Фиксированная** - одна сумма за всю работу
* **За единицу** - умножается на количество

---

### 🔹 **6. Редактировать проект** (`/editproject`)

> Редактирование существующих проектов и типов работ

```plaintext
[🏗️ Rakennuskohde Helsinki]
[🏢 Toimistoremontti Vantaa]
[🏠 Kotiremontti Espoo]
[🔙 Назад]
```

#### FSM состояния (4 основных):
1. **selecting_project** - выбор проекта для редактирования
2. **choosing_action** - выбор действия (проект/типы работ)
3. **editing_field** - редактирование поля проекта
4. **managing_work_types** - управление типами работ

#### После выбора проекта:
```plaintext
[✏️ Редактировать проект]
[🔧 Управление типами работ]
[🔙 Назад к списку]
```

#### ✏️ Редактирование проекта:
```plaintext
[📝 Изменить название]
[📍 Изменить адрес]
[🗑️ Удалить проект]
[🔙 Назад]
```

#### 🔧 Управление типами работ:
```plaintext
Текущие типы работ:

🔨 Монтаж окон - 28,50 €/час [✏️] [🗑️]
⚡ Электромонтаж - 32,00 €/час [✏️] [🗑️]
🔧 Сантехника - 25,00 €/час [✏️] [🗑️]

[➕ Добавить новый тип]
[📋 Копировать из другого проекта]
[🔙 Назад]
```

#### Редактирование типа работы:
```plaintext
Редактирование: Монтаж окон

[📝 Изменить название]
[📏 Изменить единицу (час)]
[💰 Изменить ставку (28,50 €)]
[🔄 Изменить тип ставки]
[🔙 Назад]
```

#### Зависимости:
* Права: `can_manage_projects = True`
* Доступ только к проектам своей компании
* Нельзя удалить проект с существующими записями
* При удалении типа работы - проверка на использование

#### Безопасность:
* Подтверждение удаления проекта/типа работы
* Проверка на существующие записи
* Валидация всех изменений
* Логирование операций

---

### 🔹 **7. Экспорт данных** (`/export`)

> Экспорт личных данных рабочего

```plaintext
[📤 Экспорт Excel]
[📤 Экспорт PDF]
[🔙 Назад]
```

#### FSM состояния (6 шагов):
1. **choosing_format** - выбор формата (Excel/PDF)
2. **choosing_project** - выбор проекта (если несколько)
3. **choosing_period** - выбор периода
4. **entering_date_range** - ввод дат (если "За период")
5. **choosing_columns** - выбор столбцов (только Excel)
6. **confirming_export** - подтверждение и генерация

#### 📤 Экспорт Excel:
* **Настраиваемые столбцы**:
  - Дата
  - Тип работы  
  - Описание
  - Количество
  - Ед. изм.
  - Ставка
  - Сумма

#### 📤 Экспорт PDF:
* **Фиксированный шаблон** с параметрами:
  - Логотип компании
  - Информация о рабочем
  - Детальные записи
  - Поля для подписей
  - Итоговые суммы

#### Пример PDF параметров:
```python
PDF_CONFIG = {
    "company_logo": True,
    "worker_info": True,
    "signatures": True,
    "totals": True,
    "currency": "€",
    "locale": "fi_FI"
}
```

---

### 🔹 **8. Инфо**

```plaintext
👤 Имя: Mikael Virtanen
🏢 Компания: Rakennus Oy Helsinki  
🏗️ Активный проект: Toimistoremontti Vantaa
🆔 ID: *********
🎯 Роль: Рабочий
📅 Зарегистрирован: 2024-05-15
💰 Заработано за месяц: 3 245,50 €
⏱️ Отработано за месяц: 124,5 час
```

---

## 🔧 **Дополнительные функции**

### ✏️ **Редактирование записи** (`/edit`)

#### FSM состояния (3 шага):
1. **selecting_entry_to_edit** - выбор записи из последних 10
2. **choosing_edit_field** - выбор поля (дата, тип, количество, описание)
3. **entering_edit_value** - ввод нового значения

#### Ограничения:
* Можно редактировать только свои записи
* Проверка доступа через `AccessControlService`

### 🗑️ **Удаление записи** (`/delete`)

#### FSM состояния (2 шага):
1. **selecting_entry_to_delete** - выбор записи
2. **confirming_delete** - подтверждение удаления

#### Безопасность:
* Двойное подтверждение
* Проверка прав доступа
* Логирование операций

---

## 📊 **Интеграция с системой**

### 🔐 **RBAC права рабочего**:
```python
worker_permissions = {
    "can_add_work": True,
    "can_view_own_reports": True,
    "can_edit_own_entries": True,
    "can_export_own_data": True,
    "can_manage_projects": True,  # Для создания/редактирования проектов
    "can_manage_workers": False,
    "is_admin": False
}
```

### 🗄️ **Связанные таблицы**:
* `users` - профиль рабочего
* `user_company_roles` - роль в компании
* `work_entries` - записи о работе
* `projects` - доступные проекты
* `work_types` - типы работ

### 🌐 **Локализация (Финляндия)**:
* Валюта: € (евро)
* Формат даты: ДД.ММ.ГГГГ
* Названия компаний: "Rakennus Oy", "Toimisto Helsinki"
* Единицы измерения: час, м², шт, кг
* Язык интерфейса: русский с финскими терминами

## 🚀 **Технические детали реализации**

### 📝 **Команды и их файлы**:
```
/addwork     → handlers/addwork.py (AddWorkStates)
/list        → handlers/list.py (ListStates)
/report      → handlers/report.py (без FSM)
/setproject  → handlers/project/common.py (ProjectSelectionStates)
/newproject  → handlers/project/new_project.py (NewProjectStates)
/editproject → handlers/project/edit_project.py (EditProjectStates)
/export      → handlers/export.py (ExportStates)
/edit        → handlers/edit.py (EditWorkStates)
/delete      → handlers/delete.py (DeleteWorkStates)
```

### 🔄 **Middleware проверки**:
```python
@require_permission("can_add_work")
async def cmd_add_work(message: types.Message, state: FSMContext, **kwargs):
    # Логика команды /addwork
```

### 📊 **Расчёт сумм**:
```python
# services/calculator.py
def calculate_sum(work_type, quantity: float) -> float:
    if work_type.rate_type == 'fixed':
        return work_type.value
    elif work_type.rate_type == 'per_unit':
        return work_type.value * quantity
```

### 🗃️ **Доступ к данным**:
```python
# Только записи своей компании
entries = await EntryDAO.get_by_user_and_company(
    session, user_id, company_id
)
```

### 🎨 **Адаптивное меню**:
```python
def create_worker_menu(user_permissions: Dict) -> ReplyKeyboardMarkup:
    keyboard = ReplyKeyboardMarkup(resize_keyboard=True, row_width=2)

    if user_permissions.get("can_add_work"):
        keyboard.add("📝 Добавить работу")
        keyboard.add("📋 Мои записи", "📊 Мой отчёт")

    keyboard.add("🏗️ Выбрать проект")

    if user_permissions.get("can_manage_projects"):
        keyboard.add("➕ Новый проект", "✏️ Редактировать проект")

    if user_permissions.get("can_export_own_data"):
        keyboard.add("📤 Экспорт данных")

    keyboard.add("ℹ️ Инфо")
    return keyboard
```

---

## 📋 **Чек-лист для разработчика**

### ✅ **Обязательные проверки**:
- [ ] Рабочий привязан к компании
- [ ] Установлен активный проект
- [ ] Проект содержит типы работ
- [ ] Валидация всех входных данных
- [ ] Проверка прав доступа к записям
- [ ] Уникальность названий проектов в компании
- [ ] Проверка использования типов работ перед удалением
- [ ] Логирование всех операций

### 🔒 **Безопасность**:
- [ ] Изоляция данных по компаниям
- [ ] Доступ только к своим записям
- [ ] Валидация токенов регистрации
- [ ] Защита от SQL-инъекций
- [ ] Ограничение размера файлов экспорта

### 🌍 **Локализация**:
- [ ] Тексты на русском языке
- [ ] Финляндские названия компаний
- [ ] Валюта в евро (€)
- [ ] Формат даты ДД.ММ.ГГГГ
- [ ] Поддержка запятой как разделителя

---

*Документ содержит полную спецификацию роли рабочего для точного воспроизведения в новом проекте.*
