Отлично, финализируем оба пункта:

## ✅ 1. Финальная версия меню **директора**

Полностью адаптировано под текущую логику, интерфейсные стандарты и реализацию проекта.

---

### 📋 **Главное меню директора**

```plaintext
🏢 Управление компаниями
👷 Управление рабочими
📊 Просмотр отчётов
📁 Экспорт / Импорт данных
🛠️ Редактировать типы работ
ℹ️ Инфо
```

---

### 🔹 **1. Управление компаниями**

> Позволяет создавать, переключать и деактивировать компании

```plaintext
[➕ Добавить компанию]
[🔁 Сменить активную компанию]
[🗑️ Удалить компанию (soft delete)]
[🔙 Назад]
```

#### Детали:

* **Добавить** — ввод названия → сохраняется в `companies`, `company_id` привязывается к директору
* **Сменить** — из списка собственных компаний → переключение `active_company_id`
* **Удалить** — `is_deleted = True` (данные сохраняются, доступ блокируется)

---

### 🔹 **2. Управление рабочими**

> Управление всеми рабочими, привязанными к активной компании

```plaintext
[➕ Добавить рабочего]
[📄 Список рабочих]
[🔙 Назад]
```

#### ➕ Добавить рабочего

* Генерация **токен-ссылки** для регистрации рабочего
* После регистрации: создаётся запись в `users`, `company_id` = текущей компании директора

#### 📄 Список рабочих

* Отображается имя (из `display_name` или аналогичного поля в `users`)
* **Inline-кнопки**:

  ```plaintext
  [Имя рабочего 1] [⚙️]
  [Имя рабочего 2] [⚙️]
  ```

  При нажатии на \[⚙️] — открывается подменю:

  ```plaintext
  [✏️ Изменить имя]
  [🗑️ Удалить рабочего]
  [🔙 Назад]
  ```

---

### 🔹 **3. Просмотр отчётов**

> Быстрый доступ к статистике по компаниям

```plaintext
[📆 Отчёт по дате]
[👷 Отчёт по рабочему]
[🏗️ Отчёт по проекту]
[🔙 Назад]
```

* Данные фильтруются по `company_id`
* Все отчёты работают как интерфейс к команде `/report`

---

### 🔹 **4. Экспорт / Импорт данных**

> Команда `/export` с подфункциями

```plaintext
[📤 Экспорт Excel]
[📤 Экспорт PDF]
[📥 Импорт типов работ]
[🔙 Назад]
```

#### 📤 Экспорт Excel:

* Фильтры:

  * Компания (если их несколько)
  * Проект
  * Рабочий
  * Диапазон дат
  * Какие данные включать (тип работы, объёмы, суммы)

#### 📤 Экспорт PDF:

* Формирует **часовые листы**
* Для одного или всех выбранных рабочих
* Каждый рабочий на отдельном листе

#### 📥 Импорт типов работ:

* Загружается Excel-файл в стандартном шаблоне
* Присваивается к проекту и рабочему
* Повторяет текущую реализацию у рабочего, доступ через директорию `example/`

---

### 🔹 **5. Редактировать типы работ**

> Повторяет реализацию у рабочего

```plaintext
[📋 Список типов работ]
[✏️ Редактировать]
[📥 Импортировать из Excel]
[🔙 Назад]
```

* Используются компоненты из ранее реализованных `/editproject`
* Доступ к проектам компании
* Редактирование ставок, единиц измерения, названий

---

### 🔹 **6. Инфо**

```plaintext
👤 Имя: Иван Петров  
🏢 Активная компания: BuildTeam Oy  
🆔 ID: 123456789  
🎯 Роль: Директор  
📅 Зарегистрирован: 2024-05-10
```

---
