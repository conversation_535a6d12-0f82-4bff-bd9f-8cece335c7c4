"""
Обработчики экспорта данных (только Reply-клавиатуры)

Экспорт в Excel и PDF форматы.
"""
from aiogram import Router, types, F
from aiogram.fsm.context import FSMContext
from states import ExportStates
from services.company_service import CompanyService
from services.project_service import ProjectService
from services.auth_service import AuthService
from services.work_entry_service import WorkEntryService
from services.worker_report_service import WorkerReportService
from middleware.rbac_middleware import require_permission
from keyboards.director import create_export_format_keyboard, create_export_import_keyboard
from keyboards.worker import create_export_format_keyboard as create_worker_export_format_keyboard, create_export_period_keyboard, create_worker_menu
from services.calculator import format_currency
from keyboards.common import create_back_keyboard
from db.session import get_session
from db.models import User
from utils.date_helpers import get_last_week_range, get_last_month_range, validate_date_range

router = Router()


# ===== ФУНКЦИИ ДЛЯ REPLY-КЛАВИАТУР =====

async def show_export_menu(message: types.Message):
    """Показать меню экспорта"""
    keyboard = create_export_format_keyboard()

    await message.answer(
        "📤 <b>Экспорт данных</b>\n\n"
        "Выберите формат экспорта:",
        reply_markup=keyboard,
        parse_mode="HTML"
    )


# ===== ОБРАБОТЧИКИ REPLY-КНОПОК =====

@router.message(F.text == "📊 Excel")
async def handle_excel_export_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Excel'"""
    await message.answer(
        "📊 <b>Экспорт в Excel</b>\n\n"
        "🚧 Функция экспорта в Excel в разработке\n\n"
        "Будет реализована возможность экспорта:\n"
        "• Отчётов по проектам\n"
        "• Списков рабочих\n"
        "• Статистики работ\n"
        "• Временных отчётов"
    )


@router.message(F.text == "📄 PDF")
async def handle_pdf_export_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'PDF'"""
    await message.answer(
        "📄 <b>Экспорт в PDF</b>\n\n"
        "🚧 Функция экспорта в PDF в разработке\n\n"
        "Будет реализована возможность экспорта:\n"
        "• Часовых листов\n"
        "• Отчётов по рабочим\n"
        "• Сводных отчётов\n"
        "• Детализированных отчётов"
    )


@router.message(F.text == "📋 CSV")
async def handle_csv_export_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'CSV'"""
    await message.answer(
        "📋 <b>Экспорт в CSV</b>\n\n"
        "🚧 Функция экспорта в CSV в разработке\n\n"
        "Будет реализована возможность экспорта:\n"
        "• Данных для анализа\n"
        "• Импорта в другие системы\n"
        "• Резервного копирования\n"
        "• Интеграции с внешними сервисами"
    )


@router.message(F.text == "📤 Экспорт отчётов")
async def handle_export_reports_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Экспорт отчётов'"""
    keyboard = create_export_import_keyboard()
    await message.answer(
        "📤 <b>Экспорт отчётов</b>\n\n"
        "Выберите тип отчёта для экспорта:\n\n"
        "📊 <b>Доступные отчёты:</b>\n"
        "• Отчёт по времени работы\n"
        "• Отчёт по проектам\n"
        "• Отчёт по рабочим\n"
        "• Сводный отчёт\n\n"
        "🚧 Функция в разработке",
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.message(F.text == "📥 Импорт данных")
async def handle_import_data_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Импорт данных'"""
    keyboard = create_export_import_keyboard()
    await message.answer(
        "📥 <b>Импорт данных</b>\n\n"
        "Возможности импорта:\n\n"
        "📋 <b>Поддерживаемые форматы:</b>\n"
        "• Excel (.xlsx)\n"
        "• CSV (.csv)\n"
        "• JSON (.json)\n\n"
        "📊 <b>Типы данных:</b>\n"
        "• Типы работ\n"
        "• Проекты\n"
        "• Рабочие\n"
        "• Временные записи\n\n"
        "🚧 Функция в разработке",
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.message(F.text == "📊 Статистика экспорта")
async def handle_export_statistics_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Статистика экспорта'"""
    keyboard = create_export_import_keyboard()
    await message.answer(
        "📊 <b>Статистика экспорта</b>\n\n"
        "📈 <b>Статистика использования:</b>\n"
        "• Всего экспортов: 0\n"
        "• Excel файлов: 0\n"
        "• PDF файлов: 0\n"
        "• CSV файлов: 0\n\n"
        "📅 <b>За последний месяц:</b>\n"
        "• Экспортов: 0\n"
        "• Размер данных: 0 MB\n\n"
        "🚧 Функция в разработке",
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.message(F.text == "⚙️ Настройки экспорта")
async def handle_export_settings_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Настройки экспорта'"""
    keyboard = create_export_import_keyboard()
    await message.answer(
        "⚙️ <b>Настройки экспорта</b>\n\n"
        "🔧 <b>Доступные настройки:</b>\n\n"
        "📄 <b>Формат по умолчанию:</b> Excel\n"
        "📅 <b>Период по умолчанию:</b> Последний месяц\n"
        "🗂️ <b>Включать архивные данные:</b> Нет\n"
        "📊 <b>Детализация:</b> Полная\n"
        "🔐 <b>Защита паролем:</b> Отключена\n\n"
        "🚧 Функция в разработке",
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.message(F.text == "📋 История экспорта")
async def handle_export_history_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'История экспорта'"""
    keyboard = create_export_import_keyboard()
    await message.answer(
        "📋 <b>История экспорта</b>\n\n"
        "📚 <b>Последние экспорты:</b>\n\n"
        "Экспорты не найдены.\n\n"
        "💡 После первого экспорта здесь будет отображаться история:\n"
        "• Дата и время экспорта\n"
        "• Тип файла\n"
        "• Размер данных\n"
        "• Статус выполнения\n\n"
        "🚧 Функция в разработке",
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.message(F.text == "🔄 Автоматический экспорт")
async def handle_auto_export_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Автоматический экспорт'"""
    keyboard = create_export_import_keyboard()
    await message.answer(
        "🔄 <b>Автоматический экспорт</b>\n\n"
        "⏰ <b>Настройки автоэкспорта:</b>\n\n"
        "📅 <b>Расписание:</b> Отключено\n"
        "📊 <b>Формат:</b> Excel\n"
        "📧 <b>Email уведомления:</b> Отключены\n"
        "☁️ <b>Облачное хранение:</b> Не настроено\n\n"
        "🔧 <b>Доступные опции:</b>\n"
        "• Ежедневный экспорт\n"
        "• Еженедельный экспорт\n"
        "• Ежемесячный экспорт\n"
        "• Экспорт по событиям\n\n"
        "🚧 Функция в разработке",
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.message(F.text == "📁 Шаблоны экспорта")
async def handle_export_templates_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Шаблоны экспорта'"""
    keyboard = create_export_import_keyboard()
    await message.answer(
        "📁 <b>Шаблоны экспорта</b>\n\n"
        "📋 <b>Доступные шаблоны:</b>\n\n"
        "Шаблоны не созданы.\n\n"
        "💡 Здесь можно будет создать и сохранить шаблоны для:\n"
        "• Стандартных отчётов\n"
        "• Пользовательских форматов\n"
        "• Специальных выборок\n"
        "• Регулярных экспортов\n\n"
        "🔧 <b>Возможности:</b>\n"
        "• Создание шаблонов\n"
        "• Редактирование шаблонов\n"
        "• Копирование шаблонов\n"
        "• Удаление шаблонов\n\n"
        "🚧 Функция в разработке",
        reply_markup=keyboard,
        parse_mode="HTML"
    )


# ===== ФУНКЦИИ ЭКСПОРТА ДЛЯ РАБОЧЕГО (CHECKPOINT 7) =====

async def start_worker_export_flow(message: types.Message, state: FSMContext):
    """
    Начинает процесс экспорта данных рабочего.
    Вызывается из handlers/worker.py
    """
    await state.clear()

    keyboard = create_export_period_keyboard()

    await message.answer(
        "📤 **Экспорт ваших данных**\n\n"
        "Выберите период для экспорта:",
        reply_markup=keyboard,
        parse_mode="Markdown"
    )
    await state.set_state(ExportStates.choosing_period)


@router.message(ExportStates.selecting_period)
async def process_worker_export_period(message: types.Message, state: FSMContext, **kwargs):
    """Обработка выбора периода экспорта для рабочего."""
    choice = message.text
    user_id = message.from_user.id

    if choice == "🔙 Отмена":
        await message.answer(
            "❌ Экспорт отменен.",
            reply_markup=create_worker_menu()
        )
        await state.clear()
        return

    try:
        async with get_session() as session:
            user = await session.get(User, user_id)
            if not user:
                await message.answer("❌ Пользователь не найден.")
                await state.clear()
                return

            # Определяем период
            start_date = None
            end_date = None
            period_text = ""

            if choice == "За всё время":
                period_text = "за всё время"
            elif choice == "За последнюю неделю":
                start_date, end_date = get_last_week_range()
                period_text = "за последнюю неделю"
            elif choice == "За последний месяц":
                start_date, end_date = get_last_month_range()
                period_text = "за последний месяц"
            elif choice == "За период":
                keyboard = create_back_keyboard()
                await message.answer(
                    "📅 Введите период в формате ДД.ММ.ГГГГ-ДД.ММ.ГГГГ\n"
                    "Например: 01.01.2024-31.01.2024",
                    reply_markup=keyboard
                )
                await state.set_state(ExportStates.entering_custom_period)
                return
            else:
                await message.answer("❌ Неверный выбор. Попробуйте еще раз.")
                return

            # Получаем данные для экспорта
            entries = await WorkEntryService.get_entries_for_export(
                session=session,
                user_id=user_id,
                start_date=start_date,
                end_date=end_date
            )

            if not entries:
                await message.answer(
                    f"📋 Записи {period_text} не найдены.",
                    reply_markup=create_worker_menu()
                )
                await state.clear()
                return

            # Сохраняем данные для экспорта
            await state.update_data(
                entries=entries,
                period_text=period_text,
                start_date=start_date.isoformat() if start_date else None,
                end_date=end_date.isoformat() if end_date else None
            )

            # Предлагаем выбрать формат
            keyboard = create_worker_export_format_keyboard()
            await message.answer(
                f"📊 Найдено записей: {len(entries)} {period_text}\n\n"
                "Выберите формат экспорта:",
                reply_markup=keyboard
            )
            await state.set_state(ExportStates.selecting_format)

    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Ошибка при выборе периода экспорта для user {user_id}: {e}")
        await message.answer("❌ Произошла ошибка. Попробуйте позже.")
        await state.clear()


@router.message(ExportStates.entering_custom_period)
async def process_custom_period_input(message: types.Message, state: FSMContext, **kwargs):
    """Обработка ввода пользовательского периода."""
    if message.text == "🔙 Назад":
        keyboard = create_export_period_keyboard()
        await message.answer(
            "📤 Выберите период для экспорта:",
            reply_markup=keyboard
        )
        await state.set_state(ExportStates.selecting_period)
        return

    # Валидируем период
    is_valid, error_msg, date_range = validate_date_range(message.text)

    if not is_valid:
        await message.answer(f"❌ {error_msg}")
        return

    start_date, end_date = date_range
    user_id = message.from_user.id

    try:
        async with get_session() as session:
            # Получаем данные для экспорта
            entries = await WorkEntryService.get_entries_for_export(
                session=session,
                user_id=user_id,
                start_date=start_date,
                end_date=end_date
            )

            if not entries:
                await message.answer(
                    f"📋 Записи за указанный период не найдены.",
                    reply_markup=create_worker_menu()
                )
                await state.clear()
                return

            # Сохраняем данные для экспорта
            await state.update_data(
                entries=entries,
                period_text=f"с {start_date.strftime('%d.%m.%Y')} по {end_date.strftime('%d.%m.%Y')}",
                start_date=start_date.isoformat(),
                end_date=end_date.isoformat()
            )

            # Предлагаем выбрать формат
            keyboard = create_worker_export_format_keyboard()
            await message.answer(
                f"📊 Найдено записей: {len(entries)} за указанный период\n\n"
                "Выберите формат экспорта:",
                reply_markup=keyboard
            )
            await state.set_state(ExportStates.selecting_format)

    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Ошибка при обработке пользовательского периода для user {user_id}: {e}")
        await message.answer("❌ Произошла ошибка. Попробуйте позже.")
        await state.clear()


@router.message(ExportStates.selecting_format)
async def process_worker_export_format(message: types.Message, state: FSMContext, **kwargs):
    """Обработка выбора формата экспорта для рабочего."""
    choice = message.text

    if choice == "🔙 Отмена":
        await message.answer(
            "❌ Экспорт отменен.",
            reply_markup=create_worker_menu()
        )
        await state.clear()
        return

    data = await state.get_data()
    entries = data.get('entries', [])
    period_text = data.get('period_text', '')

    if choice == "Excel":
        await export_worker_data_excel(message, entries, period_text)
    elif choice == "PDF":
        await export_worker_data_pdf(message, entries, period_text)
    else:
        await message.answer("❌ Неверный формат. Попробуйте еще раз.")
        return

    await state.clear()


async def export_worker_data_excel(message: types.Message, entries: list, period_text: str):
    """Экспорт данных рабочего в Excel."""
    try:
        # Подсчитываем статистику
        total_sum = sum(entry['sum'] for entry in entries)
        total_quantity = sum(entry['quantity'] for entry in entries)

        # Группируем по типам работ
        work_types = {}
        for entry in entries:
            work_type = entry['work_type']
            if work_type not in work_types:
                work_types[work_type] = {'count': 0, 'quantity': 0, 'sum': 0}
            work_types[work_type]['count'] += 1
            work_types[work_type]['quantity'] += entry['quantity']
            work_types[work_type]['sum'] += entry['sum']

        # Формируем отчет (пока текстовый, позже можно добавить реальный Excel)
        report_lines = [
            f"📊 **ОТЧЕТ {period_text.upper()}**",
            "",
            f"📈 **Общая статистика:**",
            f"• Всего записей: {len(entries)}",
            f"• Общее количество: {total_quantity:.2f}",
            f"• Общая сумма: {format_currency(total_sum)}",
            "",
            f"🔧 **По типам работ:**"
        ]

        for work_type, stats in work_types.items():
            report_lines.append(
                f"• {work_type}: {stats['count']} записей, "
                f"{stats['quantity']:.2f} ед., {format_currency(stats['sum'])}"
            )

        report_lines.extend([
            "",
            f"📋 **Детализация:**"
        ])

        for i, entry in enumerate(entries[:10], 1):  # Показываем первые 10
            report_lines.append(
                f"{i}. {entry['date']} - {entry['work_type']}\n"
                f"   {entry['description'][:50]}{'...' if len(entry['description']) > 50 else ''}\n"
                f"   {entry['quantity']} {entry['unit']} × {format_currency(entry['rate'])} = {format_currency(entry['sum'])}"
            )

        if len(entries) > 10:
            report_lines.append(f"\n... и еще {len(entries) - 10} записей")

        await message.answer(
            "\n".join(report_lines),
            reply_markup=create_worker_menu(),
            parse_mode="Markdown"
        )

        await message.answer(
            "✅ **Экспорт завершен!**\n\n"
            "📄 Данные экспортированы в текстовом формате.\n"
            "🚧 Экспорт в реальный Excel файл будет добавлен в следующем обновлении.",
            reply_markup=create_worker_menu(),
            parse_mode="Markdown"
        )

    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Ошибка экспорта в Excel: {e}")
        await message.answer(
            "❌ Ошибка при экспорте данных.",
            reply_markup=create_worker_menu()
        )


async def export_worker_data_pdf(message: types.Message, entries: list, period_text: str):
    """Экспорт данных рабочего в PDF."""
    try:
        await message.answer(
            f"📄 **Экспорт в PDF**\n\n"
            f"📊 Записей для экспорта: {len(entries)} {period_text}\n\n"
            f"🚧 Экспорт в PDF формат будет реализован в следующем обновлении.\n\n"
            f"💡 Пока доступен экспорт в Excel формат.",
            reply_markup=create_worker_menu(),
            parse_mode="Markdown"
        )

    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Ошибка экспорта в PDF: {e}")
        await message.answer(
            "❌ Ошибка при экспорте данных.",
            reply_markup=create_worker_menu()
        )
