# 🚀 РУКОВОДСТВО ПО РАЗВЁРТЫВАНИЮ WORKLOG MVP v2.0

## 🎯 Обзор развёртывания

### Поддерживаемые среды
- **Development** - локальная разработка
- **Testing** - тестовая среда
- **Staging** - предпродакшн
- **Production** - продакшн

### Архитектура развёртывания
```mermaid
graph TB
    subgraph "Production Environment"
        LB[Load Balancer]
        Bot1[Bot Instance 1]
        Bot2[Bot Instance 2]
        DB[(PostgreSQL)]
        Redis[(Redis)]
        Monitor[Monitoring]
    end
    
    LB --> Bot1
    LB --> Bot2
    Bot1 --> DB
    Bot2 --> DB
    Bot1 --> Redis
    Bot2 --> Redis
    Monitor --> Bot1
    Monitor --> Bot2
    Monitor --> DB
    Monitor --> Redis
```

## 🛠️ Требования к системе

### Минимальные требования
- **CPU**: 2 ядра
- **RAM**: 4 GB
- **Диск**: 20 GB SSD
- **ОС**: Ubuntu 20.04+ / CentOS 8+ / Debian 11+
- **Python**: 3.11+
- **PostgreSQL**: 15+
- **Redis**: 7+

### Рекомендуемые требования (Production)
- **CPU**: 4 ядра
- **RAM**: 8 GB
- **Диск**: 50 GB SSD
- **Сеть**: 100 Mbps
- **Backup**: автоматическое резервное копирование

## 📦 Установка зависимостей

### Системные пакеты
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install -y python3.11 python3.11-venv python3.11-dev
sudo apt install -y postgresql-15 postgresql-contrib
sudo apt install -y redis-server
sudo apt install -y nginx
sudo apt install -y git curl wget

# CentOS/RHEL
sudo dnf install -y python3.11 python3.11-devel
sudo dnf install -y postgresql15-server postgresql15-contrib
sudo dnf install -y redis
sudo dnf install -y nginx
sudo dnf install -y git curl wget
```

### Python зависимости
```bash
# Создание виртуального окружения
python3.11 -m venv /opt/worklog/venv
source /opt/worklog/venv/bin/activate

# Установка зависимостей
pip install --upgrade pip
pip install -r requirements.txt
```

## 🗄️ Настройка базы данных

### PostgreSQL
```bash
# Создание пользователя и базы данных
sudo -u postgres psql << EOF
CREATE USER worklog_user WITH PASSWORD 'secure_password_here';
CREATE DATABASE worklog_db OWNER worklog_user;
GRANT ALL PRIVILEGES ON DATABASE worklog_db TO worklog_user;
ALTER USER worklog_user CREATEDB;
EOF

# Настройка PostgreSQL
sudo nano /etc/postgresql/15/main/postgresql.conf
```

#### Конфигурация PostgreSQL
```ini
# postgresql.conf
listen_addresses = 'localhost'
port = 5432
max_connections = 100
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100
```

### Redis
```bash
# Настройка Redis
sudo nano /etc/redis/redis.conf
```

#### Конфигурация Redis
```ini
# redis.conf
bind 127.0.0.1
port 6379
timeout 300
tcp-keepalive 60
maxmemory 512mb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

## 🔧 Конфигурация приложения

### Переменные окружения (.env)
```env
# Production Configuration
BOT_TOKEN=your_production_bot_token
DEBUG=False
LOG_LEVEL=INFO

# Database
DATABASE_URL=postgresql+asyncpg://worklog_user:secure_password@localhost:5432/worklog_db
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30
DATABASE_POOL_TIMEOUT=30

# Redis
REDIS_URL=redis://localhost:6379/0
REDIS_POOL_SIZE=10

# Security
SECRET_KEY=your_very_secure_secret_key_here
TOKEN_TTL_HOURS=24
BCRYPT_ROUNDS=12

# Rate Limiting
RATE_LIMIT_COMMANDS_PER_MINUTE=20
RATE_LIMIT_REPORTS_PER_HOUR=10

# Monitoring
SENTRY_DSN=your_sentry_dsn_here
PROMETHEUS_PORT=8000

# File Storage
UPLOAD_PATH=/opt/worklog/uploads
MAX_FILE_SIZE=10485760  # 10MB

# Email (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password
```

### Миграции базы данных
```bash
# Применение миграций
cd /opt/worklog
source venv/bin/activate
alembic upgrade head

# Создание начальных данных
python scripts/create_initial_data.py
```

## 🐳 Docker развёртывание

### Dockerfile
```dockerfile
FROM python:3.11-slim

# Установка системных зависимостей
RUN apt-get update && apt-get install -y \
    gcc \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Создание пользователя
RUN useradd --create-home --shell /bin/bash worklog

# Установка приложения
WORKDIR /app
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .
RUN chown -R worklog:worklog /app

USER worklog

# Запуск приложения
CMD ["python", "-m", "src.bot"]
```

### docker-compose.yml (Production)
```yaml
version: '3.8'

services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: worklog_db
      POSTGRES_USER: worklog_user
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backups:/backups
    ports:
      - "127.0.0.1:5432:5432"
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U worklog_user -d worklog_db"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "127.0.0.1:6379:6379"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  bot:
    build: .
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      - DATABASE_URL=postgresql+asyncpg://worklog_user:${DB_PASSWORD}@postgres:5432/worklog_db
      - REDIS_URL=redis://redis:6379/0
    env_file:
      - .env
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:8000/health')"]
      interval: 60s
      timeout: 10s
      retries: 3

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - bot
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
```

## 🔒 Безопасность

### SSL/TLS сертификаты
```bash
# Получение Let's Encrypt сертификата
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com

# Автоматическое обновление
sudo crontab -e
# Добавить: 0 12 * * * /usr/bin/certbot renew --quiet
```

### Firewall настройки
```bash
# UFW (Ubuntu)
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw deny 5432/tcp  # PostgreSQL только локально
sudo ufw deny 6379/tcp  # Redis только локально

# Проверка статуса
sudo ufw status verbose
```

### Nginx конфигурация
```nginx
# nginx.conf
events {
    worker_connections 1024;
}

http {
    upstream bot_backend {
        server bot:8000;
    }

    server {
        listen 80;
        server_name your-domain.com;
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name your-domain.com;

        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

        location /health {
            proxy_pass http://bot_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }

        location /metrics {
            proxy_pass http://bot_backend;
            allow 127.0.0.1;
            deny all;
        }
    }
}
```

## 📊 Мониторинг и логирование

### Systemd сервис
```ini
# /etc/systemd/system/worklog-bot.service
[Unit]
Description=WorkLog MVP Bot
After=network.target postgresql.service redis.service
Requires=postgresql.service redis.service

[Service]
Type=simple
User=worklog
Group=worklog
WorkingDirectory=/opt/worklog
Environment=PATH=/opt/worklog/venv/bin
ExecStart=/opt/worklog/venv/bin/python -m src.bot
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=worklog-bot

# Безопасность
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/worklog/uploads /opt/worklog/logs

[Install]
WantedBy=multi-user.target
```

### Логирование
```python
# logging_config.py
import logging.config

LOGGING_CONFIG = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'detailed': {
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        },
        'json': {
            'format': '{"timestamp": "%(asctime)s", "logger": "%(name)s", "level": "%(levelname)s", "message": "%(message)s"}'
        }
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'level': 'INFO',
            'formatter': 'detailed',
            'stream': 'ext://sys.stdout'
        },
        'file': {
            'class': 'logging.handlers.RotatingFileHandler',
            'level': 'INFO',
            'formatter': 'json',
            'filename': '/opt/worklog/logs/app.log',
            'maxBytes': 10485760,  # 10MB
            'backupCount': 5
        },
        'error_file': {
            'class': 'logging.handlers.RotatingFileHandler',
            'level': 'ERROR',
            'formatter': 'json',
            'filename': '/opt/worklog/logs/error.log',
            'maxBytes': 10485760,
            'backupCount': 5
        }
    },
    'loggers': {
        'src': {
            'level': 'INFO',
            'handlers': ['console', 'file', 'error_file'],
            'propagate': False
        }
    },
    'root': {
        'level': 'WARNING',
        'handlers': ['console']
    }
}
```

### Health Check endpoint
```python
# health.py
from fastapi import FastAPI
from sqlalchemy import text
import redis
import asyncio

app = FastAPI()

@app.get("/health")
async def health_check():
    """Проверка состояния сервисов."""
    checks = {
        "status": "healthy",
        "database": False,
        "redis": False,
        "bot": False
    }
    
    try:
        # Проверка БД
        async with get_session() as session:
            await session.execute(text("SELECT 1"))
            checks["database"] = True
    except Exception:
        checks["status"] = "unhealthy"
    
    try:
        # Проверка Redis
        r = redis.Redis.from_url(config.redis_url)
        r.ping()
        checks["redis"] = True
    except Exception:
        checks["status"] = "unhealthy"
    
    try:
        # Проверка бота
        bot_info = await bot.get_me()
        checks["bot"] = bool(bot_info)
    except Exception:
        checks["status"] = "unhealthy"
    
    return checks

@app.get("/metrics")
async def metrics():
    """Метрики для Prometheus."""
    # Реализация метрик
    pass
```

## 💾 Резервное копирование

### Скрипт бэкапа
```bash
#!/bin/bash
# backup.sh

BACKUP_DIR="/opt/worklog/backups"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="worklog_db"
DB_USER="worklog_user"

# Создание директории бэкапов
mkdir -p $BACKUP_DIR

# Бэкап базы данных
pg_dump -U $DB_USER -h localhost $DB_NAME | gzip > $BACKUP_DIR/db_backup_$DATE.sql.gz

# Бэкап файлов
tar -czf $BACKUP_DIR/files_backup_$DATE.tar.gz /opt/worklog/uploads

# Удаление старых бэкапов (старше 30 дней)
find $BACKUP_DIR -name "*.gz" -mtime +30 -delete

# Отправка в облако (опционально)
# aws s3 cp $BACKUP_DIR/db_backup_$DATE.sql.gz s3://your-bucket/backups/

echo "Backup completed: $DATE"
```

### Cron задача для автоматического бэкапа
```bash
# Добавить в crontab
sudo crontab -e

# Ежедневный бэкап в 2:00
0 2 * * * /opt/worklog/scripts/backup.sh >> /var/log/worklog-backup.log 2>&1
```

## 🚀 Процедура развёртывания

### Первоначальное развёртывание
```bash
#!/bin/bash
# deploy.sh

set -e

echo "🚀 Начало развёртывания WorkLog MVP v2.0"

# 1. Обновление кода
cd /opt/worklog
git pull origin main

# 2. Активация виртуального окружения
source venv/bin/activate

# 3. Обновление зависимостей
pip install -r requirements.txt

# 4. Применение миграций
alembic upgrade head

# 5. Сборка статических файлов (если есть)
# python manage.py collectstatic --noinput

# 6. Перезапуск сервисов
sudo systemctl restart worklog-bot
sudo systemctl restart nginx

# 7. Проверка состояния
sleep 10
curl -f http://localhost:8000/health || exit 1

echo "✅ Развёртывание завершено успешно"
```

### Blue-Green развёртывание
```bash
#!/bin/bash
# blue-green-deploy.sh

CURRENT_ENV=$(docker-compose ps --services --filter "status=running" | grep bot | head -1)
NEW_ENV="blue"

if [ "$CURRENT_ENV" = "blue" ]; then
    NEW_ENV="green"
fi

echo "Развёртывание в среду: $NEW_ENV"

# Запуск новой версии
docker-compose -f docker-compose.$NEW_ENV.yml up -d

# Проверка здоровья
sleep 30
curl -f http://localhost:8001/health || exit 1

# Переключение трафика
# Обновление nginx конфигурации для переключения на новую версию

# Остановка старой версии
docker-compose -f docker-compose.$CURRENT_ENV.yml down

echo "Переключение на $NEW_ENV завершено"
```

## 📋 Чек-лист развёртывания

### Перед развёртыванием
- [ ] Код протестирован и готов к релизу
- [ ] Миграции БД проверены
- [ ] Конфигурация обновлена
- [ ] SSL сертификаты действительны
- [ ] Бэкапы созданы
- [ ] Мониторинг настроен

### После развёртывания
- [ ] Сервисы запущены и работают
- [ ] Health check проходит
- [ ] Логи не содержат ошибок
- [ ] Метрики собираются
- [ ] Бот отвечает на команды
- [ ] База данных доступна
- [ ] Файлы загружаются корректно

### Откат (если что-то пошло не так)
- [ ] Остановить новую версию
- [ ] Восстановить предыдущую версию
- [ ] Откатить миграции БД (если нужно)
- [ ] Восстановить из бэкапа (если нужно)
- [ ] Проверить работоспособность
- [ ] Уведомить команду о проблеме
