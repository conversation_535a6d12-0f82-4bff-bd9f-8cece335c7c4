# 🤖 ПРОМТ ДЛЯ ИИ-АГЕНТОВ: WORKLOG BOT v2.0

## 🎯 РОЛЬ И КОНТЕКСТ

Вы — ИИ-агент разработчик, работающий над проектом **Worklog Bot v2.0** — Telegram-бот для учета рабочего времени строительных компаний в Финляндии. Проект имеет русскоязычный интерфейс с финляндской локализацией (евро, финские адреса, местные стандарты).

## 📚 ОБЯЗАТЕЛЬНЫЕ ДОКУМЕНТЫ ДЛЯ ИЗУЧЕНИЯ

### 🔥 ПРИОРИТЕТ 1 (ЧИТАТЬ ПЕРВЫМИ)
1. **`COMPREHENSIVE_IMPLEMENTATION_GUIDE.md`** — главное руководство по реализации
2. **`role_admin.md`** — финальная спецификация роли администратора (НЕ РЕДАКТИРОВАТЬ)
3. **`role_director.md`** — финальная спецификация роли директора (НЕ РЕДАКТИРОВАТЬ)
4. **`role_worker.md`** — финальная спецификация роли рабочего (НЕ РЕДАКТИРОВАТЬ)

### 🔥 ПРИОРИТЕТ 2 (ТЕХНИЧЕСКИЕ ДЕТАЛИ)
5. **`TECHNICAL_SPECIFICATION.md`** — техническая спецификация системы
6. **`role_commands.md`** — команды по ролям (обновлен под финальные спецификации)
7. **`AIOGRAM_V3_MIGRATION_GUIDE.md`** — руководство по миграции с aiogram v2.x на v3.x

### 🔥 ПРИОРИТЕТ 3 (ЛОКАЛИЗАЦИЯ И ПРИМЕРЫ)
8. **`FINLAND_LOCALIZATION_EXAMPLES.md`** — примеры финляндской локализации
9. **`localization_guide.md`** — руководство по локализации
10. **`examples/`** — примеры кода (адаптированы под aiogram v3.x)

## ⚠️ КРИТИЧЕСКИ ВАЖНЫЕ ПРАВИЛА

### 🚫 ЗАПРЕЩЕНО РЕДАКТИРОВАТЬ
- **`role_admin.md`** — финальная спецификация
- **`role_director.md`** — финальная спецификация  
- **`role_worker.md`** — финальная спецификация

Эти документы являются **источником истины** для реализации меню, команд и сценариев работы каждой роли.

### ✅ ОБЯЗАТЕЛЬНЫЕ ПРИНЦИПЫ

1. **Только PostgreSQL** — SQLite исключен из-за проблем масштабирования
2. **aiogram v3.x** — все примеры и код должны использовать v3.x синтаксис
3. **Токен-система регистрации** — основной способ регистрации пользователей
4. **Гибридная система для админа** — выбор роли через /start в режиме разработки
5. **Финляндская локализация** — евро, финские адреса, европейские стандарты
6. **Только inline-клавиатуры** — никаких текстовых команд в меню

## 🛠️ СТАНДАРТЫ РАЗРАБОТКИ

### Архитектура кода
```python
# Структура обработчиков (aiogram v3.x)
from aiogram import Router, types, F
from aiogram.fsm.context import FSMContext
from aiogram.filters import Command

router = Router()

@router.message(Command("command_name"))
@require_permission("permission_name")
async def handler(message: types.Message, state: FSMContext):
    # Логика обработчика
    pass

# Регистрация
def register_handlers(dp):
    dp.include_router(router)
```

### Стандарты именования
- **Файлы**: `snake_case.py`
- **Классы**: `PascalCase`
- **Функции**: `snake_case`
- **Константы**: `UPPER_CASE`
- **FSM состояния**: `ClassName.state_name`

### Обработка ошибок
```python
try:
    # Основная логика
    result = await service.execute()
except ValidationError as e:
    await message.answer(f"❌ {e.message}")
except InsufficientPermissionsError:
    await message.answer("❌ Недостаточно прав")
except Exception as e:
    logger.error(f"Ошибка: {e}")
    await message.answer("❌ Произошла ошибка")
```

## 📝 ДОКУМЕНТИРОВАНИЕ

### При создании новых функций
1. **Обновить `CHANGELOG.md`** — записать изменения
2. **Обновить `context_memory.md`** — сохранить контекст работы
3. **Использовать `agent_checklist.md`** — проверить все пункты
4. **Создать checkpoint** — зафиксировать прогресс

### Комментирование кода
```python
async def cmd_add_work(message: types.Message, state: FSMContext):
    """
    Начало сценария добавления работы.
    
    FSM: AddWorkStates (7 шагов)
    Права: can_add_work = True
    Зависимости: активный проект
    
    Логика:
    1. Проверка активного проекта
    2. Показ клавиатуры выбора даты
    3. Переход в FSM состояние
    """
```

## 🔄 WORKFLOW РАЗРАБОТКИ

### Перед началом работы
1. Прочитать `context_memory.md` — понять текущее состояние
2. Изучить связанные документы из приоритетного списка
3. Проверить `CHANGELOG.md` — понять последние изменения

### Во время работы
1. Следовать `agent_checklist.md` при добавлении функций
2. Тестировать с гибридной системой ролей
3. Соблюдать финляндскую локализацию

### После завершения работы
1. Обновить `CHANGELOG.md`
2. Обновить `context_memory.md`
3. Создать checkpoint в системе
4. Документировать изменения

## 🎭 ОСОБЕННОСТИ РОЛЕЙ

### 👑 Администратор
- Управление всей системой через токен-ссылки
- В режиме разработки: выбор любой роли через /start
- Глобальные права доступа

### 👨‍💼 Директор  
- Управление компанией и рабочими
- Генерация токенов для рабочих
- Просмотр отчетов компании

### 👷 Рабочий
- Ведение учета рабочего времени
- Управление собственными проектами
- Экспорт собственных данных

## 🇫🇮 ФИНЛЯНДСКАЯ СПЕЦИФИКА

### Обязательные элементы
- **Валюта**: € (евро) — "25,50 €"
- **Компании**: "Rakennus Virtanen Oy"
- **Адреса**: "Mannerheimintie 15, 00100 Helsinki"
- **Проекты**: "Asuntokohde Kallio"
- **Ставки**: 25-45 €/час (типичные для Финляндии)

### Форматы
```python
# Деньги
"format": "{amount:,.2f} €"

# Даты  
"format": "%d.%m.%Y"  # 28.06.2024

# Адреса
"format": "{street}, {postal_code} {city}"
```

## 🚀 БЫСТРЫЙ СТАРТ

1. **Изучить** `COMPREHENSIVE_IMPLEMENTATION_GUIDE.md`
2. **Прочитать** финальные role_*.md спецификации
3. **Настроить** PostgreSQL окружение
4. **Использовать** aiogram v3.x синтаксис
5. **Тестировать** с гибридной системой ролей
6. **Документировать** все изменения

---

**Помните**: Документы role_admin.md, role_director.md, role_worker.md — это источник истины. Все остальное должно соответствовать им.
