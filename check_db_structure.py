"""
Скрипт для проверки структуры базы данных
"""
import asyncio
import asyncpg
import os
from dotenv import load_dotenv

async def check_db_structure():
    load_dotenv()
    database_url = os.getenv('DATABASE_URL').replace('postgresql+asyncpg://', 'postgresql://')
    
    conn = await asyncpg.connect(database_url)
    
    # Получаем список всех таблиц
    tables = await conn.fetch('''
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        ORDER BY table_name
    ''')
    
    print('📊 ТАБЛИЦЫ В БД:')
    for table in tables:
        table_name = table['table_name']
        print(f'  - {table_name}')
    
    print('\n' + '='*60)
    
    # Проверяем структуру каждой таблицы
    for table in tables:
        table_name = table['table_name']
        columns = await conn.fetch('''
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = $1
            ORDER BY ordinal_position
        ''', table_name)
        
        print(f'\n📋 Таблица: {table_name}')
        for col in columns:
            nullable = 'NULL' if col['is_nullable'] == 'YES' else 'NOT NULL'
            col_default = col['column_default']
            default = f' DEFAULT {col_default}' if col_default else ''
            col_name = col['column_name']
            data_type = col['data_type']
            print(f'  - {col_name}: {data_type} ({nullable}){default}')
    
    await conn.close()

if __name__ == "__main__":
    asyncio.run(check_db_structure())
