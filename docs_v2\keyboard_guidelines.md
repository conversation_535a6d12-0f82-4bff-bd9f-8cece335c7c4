# ⌨️ СТАНДАРТЫ REPLY-КЛАВИАТУР WORKLOG MVP v2.0

## 🎯 Общие принципы

### Философия дизайна
1. **Единообразие** - одинаковый стиль во всём приложении
2. **Интуитивность** - понятные иконки и текст
3. **Доступность** - удобство на мобильных устройствах
4. **Контекстность** - кнопки соответствуют текущему состоянию
5. **Навигация** - всегда есть способ вернуться назад

## 📱 Технические требования

### Ограничения Telegram
- **Максимум 100 кнопок** на клавиатуру
- **Максимум 4 кнопки в ряду** (рекомендуется не более 3)
- **Максимум 64 символа** для текста кнопки
- **Автоматическое изменение размера** кнопок

### Рекомендации по размещению
- **1-2 кнопки в ряду** - для основных действий
- **3 кнопки в ряду** - для навигации или выбора
- **Не более 8 рядов** - для удобства использования

## 🎨 Стандарты оформления

### Текст кнопок

#### Формат: `{эмодзи} {текст}`
```python
# ✅ Правильно - единый стиль
BUTTON_TEXTS = {
    # Основные действия
    "add_work": "➕ Добавить работу",
    "edit_work": "✏️ Редактировать",
    "delete_work": "🗑️ Удалить",
    "view_work": "👁️ Просмотреть",
    
    # Навигация
    "back": "🔙 Назад",
    "main_menu": "🏠 Главное меню",
    "cancel": "❌ Отменить",
    
    # Подтверждения
    "confirm": "✅ Подтвердить",
    "save": "💾 Сохранить",
    
    # Роли
    "role_admin": "👑 Администратор",
    "role_director": "👨‍💼 Директор",
    "role_worker": "👷 Рабочий"
}

# ❌ Неправильно - разный стиль
bad_buttons = {
    "add": "Добавить работу",           # Нет эмодзи
    "edit": "✏️ Edit",                  # Смешанные языки
    "delete": "🗑️🗑️ УДАЛИТЬ",         # Дублирование эмодзи, капс
    "view": "view work"                 # Нет эмодзи, английский
}
```

### Эмодзи стандарты

#### Основные действия
```python
EMOJI_ACTIONS = {
    "add": "➕",           # Добавить
    "edit": "✏️",          # Редактировать
    "delete": "🗑️",        # Удалить
    "view": "👁️",          # Просмотреть
    "save": "💾",          # Сохранить
    "confirm": "✅",       # Подтвердить
    "cancel": "❌",        # Отменить
    "search": "🔍",        # Поиск
    "filter": "🔽",        # Фильтр
    "export": "📤",        # Экспорт
    "import": "📥",        # Импорт
    "download": "⬇️",      # Скачать
    "upload": "⬆️",        # Загрузить
}
```

#### Навигация
```python
EMOJI_NAVIGATION = {
    "back": "🔙",          # Назад
    "forward": "▶️",       # Вперёд
    "home": "🏠",          # Главная
    "menu": "📋",          # Меню
    "settings": "⚙️",      # Настройки
    "profile": "👤",       # Профиль
    "help": "❓",          # Помощь
    "info": "ℹ️",          # Информация
}
```

#### Сущности
```python
EMOJI_ENTITIES = {
    "user": "👤",          # Пользователь
    "worker": "👷",        # Рабочий
    "director": "👨‍💼",     # Директор
    "admin": "👑",         # Администратор
    "company": "🏢",       # Компания
    "project": "🏗️",       # Проект
    "work": "⚙️",          # Работа
    "report": "📊",        # Отчёт
    "note": "📝",          # Заметка
    "request": "🛠️",       # Запрос
    "category": "📋",      # Категория
    "date": "📅",          # Дата
    "time": "⏰",          # Время
    "money": "💰",         # Деньги
}
```

## 📝 Обработка текстовых команд

### Структурированный формат команд

#### Формат: `{эмодзи} {текст команды}`
```python
def parse_text_command(text: str) -> Tuple[str, Optional[str]]:
    """
    Парсит текстовую команду с кнопки.

    Args:
        text: Текст с кнопки (например, "➕ Добавить работу")

    Returns:
        Tuple[action, entity]: Действие и сущность

    Examples:
        parse_text_command("➕ Добавить работу") -> ("add", "work")
        parse_text_command("📋 Мои работы") -> ("list", "work")
        parse_text_command("🔙 Назад") -> ("back", None)
    """
    # Убираем эмодзи и лишние пробелы
    clean_text = text.strip()
    if len(clean_text) > 0 and ord(clean_text[0]) > 127:
        clean_text = clean_text[1:].strip()

    # Маппинг команд
    command_mapping = {
        "Добавить работу": ("add", "work"),
        "Мои работы": ("list", "work"),
        "Назад": ("back", None),
        "Главное меню": ("menu", "main")
    }

    return command_mapping.get(clean_text, ("unknown", None))
```

#### Примеры использования
```python
# Основные действия
"add:work"                    # Добавить работу
"edit:work:123"              # Редактировать работу 123
"delete:work:123"            # Удалить работу 123
"view:work:123"              # Просмотреть работу 123

# Проекты
"select:project:456"         # Выбрать проект 456
"create:project"             # Создать проект
"assign:project:456:789"     # Назначить пользователя 789 на проект 456

# Отчёты
"generate:report:daily"      # Создать дневной отчёт
"export:report:123:pdf"      # Экспорт отчёта 123 в PDF
"export:report:123:excel"    # Экспорт отчёта 123 в Excel

# Роли и навигация
"select:role:worker"         # Выбрать роль рабочего
"menu:main"                  # Главное меню
"menu:worker"                # Меню рабочего
"back:work:list"             # Назад к списку работ
```

## 🏗️ Создание клавиатур

### Базовый класс для клавиатур

```python
from typing import List, Optional
from aiogram.types import KeyboardButton, ReplyKeyboardMarkup

class ReplyKeyboardBuilder:
    """Базовый класс для создания reply-клавиатур."""

    def __init__(self, resize_keyboard: bool = True, one_time_keyboard: bool = False):
        self.buttons: List[List[KeyboardButton]] = []
        self.resize_keyboard = resize_keyboard
        self.one_time_keyboard = one_time_keyboard

    def add_button(
        self,
        text: str,
        row: Optional[int] = None
    ) -> 'ReplyKeyboardBuilder':
        """Добавляет кнопку в клавиатуру."""
        button = KeyboardButton(text=text)

        if row is None:
            # Добавляем в новый ряд
            self.buttons.append([button])
        else:
            # Добавляем в существующий ряд
            while len(self.buttons) <= row:
                self.buttons.append([])
            self.buttons[row].append(button)

        return self

    def add_row(self, buttons: List[str]) -> 'ReplyKeyboardBuilder':
        """Добавляет ряд кнопок."""
        row = [KeyboardButton(text=text) for text in buttons]
        self.buttons.append(row)
        return self

    def add_back_button(self) -> 'ReplyKeyboardBuilder':
        """Добавляет кнопку 'Назад'."""
        return self.add_button("🔙 Назад")

    def add_cancel_button(self) -> 'ReplyKeyboardBuilder':
        """Добавляет кнопку 'Отменить'."""
        return self.add_button("❌ Отменить")

    def build(self) -> ReplyKeyboardMarkup:
        """Создаёт финальную клавиатуру."""
        return ReplyKeyboardMarkup(
            keyboard=self.buttons,
            resize_keyboard=self.resize_keyboard,
            one_time_keyboard=self.one_time_keyboard
        )
```

### Специализированные клавиатуры

#### Главное меню рабочего
```python
def create_worker_menu_keyboard() -> ReplyKeyboardMarkup:
    """Создаёт главное меню для рабочего."""
    return (ReplyKeyboardBuilder()
        .add_row([
            "➕ Добавить работу",
            "📋 Мои работы"
        ])
        .add_row([
            "🏗️ Мои проекты",
            "📝 Заметки"
        ])
        .add_row([
            "🛠️ Запросы",
            "👤 Профиль"
        ])
        .build())
```

#### Действия с записью о работе
```python
def create_work_actions_keyboard() -> ReplyKeyboardMarkup:
    """Создаёт клавиатуру действий для записи о работе."""
    return (ReplyKeyboardBuilder()
        .add_row([
            "✏️ Редактировать",
            "🗑️ Удалить"
        ])
        .add_row([
            "👁️ Подробнее"
        ])
        .add_back_button()
        .build())
```

#### Подтверждение действия
```python
def create_confirmation_keyboard() -> ReplyKeyboardMarkup:
    """Создаёт клавиатуру подтверждения."""
    return (ReplyKeyboardBuilder()
        .add_row([
            "✅ Подтвердить",
            "❌ Отменить"
        ])
        .build())
```

#### Выбор из списка
```python
def create_selection_keyboard(
    items: List[str]  # список названий для кнопок
) -> ReplyKeyboardMarkup:
    """Создаёт клавиатуру выбора из списка."""
    builder = ReplyKeyboardBuilder()

    for item in items:
        builder.add_button(text=item)

    builder.add_back_button()
    return builder.build()

# Пример использования
projects = [
    "🏗️ Дом на Ленина",
    "🏗️ Офис центр",
    "🏗️ Торговый комплекс"
]
keyboard = create_selection_keyboard(projects)
```

## 📋 Стандартные шаблоны

### Навигационные кнопки

#### Основная навигация
```python
NAVIGATION_BUTTONS = [
    "🔙 Назад",
    "🏠 Главное меню",
    "❌ Отменить",
    "💾 Сохранить",
    "✅ Подтвердить"
]
```

#### Навигация по спискам
```python
def create_navigation_keyboard(
    has_prev: bool = False,
    has_next: bool = False
) -> ReplyKeyboardMarkup:
    """Создаёт клавиатуру навигации по спискам."""
    builder = ReplyKeyboardBuilder()

    # Кнопки навигации
    nav_buttons = []

    if has_prev:
        nav_buttons.append("⬅️ Предыдущие")

    if has_next:
        nav_buttons.append("➡️ Следующие")

    if nav_buttons:
        builder.add_row(nav_buttons)

    # Дополнительные кнопки
    builder.add_row([
        "🔍 Поиск",
        "🔄 Обновить"
    ])

    builder.add_back_button()
    return builder.build()
```

### Фильтры и сортировка

#### Фильтры по дате
```python
def create_date_filter_keyboard() -> ReplyKeyboardMarkup:
    """Создаёт клавиатуру фильтров по дате."""
    return (ReplyKeyboardBuilder()
        .add_row([
            "📅 Сегодня",
            "📅 Вчера"
        ])
        .add_row([
            "📅 Эта неделя",
            "📅 Этот месяц"
        ])
        .add_button("📅 Выбрать период")
        .add_back_button()
        .build())
```

#### Сортировка
```python
def create_sort_keyboard() -> ReplyKeyboardMarkup:
    """Создаёт клавиатуру сортировки."""
    return (ReplyKeyboardBuilder()
        .add_row([
            "📅 По дате",
            "💰 По сумме"
        ])
        .add_row([
            "🏗️ По проекту",
            "⏰ По времени"
        ])
        .add_back_button()
        .build())
```

## 🎨 Адаптивные клавиатуры

### Клавиатуры в зависимости от роли

```python
def create_role_based_menu(user_role: str) -> ReplyKeyboardMarkup:
    """Создаёт меню в зависимости от роли пользователя."""
    builder = ReplyKeyboardBuilder()

    # Общие кнопки для всех ролей
    builder.add_row([
        "📋 Мои работы",
        "👤 Профиль"
    ])

    # Кнопки для рабочих и выше
    if user_role in ["ROLE_WORKER", "ROLE_DIRECTOR", "ROLE_ADMIN"]:
        builder.add_button("➕ Добавить работу")

    # Кнопки для директоров и выше
    if user_role in ["ROLE_DIRECTOR", "ROLE_ADMIN"]:
        builder.add_row([
            "🏗️ Проекты",
            "👷 Рабочие"
        ])
        builder.add_button("📊 Отчёты")

    # Кнопки только для админов
    if user_role == "ROLE_ADMIN":
        builder.add_row([
            "🏢 Компании",
            "⚙️ Система"
        ])

    return builder.build()
```

### Динамические клавиатуры

```python
def create_dynamic_project_keyboard(
    projects: List[Project],
    user_role: str,
    current_project_id: Optional[int] = None
) -> ReplyKeyboardMarkup:
    """Создаёт динамическую клавиатуру проектов."""
    builder = ReplyKeyboardBuilder()

    for project in projects:
        # Формируем текст кнопки
        text = f"🏗️ {project.name}"

        # Отмечаем активный проект
        if project.id == current_project_id:
            text = f"✅ {text}"

        # Добавляем информацию о статусе
        if not project.is_active:
            text = f"⏸️ {text}"

        builder.add_button(text=text)

    # Кнопка создания проекта для директоров
    if user_role in ["ROLE_DIRECTOR", "ROLE_ADMIN"]:
        builder.add_button("➕ Новый проект")

    builder.add_back_button()
    return builder.build()
```

## 🔍 Валидация и обработка команд

### Обработка текстовых команд

```python
@router.message(F.text)
async def handle_text_command(message: Message):
    """Обработчик текстовых команд с кнопок."""
    text = message.text.strip()

    # Маппинг команд на функции
    command_handlers = {
        "➕ Добавить работу": handle_add_work,
        "📋 Мои работы": handle_list_work,
        "🏗️ Мои проекты": handle_list_projects,
        "👤 Профиль": handle_profile,
        "🔙 Назад": handle_back,
        "🏠 Главное меню": handle_main_menu
    }

    # Поиск обработчика
    handler = command_handlers.get(text)
    if handler:
        await handler(message)
    else:
        await handle_unknown_command(message)
```

### Обработка неизвестных команд

```python
async def handle_unknown_command(message: Message):
    """Обработчик неизвестных команд."""
    logger.warning(f"Неизвестная команда: {message.text}")

    await message.answer(
        "❌ Неизвестная команда. Используйте кнопки меню.",
        reply_markup=create_role_based_menu(message.from_user.role)
    )
```

## 📊 Метрики и аналитика

### Отслеживание использования кнопок

```python
async def track_button_usage(
    user_id: int,
    button_text: str,
    session: AsyncSession
):
    """Отслеживает использование кнопок для аналитики."""
    # Парсим команду из текста кнопки
    action, entity = parse_text_command(button_text)

    # Логируем использование
    logger.info(
        "Button usage: user_id=%s, action=%s, entity=%s, text=%s",
        user_id, action, entity, button_text
    )

    # Можно сохранять в БД для аналитики
    # analytics_service.track_button_click(user_id, action, entity, button_text)
```
