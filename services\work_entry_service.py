"""
Сервис для работы с записями о работе (адаптировано из examples).

Основные функции:
- create_work_entry() - создание записи работы
- get_user_work_entries() - получение записей пользователя с пагинацией
- update_work_entry() - редактирование записи
- delete_work_entry() - мягкое удаление записи
- get_work_entry_by_id() - получение записи по ID
- validate_work_entry_data() - валидация данных
"""
import logging
from datetime import datetime, date
from typing import List, Optional, Dict, Any
from sqlalchemy import select, and_, or_, desc
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from db.models import WorkEntry, WorkType, Project, User, Company
from services.calculator import calculate_sum, validate_work_entry_data
from utils.exceptions import ValidationError, NotFoundError, PermissionError

logger = logging.getLogger(__name__)


class WorkEntryService:
    """Сервис для работы с записями о работе."""

    @staticmethod
    async def create_work_entry(
        session: AsyncSession,
        user_id: int,
        project_id: int,
        work_type_id: int,
        date: date,
        quantity: float,
        description: str,
        company_id: int
    ) -> WorkEntry:
        """
        Создает новую запись о работе.
        
        Args:
            session: Сессия БД
            user_id: ID пользователя
            project_id: ID проекта
            work_type_id: ID типа работы
            date: Дата выполнения работы
            quantity: Количество
            description: Описание работы
            company_id: ID компании
            
        Returns:
            Созданная запись
            
        Raises:
            ValidationError: При некорректных данных
            NotFoundError: Если тип работы не найден
        """
        try:
            # Получаем тип работы для расчета суммы
            work_type_result = await session.execute(
                select(WorkType).where(WorkType.work_type_id == work_type_id)
            )
            work_type = work_type_result.scalar_one_or_none()
            
            if not work_type:
                raise NotFoundError(f"Тип работы с ID {work_type_id} не найден")
            
            # Валидируем данные
            rate = float(work_type.hourly_rate or work_type.value or 0)
            is_valid, error_msg = validate_work_entry_data(quantity, rate)
            if not is_valid:
                raise ValidationError(error_msg)
            
            # Рассчитываем сумму
            calculated_amount = calculate_sum(work_type, quantity)
            
            # Создаем запись
            work_entry = WorkEntry(
                user_id=user_id,
                project_id=project_id,
                work_type_id=work_type_id,
                company_id=company_id,
                date=date,
                quantity=quantity,
                description=description,
                calculated_amount=calculated_amount,
                # Дополнительные поля для совместимости
                sum_total=calculated_amount,
                created_by=user_id,
                work_date=date,
                hours=quantity,
                total_amount=calculated_amount
            )
            
            session.add(work_entry)
            await session.commit()
            await session.refresh(work_entry)
            
            logger.info(f"Создана запись о работе ID {work_entry.entry_id} для пользователя {user_id}")
            return work_entry
            
        except Exception as e:
            await session.rollback()
            logger.error(f"Ошибка создания записи о работе: {e}")
            raise


    @staticmethod
    async def get_user_work_entries(
        session: AsyncSession,
        user_id: int,
        company_id: Optional[int] = None,
        project_id: Optional[int] = None,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        limit: int = 50,
        offset: int = 0
    ) -> List[WorkEntry]:
        """
        Получает записи о работе пользователя с фильтрацией.
        
        Args:
            session: Сессия БД
            user_id: ID пользователя
            company_id: ID компании (для фильтрации)
            project_id: ID проекта (для фильтрации)
            start_date: Начальная дата
            end_date: Конечная дата
            limit: Лимит записей
            offset: Смещение для пагинации
            
        Returns:
            Список записей о работе
        """
        try:
            query = select(WorkEntry).options(
                selectinload(WorkEntry.work_type),
                selectinload(WorkEntry.project),
                selectinload(WorkEntry.user)
            ).where(
                and_(
                    WorkEntry.user_id == user_id,
                    WorkEntry.is_deleted == False
                )
            )
            
            # Фильтрация по компании
            if company_id:
                query = query.where(WorkEntry.company_id == company_id)
            
            # Фильтрация по проекту
            if project_id:
                query = query.where(WorkEntry.project_id == project_id)
            
            # Фильтрация по датам
            if start_date:
                query = query.where(WorkEntry.date >= start_date)
            if end_date:
                query = query.where(WorkEntry.date <= end_date)
            
            # Сортировка и пагинация
            query = query.order_by(desc(WorkEntry.date), desc(WorkEntry.created_at))
            query = query.limit(limit).offset(offset)
            
            result = await session.execute(query)
            entries = list(result.scalars().all())
            
            logger.info(f"Получено {len(entries)} записей для пользователя {user_id}")
            return entries
            
        except Exception as e:
            logger.error(f"Ошибка получения записей пользователя {user_id}: {e}")
            raise


    @staticmethod
    async def get_work_entry_by_id(
        session: AsyncSession,
        entry_id: int,
        user_id: int
    ) -> Optional[WorkEntry]:
        """
        Получает запись о работе по ID с проверкой прав доступа.
        
        Args:
            session: Сессия БД
            entry_id: ID записи
            user_id: ID пользователя (для проверки прав)
            
        Returns:
            Запись о работе или None
        """
        try:
            result = await session.execute(
                select(WorkEntry).options(
                    selectinload(WorkEntry.work_type),
                    selectinload(WorkEntry.project)
                ).where(
                    and_(
                        WorkEntry.entry_id == entry_id,
                        WorkEntry.user_id == user_id,
                        WorkEntry.is_deleted == False
                    )
                )
            )
            return result.scalar_one_or_none()
            
        except Exception as e:
            logger.error(f"Ошибка получения записи {entry_id}: {e}")
            raise


    @staticmethod
    async def update_work_entry(
        session: AsyncSession,
        entry_id: int,
        user_id: int,
        **updates
    ) -> WorkEntry:
        """
        Обновляет запись о работе.
        
        Args:
            session: Сессия БД
            entry_id: ID записи
            user_id: ID пользователя (для проверки прав)
            **updates: Поля для обновления
            
        Returns:
            Обновленная запись
            
        Raises:
            NotFoundError: Если запись не найдена
            PermissionError: Если нет прав на редактирование
        """
        try:
            # Получаем запись
            entry = await WorkEntryService.get_work_entry_by_id(session, entry_id, user_id)
            if not entry:
                raise NotFoundError(f"Запись {entry_id} не найдена")
            
            # Обновляем поля
            for field, value in updates.items():
                if hasattr(entry, field):
                    setattr(entry, field, value)
            
            # Пересчитываем сумму если изменилось количество
            if 'quantity' in updates:
                work_type = entry.work_type
                entry.calculated_amount = calculate_sum(work_type, entry.quantity)
                entry.sum_total = entry.calculated_amount
                entry.total_amount = entry.calculated_amount
            
            await session.commit()
            await session.refresh(entry)
            
            logger.info(f"Обновлена запись {entry_id}")
            return entry
            
        except Exception as e:
            await session.rollback()
            logger.error(f"Ошибка обновления записи {entry_id}: {e}")
            raise


    @staticmethod
    async def delete_work_entry(
        session: AsyncSession,
        entry_id: int,
        user_id: int
    ) -> bool:
        """
        Мягкое удаление записи о работе.
        
        Args:
            session: Сессия БД
            entry_id: ID записи
            user_id: ID пользователя (для проверки прав)
            
        Returns:
            True если удалено успешно
            
        Raises:
            NotFoundError: Если запись не найдена
        """
        try:
            entry = await WorkEntryService.get_work_entry_by_id(session, entry_id, user_id)
            if not entry:
                raise NotFoundError(f"Запись {entry_id} не найдена")
            
            entry.is_deleted = True
            await session.commit()
            
            logger.info(f"Удалена запись {entry_id}")
            return True
            
        except Exception as e:
            await session.rollback()
            logger.error(f"Ошибка удаления записи {entry_id}: {e}")
            raise


    @staticmethod
    async def get_entries_for_export(
        session: AsyncSession,
        user_id: int,
        project_id: Optional[int] = None,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None
    ) -> List[Dict[str, Any]]:
        """
        Получает записи для экспорта в формате словарей.
        
        Args:
            session: Сессия БД
            user_id: ID пользователя
            project_id: ID проекта
            start_date: Начальная дата
            end_date: Конечная дата
            
        Returns:
            Список словарей с данными для экспорта
        """
        entries = await WorkEntryService.get_user_work_entries(
            session=session,
            user_id=user_id,
            project_id=project_id,
            start_date=start_date,
            end_date=end_date,
            limit=10000  # Большой лимит для экспорта
        )
        
        export_data = []
        for entry in entries:
            export_data.append({
                "date": entry.date,
                "work_type": entry.work_type.name if entry.work_type else "",
                "description": entry.description,
                "quantity": float(entry.quantity),
                "unit": entry.work_type.unit if entry.work_type else "",
                "rate": float(entry.work_type.hourly_rate or entry.work_type.value or 0),
                "sum": float(entry.calculated_amount)
            })
        
        return export_data
