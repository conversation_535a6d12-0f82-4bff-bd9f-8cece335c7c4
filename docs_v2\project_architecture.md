# 🏗️ АРХИТЕКТУРА ПРОЕКТА WORKLOG MVP v2.0

## 🎯 Общие принципы

### Архитектурные решения v2.0
1. **Модульная архитектура** - четкое разделение ответственности
2. **Event-driven подход** - использование FSM для сложных операций
3. **Единый интерфейс** - все взаимодействие через inline-клавиатуры
4. **Безопасность по умолчанию** - проверка прав на каждом уровне
5. **Простота тестирования** - каждый компонент изолирован

## 📊 Диаграмма архитектуры

```mermaid
graph TB
    User[👤 Пользователь] --> TG[📱 Telegram Bot]
    TG --> Router[🔀 Router/Dispatcher]
    Router --> Auth[🔐 Auth Middleware]
    Auth --> RBAC[👮 RBAC Middleware]
    RBAC --> Handlers[📋 Handlers]
    
    Handlers --> Services[⚙️ Services]
    Services --> Models[🗃️ Models]
    Models --> DB[(🗄️ PostgreSQL)]
    
    Handlers --> FSM[🔄 FSM States]
    Handlers --> Keyboards[⌨️ Inline Keyboards]
    Handlers --> Localization[🌍 Localization]
    
    subgraph "Services Layer"
        AuthService[🔐 AuthService]
        WorkService[👷 WorkService]
        ProjectService[🏗️ ProjectService]
        DirectorService[👨‍💼 DirectorService]
        ReportService[📊 ReportService]
        ExportService[📤 ExportService]
    end
```

## 🏛️ Слои архитектуры

### 1. **Presentation Layer (Telegram Bot)**
```
📱 Telegram Bot Interface
├── 🔀 Dispatcher/Router
├── 📋 Message Handlers
├── ⌨️ Inline Keyboards
└── 🌍 Localization
```

**Ответственность:**
- Обработка входящих сообщений
- Маршрутизация команд
- Формирование ответов
- Управление inline-клавиатурами

### 2. **Security Layer (Middleware)**
```
🛡️ Security & Auth
├── 🔐 Authentication
├── 👮 RBAC (Role-Based Access Control)
├── 🔒 Session Management
└── ✅ Input Validation
```

**Ответственность:**
- Аутентификация пользователей
- Проверка прав доступа
- Управление сессиями
- Валидация входных данных

### 3. **Business Logic Layer (Services)**
```
⚙️ Business Services
├── 🔐 AuthService (аутентификация)
├── 👷 WorkService (записи о работе)
├── 🏗️ ProjectService (проекты)
├── 👨‍💼 DirectorService (управление)
├── 📊 ReportService (отчёты)
└── 📤 ExportService (экспорт)
```

**Ответственность:**
- Бизнес-логика приложения
- Валидация бизнес-правил
- Координация между компонентами
- Обработка сложных операций

### 4. **Data Access Layer (Models)**
```
🗃️ Data Models
├── 👤 User (пользователи)
├── 🏢 Company (компании)
├── 🏗️ Project (проекты)
├── 📝 WorkEntry (записи о работе)
├── 🎫 RegistrationToken (токены)
└── 📊 ExportLog (логи экспорта)
```

**Ответственность:**
- Определение структуры данных
- ORM маппинг
- Связи между сущностями
- Валидация на уровне модели

### 5. **Infrastructure Layer**
```
🔧 Infrastructure
├── 🗄️ PostgreSQL Database
├── 📁 File Storage
├── 📧 Notification System
└── 🔍 Logging & Monitoring
```

## 🔄 Временная система ролей (MVP)

### Упрощённая схема для тестирования

```mermaid
sequenceDiagram
    participant U as 👤 User
    participant B as 🤖 Bot
    participant DB as 🗄️ Database
    
    U->>B: /start
    B->>U: Выбор роли [👑 Админ] [👨‍💼 Директор] [👷 Рабочий]
    U->>B: Выбирает роль
    B->>DB: Сохраняет роль в users.role
    B->>U: Показывает меню роли
```

### Переход к токен-системе

После успешного тестирования MVP:

```mermaid
sequenceDiagram
    participant D as 👨‍💼 Director
    participant B as 🤖 Bot
    participant W as 👷 Worker
    participant DB as 🗄️ Database
    
    D->>B: Создаёт токен регистрации
    B->>DB: Сохраняет токен
    D->>W: Отправляет ссылку с токеном
    W->>B: Переходит по ссылке
    B->>DB: Проверяет токен
    B->>DB: Регистрирует пользователя с ролью
    B->>W: Показывает меню роли
```

## 🎭 Роли и права доступа

### 👑 Администратор
```
Права доступа:
✅ Все функции директора
✅ Управление компаниями
✅ Системные настройки
✅ Просмотр всех данных
✅ Управление пользователями
```

### 👨‍💼 Директор
```
Права доступа:
✅ Создание проектов
✅ Управление рабочими
✅ Создание токенов регистрации
✅ Просмотр отчётов компании
✅ Экспорт данных
✅ Назначение рабочих на проекты
❌ Системные настройки
```

### 👷 Рабочий
```
Права доступа:
✅ Добавление записей о работе
✅ Редактирование своих записей
✅ Просмотр своих данных
✅ Просмотр назначенных проектов
✅ Создание заметок и запросов
❌ Управление проектами
❌ Просмотр данных других рабочих
```

## 🔄 FSM (Finite State Machine) Архитектура

### Принципы использования FSM

1. **Сложные операции** - многошаговые процессы
2. **Валидация данных** - проверка на каждом шаге
3. **Отмена операций** - возможность прервать процесс
4. **Сохранение состояния** - данные между шагами

### Основные FSM группы

```python
# Рабочий
class WorkerStates(StatesGroup):
    selecting_project = State()
    selecting_work_type = State()
    entering_description = State()
    entering_quantity = State()
    confirming_entry = State()

# Директор
class DirectorStates(StatesGroup):
    creating_project_name = State()
    creating_project_address = State()
    setting_project_rates = State()
    confirming_project = State()

# Отчёты
class ReportStates(StatesGroup):
    selecting_report_type = State()
    selecting_date_range = State()
    selecting_filters = State()
    confirming_export = State()
```

## 📱 Reply-клавиатуры архитектура

### Принципы дизайна

1. **Единообразие** - одинаковый стиль для всех кнопок
2. **Интуитивность** - понятные иконки и текст
3. **Навигация** - всегда есть кнопка "Назад"
4. **Контекст** - кнопки соответствуют текущему состоянию

### Обработка текстовых команд

```python
# Обработка команд с кнопок
@router.message(F.text == "➕ Добавить работу")
async def add_work_handler(message: Message):
    # Логика добавления работы
    pass

@router.message(F.text == "📋 Мои работы")
async def list_work_handler(message: Message):
    # Логика просмотра работ
    pass

@router.message(F.text == "🔙 Назад")
async def back_handler(message: Message):
    # Логика возврата назад
    pass
```

## 🔒 Безопасность

### Уровни защиты

1. **Telegram уровень** - проверка подлинности пользователя
2. **Middleware уровень** - аутентификация и авторизация
3. **Service уровень** - бизнес-правила и валидация
4. **Database уровень** - ограничения и связи

### Принципы безопасности

- **Принцип минимальных привилегий** - только необходимые права
- **Изоляция данных** - пользователи видят только свои данные
- **Валидация входных данных** - на всех уровнях
- **Аудит действий** - логирование важных операций

## 📊 Производительность

### Оптимизации

1. **Lazy Loading** - загрузка данных по требованию
2. **Кэширование** - часто используемые данные
3. **Пагинация** - разбивка больших списков
4. **Индексы БД** - оптимизация запросов

### Мониторинг

- **Время отклика** - метрики производительности
- **Использование памяти** - контроль ресурсов
- **Ошибки** - отслеживание проблем
- **Пользовательская активность** - аналитика использования

## 🧪 Тестируемость

### Принципы

1. **Dependency Injection** - внедрение зависимостей
2. **Мокирование** - изоляция компонентов
3. **Фикстуры** - подготовка тестовых данных
4. **Покрытие** - контроль качества тестов

### Уровни тестирования

- **Unit тесты** - отдельные компоненты
- **Integration тесты** - взаимодействие компонентов
- **E2E тесты** - полные пользовательские сценарии
- **Performance тесты** - нагрузочное тестирование

## 🚀 Масштабируемость

### Горизонтальное масштабирование

- **Stateless архитектура** - без состояния на сервере
- **Database sharding** - разделение данных
- **Load balancing** - распределение нагрузки
- **Microservices готовность** - модульная архитектура

### Вертикальное масштабирование

- **Оптимизация запросов** - эффективное использование БД
- **Кэширование** - снижение нагрузки
- **Асинхронность** - неблокирующие операции
- **Resource pooling** - управление ресурсами
