"""Update models with compatibility fields

Revision ID: 5d75fe6ac4f2
Revises: fix_schema_issues
Create Date: 2025-06-30 21:39:14.650497

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '5d75fe6ac4f2'
down_revision: Union[str, None] = 'fix_schema_issues'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('companies', 'address',
               existing_type=sa.TEXT(),
               type_=sa.String(length=300),
               existing_nullable=True)
    op.alter_column('companies', 'is_deleted',
               existing_type=sa.BOOLEAN(),
               nullable=False,
               existing_server_default=sa.text('false'))
    op.alter_column('projects', 'address',
               existing_type=sa.VARCHAR(length=255),
               type_=sa.String(length=300),
               existing_nullable=True)
    op.alter_column('projects', 'is_deleted',
               existing_type=sa.BOOLEAN(),
               nullable=False,
               existing_server_default=sa.text('false'))
    op.drop_constraint('unique_project_name_per_company', 'projects', type_='unique')
    op.drop_constraint('projects_company_id_fkey', 'projects', type_='foreignkey')
    op.create_foreign_key(None, 'projects', 'companies', ['company_id'], ['id'])
    op.drop_constraint('tokens_company_id_fkey', 'tokens', type_='foreignkey')
    op.create_foreign_key(None, 'tokens', 'companies', ['company_id'], ['id'])
    op.alter_column('user_company_roles', 'permissions',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.JSON(),
               existing_nullable=True,
               existing_server_default=sa.text("'{}'::jsonb"))
    op.drop_constraint('unique_user_company_role', 'user_company_roles', type_='unique')
    op.create_unique_constraint('unique_user_company', 'user_company_roles', ['user_id', 'company_id'])
    op.drop_constraint('user_company_roles_company_id_fkey', 'user_company_roles', type_='foreignkey')
    op.create_foreign_key(None, 'user_company_roles', 'users', ['user_id'], ['user_id'])
    op.create_foreign_key(None, 'user_company_roles', 'companies', ['company_id'], ['id'])
    op.drop_constraint('work_entries_company_id_fkey', 'work_entries', type_='foreignkey')
    op.create_foreign_key(None, 'work_entries', 'companies', ['company_id'], ['id'])
    op.create_foreign_key(None, 'work_entries', 'users', ['user_id'], ['user_id'])
    op.alter_column('work_types', 'rate_type',
               existing_type=sa.VARCHAR(length=50),
               type_=sa.String(length=20),
               existing_nullable=False)
    op.drop_constraint('work_types_project_id_name_key', 'work_types', type_='unique')
    op.drop_constraint('work_types_project_id_fkey', 'work_types', type_='foreignkey')
    op.create_foreign_key(None, 'work_types', 'projects', ['project_id'], ['project_id'])
    op.create_foreign_key(None, 'work_types', 'companies', ['company_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'work_types', type_='foreignkey')
    op.drop_constraint(None, 'work_types', type_='foreignkey')
    op.create_foreign_key('work_types_project_id_fkey', 'work_types', 'projects', ['project_id'], ['project_id'], ondelete='CASCADE')
    op.create_unique_constraint('work_types_project_id_name_key', 'work_types', ['project_id', 'name'])
    op.alter_column('work_types', 'rate_type',
               existing_type=sa.String(length=20),
               type_=sa.VARCHAR(length=50),
               existing_nullable=False)
    op.drop_constraint(None, 'work_entries', type_='foreignkey')
    op.drop_constraint(None, 'work_entries', type_='foreignkey')
    op.create_foreign_key('work_entries_company_id_fkey', 'work_entries', 'companies', ['company_id'], ['id'], ondelete='CASCADE')
    op.drop_constraint(None, 'user_company_roles', type_='foreignkey')
    op.drop_constraint(None, 'user_company_roles', type_='foreignkey')
    op.create_foreign_key('user_company_roles_company_id_fkey', 'user_company_roles', 'companies', ['company_id'], ['id'], ondelete='CASCADE')
    op.drop_constraint('unique_user_company', 'user_company_roles', type_='unique')
    op.create_unique_constraint('unique_user_company_role', 'user_company_roles', ['user_id', 'company_id', 'role'])
    op.alter_column('user_company_roles', 'permissions',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True,
               existing_server_default=sa.text("'{}'::jsonb"))
    op.drop_constraint(None, 'tokens', type_='foreignkey')
    op.create_foreign_key('tokens_company_id_fkey', 'tokens', 'companies', ['company_id'], ['id'], ondelete='CASCADE')
    op.drop_constraint(None, 'projects', type_='foreignkey')
    op.create_foreign_key('projects_company_id_fkey', 'projects', 'companies', ['company_id'], ['id'], ondelete='CASCADE')
    op.create_unique_constraint('unique_project_name_per_company', 'projects', ['company_id', 'name'])
    op.alter_column('projects', 'is_deleted',
               existing_type=sa.BOOLEAN(),
               nullable=True,
               existing_server_default=sa.text('false'))
    op.alter_column('projects', 'address',
               existing_type=sa.String(length=300),
               type_=sa.VARCHAR(length=255),
               existing_nullable=True)
    op.alter_column('companies', 'is_deleted',
               existing_type=sa.BOOLEAN(),
               nullable=True,
               existing_server_default=sa.text('false'))
    op.alter_column('companies', 'address',
               existing_type=sa.String(length=300),
               type_=sa.TEXT(),
               existing_nullable=True)
    # ### end Alembic commands ###
