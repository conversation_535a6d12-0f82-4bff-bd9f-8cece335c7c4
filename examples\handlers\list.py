"""
Обработчики для просмотра списка записей о работе.

Команда: /list
FSM: ListStates (2 шага)
Права: Доступ к записям пользователя

Логика:
1. selecting_filter - выбор фильтра (все/за период)
2. entering_date_range - ввод диапазона дат

Особенности:
- Пагинация для больших списков
- Фильтрация по датам
- Форматированный вывод с суммами
"""
import logging
from aiogram import types, Dispatcher
from aiogram.dispatcher import FSMContext
import datetime

from db.dao.entry_dao import EntryDAO
from db.session import async_session
from keyboards.common import create_main_menu, create_cancel_keyboard, create_pagination_keyboard
from localization.texts import get_text
from services.calculator import format_currency, format_quantity
from states import ListStates
from utils.date_helpers import validate_date_range
from middleware import require_permission

logger = logging.getLogger(__name__)

# Константы для пагинации
ENTRIES_PER_PAGE = 10


def register_handlers_list(dp: Dispatcher):
    """Регистрирует обработчики для команды /list."""
    dp.register_message_handler(cmd_list, commands=["list"], state="*")
    dp.register_message_handler(process_filter_selection, state=ListStates.selecting_filter)
    dp.register_message_handler(process_date_range_input, state=ListStates.entering_date_range)
    dp.register_callback_query_handler(
        process_pagination,
        lambda c: c.data.startswith("list_page_"),
        state="*"
    )


@require_permission("can_add_work")  # Те же права что и для добавления
async def cmd_list(message: types.Message, state: FSMContext, **kwargs):
    """
    Начало сценария просмотра списка записей.
    
    Показывает:
    - Фильтры: "Все записи" / "За период"
    """
    await state.finish()
    
    keyboard = types.ReplyKeyboardMarkup(resize_keyboard=True, one_time_keyboard=True)
    keyboard.add(
        types.KeyboardButton("Все записи"),
        types.KeyboardButton("За период")
    )
    keyboard.add(types.KeyboardButton(get_text("cancel")))
    
    await message.answer(get_text("list_choose_filter"), reply_markup=keyboard)
    await ListStates.selecting_filter.set()


async def process_filter_selection(message: types.Message, state: FSMContext):
    """
    Обработка выбора фильтра.
    
    Варианты:
    - "Все записи" → показывает все записи пользователя
    - "За период" → запрашивает диапазон дат
    """
    choice = message.text
    
    if choice.lower() == get_text("cancel").lower():
        await message.answer(get_text("operation_cancelled"), reply_markup=create_main_menu())
        await state.finish()
        return
    
    user_id = message.from_user.id
    
    if choice == "Все записи":
        await show_entries_list(message, user_id, page=1)
        await state.finish()
    elif choice == "За период":
        await message.answer(
            get_text("list_enter_date_range"),
            reply_markup=create_cancel_keyboard()
        )
        await ListStates.entering_date_range.set()
    else:
        await message.answer(get_text("invalid_choice"))


async def process_date_range_input(message: types.Message, state: FSMContext):
    """
    Обработка ввода диапазона дат.
    
    Формат: "01.01.2023-31.01.2023"
    Валидация через validate_date_range()
    """
    if message.text.lower() == get_text("cancel").lower():
        await message.answer(get_text("operation_cancelled"), reply_markup=create_main_menu())
        await state.finish()
        return
    
    # Валидация диапазона дат
    is_valid, error_msg, dates = validate_date_range(message.text)
    if not is_valid:
        await message.answer(get_text("invalid_date_range", error=error_msg))
        return
    
    start_date, end_date = dates
    user_id = message.from_user.id
    
    await show_entries_list(
        message, 
        user_id, 
        page=1, 
        start_date=start_date, 
        end_date=end_date
    )
    await state.finish()


async def show_entries_list(
    message: types.Message,
    user_id: int,
    page: int = 1,
    start_date: datetime.date = None,
    end_date: datetime.date = None
):
    """
    Показывает список записей с пагинацией.
    
    Args:
        message: Сообщение пользователя
        user_id: ID пользователя
        page: Номер страницы
        start_date: Начальная дата фильтра
        end_date: Конечная дата фильтра
    """
    try:
        async with async_session() as session:
            # Получаем все записи для подсчета страниц
            all_entries = await EntryDAO.get_by_user_and_date_range(
                session=session,
                user_id=user_id,
                start_date=start_date,
                end_date=end_date
            )
        
        if not all_entries:
            period_text = ""
            if start_date and end_date:
                period_text = f" за период {start_date.strftime('%d.%m.%Y')} - {end_date.strftime('%d.%m.%Y')}"
            elif start_date:
                period_text = f" с {start_date.strftime('%d.%m.%Y')}"
            elif end_date:
                period_text = f" до {end_date.strftime('%d.%m.%Y')}"
            
            await message.answer(
                get_text("no_entries_found") + period_text,
                reply_markup=create_main_menu()
            )
            return
        
        # Пагинация
        total_entries = len(all_entries)
        total_pages = (total_entries + ENTRIES_PER_PAGE - 1) // ENTRIES_PER_PAGE
        start_idx = (page - 1) * ENTRIES_PER_PAGE
        end_idx = start_idx + ENTRIES_PER_PAGE
        page_entries = all_entries[start_idx:end_idx]
        
        # Формируем текст списка
        text_lines = []
        
        # Заголовок
        if start_date and end_date:
            period_text = f"за период {start_date.strftime('%d.%m.%Y')} - {end_date.strftime('%d.%m.%Y')}"
        elif start_date:
            period_text = f"с {start_date.strftime('%d.%m.%Y')}"
        elif end_date:
            period_text = f"до {end_date.strftime('%d.%m.%Y')}"
        else:
            period_text = "за всё время"
        
        text_lines.append(f"📋 **Записи о работе {period_text}**")
        text_lines.append(f"Страница {page} из {total_pages} (всего записей: {total_entries})")
        text_lines.append("")
        
        # Записи на текущей странице
        page_total = 0
        for i, entry in enumerate(page_entries, start_idx + 1):
            # Форматируем дату
            date_str = entry.date.strftime("%d.%m.%Y")
            
            # Форматируем количество
            quantity_str = format_quantity(entry.quantity, entry.work_type.unit)
            
            # Форматируем сумму
            sum_str = format_currency(entry.sum_total)
            
            # Описание (обрезаем если длинное)
            description = entry.description or "Без описания"
            if len(description) > 50:
                description = description[:47] + "..."
            
            # Формируем строку записи
            entry_text = (
                f"**{i}.** {date_str} - {entry.work_type.name}\n"
                f"   {description}\n"
                f"   {quantity_str} × {format_currency(entry.work_type.value)} = {sum_str}\n"
            )
            text_lines.append(entry_text)
            page_total += entry.sum_total
        
        # Итого по странице
        if page_entries:
            text_lines.append(f"💰 **Итого по странице:** {format_currency(page_total)}")
        
        # Общий итог
        if page == 1:  # Показываем только на первой странице
            total_sum = sum(entry.sum_total for entry in all_entries)
            text_lines.append(f"💰 **Общий итог:** {format_currency(total_sum)}")
        
        # Отправляем сообщение
        text = "\n".join(text_lines)
        
        # Клавиатура пагинации
        if total_pages > 1:
            keyboard = create_pagination_keyboard(
                current_page=page,
                total_pages=total_pages,
                callback_prefix="list"
            )
            # Добавляем кнопку возврата в меню
            keyboard.add(
                types.InlineKeyboardButton(
                    "🔙 В главное меню",
                    callback_data="list_back_to_menu"
                )
            )
            await message.answer(text, reply_markup=keyboard, parse_mode="Markdown")
        else:
            await message.answer(text, reply_markup=create_main_menu(), parse_mode="Markdown")
    
    except Exception as e:
        logger.exception(f"Ошибка при показе списка записей для user {user_id}: {e}")
        await message.answer(
            get_text("error_loading_entries"),
            reply_markup=create_main_menu()
        )


async def process_pagination(callback_query: types.CallbackQuery, state: FSMContext):
    """
    Обработка пагинации списка записей.
    
    Callback data формат: "list_page_{page_number}"
    """
    try:
        await callback_query.answer()
        
        if callback_query.data == "list_back_to_menu":
            await callback_query.message.edit_reply_markup(reply_markup=None)
            await callback_query.message.answer(
                get_text("back_to_menu"),
                reply_markup=create_main_menu()
            )
            return
        
        # Извлекаем номер страницы
        page = int(callback_query.data.split("_")[-1])
        user_id = callback_query.from_user.id
        
        # Получаем параметры фильтра из состояния (если есть)
        data = await state.get_data()
        start_date = data.get("start_date")
        end_date = data.get("end_date")
        
        # Показываем новую страницу
        await show_entries_list_edit(
            callback_query.message,
            user_id,
            page,
            start_date,
            end_date
        )
    
    except Exception as e:
        logger.exception(f"Ошибка пагинации для user {callback_query.from_user.id}: {e}")
        await callback_query.answer("Ошибка при загрузке страницы")


async def show_entries_list_edit(
    message: types.Message,
    user_id: int,
    page: int,
    start_date: datetime.date = None,
    end_date: datetime.date = None
):
    """
    Показывает список записей с редактированием сообщения (для пагинации).
    
    Аналогично show_entries_list, но использует edit_text вместо answer.
    """
    try:
        async with async_session() as session:
            all_entries = await EntryDAO.get_by_user_and_date_range(
                session=session,
                user_id=user_id,
                start_date=start_date,
                end_date=end_date
            )
        
        if not all_entries:
            await message.edit_text(
                get_text("no_entries_found"),
                reply_markup=None
            )
            return
        
        # Пагинация
        total_entries = len(all_entries)
        total_pages = (total_entries + ENTRIES_PER_PAGE - 1) // ENTRIES_PER_PAGE
        start_idx = (page - 1) * ENTRIES_PER_PAGE
        end_idx = start_idx + ENTRIES_PER_PAGE
        page_entries = all_entries[start_idx:end_idx]
        
        # Формируем текст (аналогично show_entries_list)
        text_lines = []
        
        if start_date and end_date:
            period_text = f"за период {start_date.strftime('%d.%m.%Y')} - {end_date.strftime('%d.%m.%Y')}"
        elif start_date:
            period_text = f"с {start_date.strftime('%d.%m.%Y')}"
        elif end_date:
            period_text = f"до {end_date.strftime('%d.%m.%Y')}"
        else:
            period_text = "за всё время"
        
        text_lines.append(f"📋 **Записи о работе {period_text}**")
        text_lines.append(f"Страница {page} из {total_pages} (всего записей: {total_entries})")
        text_lines.append("")
        
        page_total = 0
        for i, entry in enumerate(page_entries, start_idx + 1):
            date_str = entry.date.strftime("%d.%m.%Y")
            quantity_str = format_quantity(entry.quantity, entry.work_type.unit)
            sum_str = format_currency(entry.sum_total)
            
            description = entry.description or "Без описания"
            if len(description) > 50:
                description = description[:47] + "..."
            
            entry_text = (
                f"**{i}.** {date_str} - {entry.work_type.name}\n"
                f"   {description}\n"
                f"   {quantity_str} × {format_currency(entry.work_type.value)} = {sum_str}\n"
            )
            text_lines.append(entry_text)
            page_total += entry.sum_total
        
        if page_entries:
            text_lines.append(f"💰 **Итого по странице:** {format_currency(page_total)}")
        
        text = "\n".join(text_lines)
        
        # Клавиатура пагинации
        keyboard = create_pagination_keyboard(
            current_page=page,
            total_pages=total_pages,
            callback_prefix="list"
        )
        keyboard.add(
            types.InlineKeyboardButton(
                "🔙 В главное меню",
                callback_data="list_back_to_menu"
            )
        )
        
        await message.edit_text(text, reply_markup=keyboard, parse_mode="Markdown")
    
    except Exception as e:
        logger.exception(f"Ошибка при редактировании списка записей для user {user_id}: {e}")
        await message.edit_text("Ошибка при загрузке записей")


# Примеры использования:
"""
# Регистрация обработчиков
register_handlers_list(dp)

# Команда /list
# Пользователь вводит: /list
# Бот показывает: "Все записи" / "За период"
# Пользователь выбирает фильтр
# Бот показывает список с пагинацией

# Пример вывода:
📋 **Записи о работе за всё время**
Страница 1 из 3 (всего записей: 25)

**1.** 15.01.2023 - Монтаж
   Установка оборудования
   8.0 час × 1 000.00 руб. = 8 000.00 руб.

**2.** 16.01.2023 - Сварка
   Сварочные работы
   4.5 час × 1 200.00 руб. = 5 400.00 руб.

💰 **Итого по странице:** 13 400.00 руб.
💰 **Общий итог:** 125 000.00 руб.
"""
