"""
ReportService для генерации отчетов

Предоставляет функции для создания отчетов по датам, рабочим и проектам
с фильтрацией и статистикой.
"""
import logging
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Any, Tuple
from sqlalchemy import select, func, and_, or_
from sqlalchemy.orm import joinedload

from db.database import async_session
from db.models import WorkEntry, User, Project, WorkType, Company, UserCompanyRole

logger = logging.getLogger(__name__)


class ReportService:
    """Сервис для генерации отчетов"""

    @staticmethod
    async def get_date_report(
        company_id: int,
        start_date: date,
        end_date: date,
        worker_id: Optional[int] = None,
        project_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Генерация отчета по датам
        
        Args:
            company_id: ID компании
            start_date: Начальная дата
            end_date: Конечная дата
            worker_id: ID рабочего (опционально)
            project_id: ID проекта (опционально)
            
        Returns:
            Словарь с данными отчета
        """
        async with async_session() as session:
            try:
                # Базовый запрос
                query = select(WorkEntry).options(
                    joinedload(WorkEntry.user),
                    joinedload(WorkEntry.project),
                    joinedload(WorkEntry.work_type)
                ).where(
                    and_(
                        WorkEntry.company_id == company_id,
                        WorkEntry.work_date >= start_date,
                        WorkEntry.work_date <= end_date,
                        WorkEntry.is_deleted == False
                    )
                )
                
                # Дополнительные фильтры
                if worker_id:
                    query = query.where(WorkEntry.user_id == worker_id)
                if project_id:
                    query = query.where(WorkEntry.project_id == project_id)
                
                result = await session.execute(query)
                entries = result.scalars().all()
                
                # Подсчет статистики
                total_entries = len(entries)
                total_hours = sum(entry.hours for entry in entries)
                total_amount = sum(entry.total_amount for entry in entries)
                
                # Группировка по рабочим
                workers_stats = {}
                for entry in entries:
                    worker_id = entry.user_id
                    if worker_id not in workers_stats:
                        workers_stats[worker_id] = {
                            'name': entry.user.display_name if entry.user else 'Неизвестно',
                            'entries': 0,
                            'hours': 0.0,
                            'amount': 0.0
                        }
                    workers_stats[worker_id]['entries'] += 1
                    workers_stats[worker_id]['hours'] += entry.hours
                    workers_stats[worker_id]['amount'] += entry.total_amount
                
                # Группировка по проектам
                projects_stats = {}
                for entry in entries:
                    project_id = entry.project_id
                    if project_id not in projects_stats:
                        projects_stats[project_id] = {
                            'name': entry.project.name if entry.project else 'Неизвестно',
                            'entries': 0,
                            'hours': 0.0,
                            'amount': 0.0
                        }
                    projects_stats[project_id]['entries'] += 1
                    projects_stats[project_id]['hours'] += entry.hours
                    projects_stats[project_id]['amount'] += entry.total_amount
                
                # Группировка по типам работ
                work_types_stats = {}
                for entry in entries:
                    work_type_id = entry.work_type_id
                    if work_type_id not in work_types_stats:
                        work_types_stats[work_type_id] = {
                            'name': entry.work_type.name if entry.work_type else 'Неизвестно',
                            'entries': 0,
                            'hours': 0.0,
                            'amount': 0.0
                        }
                    work_types_stats[work_type_id]['entries'] += 1
                    work_types_stats[work_type_id]['hours'] += entry.hours
                    work_types_stats[work_type_id]['amount'] += entry.total_amount
                
                return {
                    'period': {
                        'start_date': start_date,
                        'end_date': end_date,
                        'days': (end_date - start_date).days + 1
                    },
                    'summary': {
                        'total_entries': total_entries,
                        'total_hours': round(total_hours, 2),
                        'total_amount': round(total_amount, 2),
                        'workers_count': len(workers_stats),
                        'projects_count': len(projects_stats),
                        'work_types_count': len(work_types_stats)
                    },
                    'workers': workers_stats,
                    'projects': projects_stats,
                    'work_types': work_types_stats,
                    'entries': [
                        {
                            'id': entry.id,
                            'date': entry.work_date,
                            'worker': entry.user.display_name if entry.user else 'Неизвестно',
                            'project': entry.project.name if entry.project else 'Неизвестно',
                            'work_type': entry.work_type.name if entry.work_type else 'Неизвестно',
                            'hours': entry.hours,
                            'rate': entry.hourly_rate,
                            'amount': entry.total_amount,
                            'description': entry.description
                        }
                        for entry in entries
                    ]
                }
                
            except Exception as e:
                logger.error(f"Ошибка при генерации отчета по датам: {e}")
                return {
                    'period': {'start_date': start_date, 'end_date': end_date, 'days': 0},
                    'summary': {'total_entries': 0, 'total_hours': 0.0, 'total_amount': 0.0,
                              'workers_count': 0, 'projects_count': 0, 'work_types_count': 0},
                    'workers': {}, 'projects': {}, 'work_types': {}, 'entries': []
                }

    @staticmethod
    async def get_worker_report(
        company_id: int,
        worker_id: int,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None
    ) -> Dict[str, Any]:
        """
        Генерация отчета по рабочему
        
        Args:
            company_id: ID компании
            worker_id: ID рабочего
            start_date: Начальная дата (опционально)
            end_date: Конечная дата (опционально)
            
        Returns:
            Словарь с данными отчета по рабочему
        """
        # Если даты не указаны, берем последние 30 дней
        if not start_date:
            end_date = date.today()
            start_date = end_date - timedelta(days=30)
        elif not end_date:
            end_date = date.today()
        
        async with async_session() as session:
            try:
                # Получаем информацию о рабочем
                user_query = select(User).where(User.id == worker_id)
                user_result = await session.execute(user_query)
                user = user_result.scalar_one_or_none()
                
                if not user:
                    return {'error': 'Рабочий не найден'}
                
                # Получаем записи работ
                entries_query = select(WorkEntry).options(
                    joinedload(WorkEntry.project),
                    joinedload(WorkEntry.work_type)
                ).where(
                    and_(
                        WorkEntry.company_id == company_id,
                        WorkEntry.user_id == worker_id,
                        WorkEntry.work_date >= start_date,
                        WorkEntry.work_date <= end_date,
                        WorkEntry.is_deleted == False
                    )
                )
                
                result = await session.execute(entries_query)
                entries = result.scalars().all()
                
                # Статистика
                total_entries = len(entries)
                total_hours = sum(entry.hours for entry in entries)
                total_amount = sum(entry.total_amount for entry in entries)
                
                # Группировка по проектам
                projects_stats = {}
                for entry in entries:
                    project_id = entry.project_id
                    if project_id not in projects_stats:
                        projects_stats[project_id] = {
                            'name': entry.project.name if entry.project else 'Неизвестно',
                            'entries': 0,
                            'hours': 0.0,
                            'amount': 0.0
                        }
                    projects_stats[project_id]['entries'] += 1
                    projects_stats[project_id]['hours'] += entry.hours
                    projects_stats[project_id]['amount'] += entry.total_amount
                
                # Группировка по типам работ
                work_types_stats = {}
                for entry in entries:
                    work_type_id = entry.work_type_id
                    if work_type_id not in work_types_stats:
                        work_types_stats[work_type_id] = {
                            'name': entry.work_type.name if entry.work_type else 'Неизвестно',
                            'entries': 0,
                            'hours': 0.0,
                            'amount': 0.0
                        }
                    work_types_stats[work_type_id]['entries'] += 1
                    work_types_stats[work_type_id]['hours'] += entry.hours
                    work_types_stats[work_type_id]['amount'] += entry.total_amount
                
                return {
                    'worker': {
                        'id': user.id,
                        'name': user.display_name,
                        'telegram_id': user.telegram_id
                    },
                    'period': {
                        'start_date': start_date,
                        'end_date': end_date,
                        'days': (end_date - start_date).days + 1
                    },
                    'summary': {
                        'total_entries': total_entries,
                        'total_hours': round(total_hours, 2),
                        'total_amount': round(total_amount, 2),
                        'projects_count': len(projects_stats),
                        'work_types_count': len(work_types_stats),
                        'avg_hours_per_day': round(total_hours / ((end_date - start_date).days + 1), 2)
                    },
                    'projects': projects_stats,
                    'work_types': work_types_stats,
                    'entries': [
                        {
                            'id': entry.id,
                            'date': entry.work_date,
                            'project': entry.project.name if entry.project else 'Неизвестно',
                            'work_type': entry.work_type.name if entry.work_type else 'Неизвестно',
                            'hours': entry.hours,
                            'rate': entry.hourly_rate,
                            'amount': entry.total_amount,
                            'description': entry.description
                        }
                        for entry in entries
                    ]
                }
                
            except Exception as e:
                logger.error(f"Ошибка при генерации отчета по рабочему {worker_id}: {e}")
                return {'error': f'Ошибка при генерации отчета: {str(e)}'}

    @staticmethod
    async def get_project_report(
        company_id: int,
        project_id: int,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None
    ) -> Dict[str, Any]:
        """
        Генерация отчета по проекту

        Args:
            company_id: ID компании
            project_id: ID проекта
            start_date: Начальная дата (опционально)
            end_date: Конечная дата (опционально)

        Returns:
            Словарь с данными отчета по проекту
        """
        # Если даты не указаны, берем все записи
        if not start_date:
            start_date = date(2020, 1, 1)  # Начальная дата по умолчанию
        if not end_date:
            end_date = date.today()

        async with async_session() as session:
            try:
                # Получаем информацию о проекте
                project_query = select(Project).where(Project.id == project_id)
                project_result = await session.execute(project_query)
                project = project_result.scalar_one_or_none()

                if not project:
                    return {'error': 'Проект не найден'}

                # Получаем записи работ
                entries_query = select(WorkEntry).options(
                    joinedload(WorkEntry.user),
                    joinedload(WorkEntry.work_type)
                ).where(
                    and_(
                        WorkEntry.company_id == company_id,
                        WorkEntry.project_id == project_id,
                        WorkEntry.work_date >= start_date,
                        WorkEntry.work_date <= end_date,
                        WorkEntry.is_deleted == False
                    )
                )

                result = await session.execute(entries_query)
                entries = result.scalars().all()

                # Статистика
                total_entries = len(entries)
                total_hours = sum(entry.hours for entry in entries)
                total_amount = sum(entry.total_amount for entry in entries)

                # Группировка по рабочим
                workers_stats = {}
                for entry in entries:
                    worker_id = entry.user_id
                    if worker_id not in workers_stats:
                        workers_stats[worker_id] = {
                            'name': entry.user.display_name if entry.user else 'Неизвестно',
                            'entries': 0,
                            'hours': 0.0,
                            'amount': 0.0
                        }
                    workers_stats[worker_id]['entries'] += 1
                    workers_stats[worker_id]['hours'] += entry.hours
                    workers_stats[worker_id]['amount'] += entry.total_amount

                # Группировка по типам работ
                work_types_stats = {}
                for entry in entries:
                    work_type_id = entry.work_type_id
                    if work_type_id not in work_types_stats:
                        work_types_stats[work_type_id] = {
                            'name': entry.work_type.name if entry.work_type else 'Неизвестно',
                            'entries': 0,
                            'hours': 0.0,
                            'amount': 0.0
                        }
                    work_types_stats[work_type_id]['entries'] += 1
                    work_types_stats[work_type_id]['hours'] += entry.hours
                    work_types_stats[work_type_id]['amount'] += entry.total_amount

                # Группировка по датам (последние 30 дней)
                daily_stats = {}
                for entry in entries:
                    entry_date = entry.work_date
                    if entry_date not in daily_stats:
                        daily_stats[entry_date] = {
                            'entries': 0,
                            'hours': 0.0,
                            'amount': 0.0
                        }
                    daily_stats[entry_date]['entries'] += 1
                    daily_stats[entry_date]['hours'] += entry.hours
                    daily_stats[entry_date]['amount'] += entry.total_amount

                return {
                    'project': {
                        'id': project.id,
                        'name': project.name,
                        'address': project.address,
                        'status': 'Активный' if not project.is_deleted else 'Удален'
                    },
                    'period': {
                        'start_date': start_date,
                        'end_date': end_date,
                        'days': (end_date - start_date).days + 1
                    },
                    'summary': {
                        'total_entries': total_entries,
                        'total_hours': round(total_hours, 2),
                        'total_amount': round(total_amount, 2),
                        'workers_count': len(workers_stats),
                        'work_types_count': len(work_types_stats),
                        'avg_hours_per_day': round(total_hours / len(daily_stats), 2) if daily_stats else 0
                    },
                    'workers': workers_stats,
                    'work_types': work_types_stats,
                    'daily_stats': daily_stats,
                    'entries': [
                        {
                            'id': entry.id,
                            'date': entry.work_date,
                            'worker': entry.user.display_name if entry.user else 'Неизвестно',
                            'work_type': entry.work_type.name if entry.work_type else 'Неизвестно',
                            'hours': entry.hours,
                            'rate': entry.hourly_rate,
                            'amount': entry.total_amount,
                            'description': entry.description
                        }
                        for entry in entries
                    ]
                }

            except Exception as e:
                logger.error(f"Ошибка при генерации отчета по проекту {project_id}: {e}")
                return {'error': f'Ошибка при генерации отчета: {str(e)}'}

    @staticmethod
    async def get_company_summary(company_id: int) -> Dict[str, Any]:
        """
        Получение общей сводки по компании

        Args:
            company_id: ID компании

        Returns:
            Словарь с общей статистикой компании
        """
        async with async_session() as session:
            try:
                # Общая статистика записей
                entries_query = select(func.count(WorkEntry.id), func.sum(WorkEntry.hours),
                                     func.sum(WorkEntry.total_amount)).where(
                    and_(WorkEntry.company_id == company_id, WorkEntry.is_deleted == False)
                )
                entries_result = await session.execute(entries_query)
                total_entries, total_hours, total_amount = entries_result.first()

                # Количество активных рабочих
                workers_query = select(func.count(UserCompanyRole.user_id.distinct())).where(
                    and_(UserCompanyRole.company_id == company_id, UserCompanyRole.role == 'worker')
                )
                workers_result = await session.execute(workers_query)
                workers_count = workers_result.scalar()

                # Количество активных проектов
                projects_query = select(func.count(Project.id)).where(
                    and_(Project.company_id == company_id, Project.is_deleted == False)
                )
                projects_result = await session.execute(projects_query)
                projects_count = projects_result.scalar()

                # Количество типов работ
                work_types_query = select(func.count(WorkType.id)).where(
                    and_(WorkType.company_id == company_id, WorkType.is_deleted == False)
                )
                work_types_result = await session.execute(work_types_query)
                work_types_count = work_types_result.scalar()

                return {
                    'summary': {
                        'total_entries': total_entries or 0,
                        'total_hours': round(float(total_hours or 0), 2),
                        'total_amount': round(float(total_amount or 0), 2),
                        'workers_count': workers_count or 0,
                        'projects_count': projects_count or 0,
                        'work_types_count': work_types_count or 0
                    }
                }

            except Exception as e:
                logger.error(f"Ошибка при получении сводки компании {company_id}: {e}")
                return {
                    'summary': {
                        'total_entries': 0, 'total_hours': 0.0, 'total_amount': 0.0,
                        'workers_count': 0, 'projects_count': 0, 'work_types_count': 0
                    }
                }
