"""
Скрипт для запуска тестов RBAC системы
"""
import subprocess
import sys
import os

def run_tests():
    """Запуск всех тестов"""
    print("🧪 Запуск тестов RBAC системы...")
    print("=" * 50)
    
    # Проверяем наличие pytest
    try:
        import pytest
    except ImportError:
        print("❌ pytest не установлен. Установите: pip install pytest pytest-asyncio")
        return False
    
    # Запускаем тесты
    test_files = [
        "tests/test_auth_service.py",
        "tests/test_rbac_middleware.py", 
        "tests/test_token_handlers.py"
    ]
    
    all_passed = True
    
    for test_file in test_files:
        if os.path.exists(test_file):
            print(f"\n📋 Запуск {test_file}...")
            result = subprocess.run([
                sys.executable, "-m", "pytest", 
                test_file, 
                "-v",
                "--tb=short"
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✅ {test_file} - ВСЕ ТЕСТЫ ПРОШЛИ")
            else:
                print(f"❌ {test_file} - ЕСТЬ ОШИБКИ:")
                print(result.stdout)
                print(result.stderr)
                all_passed = False
        else:
            print(f"⚠️  Файл {test_file} не найден")
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 ВСЕ ТЕСТЫ УСПЕШНО ПРОШЛИ!")
        print("\n📊 Покрытие тестами:")
        print("✅ AuthService - права ролей")
        print("✅ RBACMiddleware - передача прав")
        print("✅ @require_permission - проверка прав")
        print("✅ Обработчики токенов - создание токенов")
        print("✅ Интеграция - полный цикл проверки прав")
    else:
        print("❌ НЕКОТОРЫЕ ТЕСТЫ НЕ ПРОШЛИ")
        return False
    
    return True

def install_test_dependencies():
    """Установка зависимостей для тестов"""
    print("📦 Установка зависимостей для тестов...")
    
    dependencies = ["pytest", "pytest-asyncio"]
    
    for dep in dependencies:
        print(f"Установка {dep}...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", dep
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ {dep} установлен")
        else:
            print(f"❌ Ошибка установки {dep}: {result.stderr}")
            return False
    
    return True

if __name__ == "__main__":
    print("🚀 Система тестирования RBAC")
    print("=" * 50)
    
    # Проверяем аргументы командной строки
    if len(sys.argv) > 1 and sys.argv[1] == "--install":
        if install_test_dependencies():
            print("\n✅ Зависимости установлены. Теперь запустите: python run_tests.py")
        else:
            print("\n❌ Ошибка установки зависимостей")
        sys.exit(0)
    
    # Запускаем тесты
    success = run_tests()
    
    if not success:
        print("\n💡 Для установки зависимостей запустите: python run_tests.py --install")
        sys.exit(1)
    
    print("\n🎯 Рекомендации:")
    print("1. Запускайте тесты после каждого изменения в RBAC системе")
    print("2. Добавляйте новые тесты при добавлении новых прав или ролей")
    print("3. Проверяйте покрытие тестами новых обработчиков")
