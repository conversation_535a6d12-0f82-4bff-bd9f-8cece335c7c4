"""
WorkTypeService для управления типами работ

Предоставляет CRUD операции для типов работ с валидацией и проверками.
"""
import logging
from typing import Dict, List, Optional, Any
from sqlalchemy import select, and_, or_, func
from sqlalchemy.exc import IntegrityError

from db.database import async_session
from db.models import WorkType, Company

logger = logging.getLogger(__name__)


class WorkTypeService:
    """Сервис для управления типами работ"""

    @staticmethod
    async def create_work_type(
        company_id: int,
        name: str,
        unit: str,
        rate: float,
        created_by: int
    ) -> Optional[Dict[str, Any]]:
        """
        Создание нового типа работы
        
        Args:
            company_id: ID компании
            name: Название типа работы
            unit: Единица измерения
            rate: Ставка
            created_by: ID пользователя-создателя
            
        Returns:
            Данные созданного типа работы или None при ошибке
        """
        async with async_session() as session:
            try:
                # Проверяем уникальность названия в компании
                # Пока что пропускаем проверку, так как company_id может быть None
                # existing_query = select(WorkType).where(
                #     and_(
                #         WorkType.company_id == company_id,
                #         WorkType.name == name,
                #         WorkType.is_deleted == False
                #     )
                # )
                # existing_result = await session.execute(existing_query)
                # if existing_result.scalar_one_or_none():
                #     logger.warning(f"Тип работы '{name}' уже существует в компании {company_id}")
                #     return None

                # Создаем новый тип работы с правильными полями
                work_type = WorkType(
                    name=name,
                    unit=unit,
                    rate_type='hourly',  # Устанавливаем тип ставки
                    value=rate,
                    hourly_rate=rate,
                    project_id=1,  # Временно используем проект 1
                    company_id=company_id,
                    rate=rate,
                    is_deleted=False
                )
                
                session.add(work_type)
                await session.commit()
                await session.refresh(work_type)

                logger.info(f"Создан тип работы: {name} (ID: {work_type.work_type_id})")
                return {
                    'id': work_type.work_type_id,
                    'work_type_id': work_type.work_type_id,
                    'name': work_type.name,
                    'unit': work_type.unit,
                    'rate': work_type.rate or work_type.hourly_rate or work_type.value,
                    'company_id': work_type.company_id,
                    'is_deleted': work_type.is_deleted or False,
                    'created_at': work_type.created_at,
                    'updated_at': work_type.updated_at
                }
                
            except IntegrityError as e:
                await session.rollback()
                logger.error(f"Ошибка целостности при создании типа работы: {e}")
                return None
            except Exception as e:
                await session.rollback()
                logger.error(f"Ошибка при создании типа работы: {e}")
                return None

    @staticmethod
    async def get_company_work_types(
        company_id: int,
        include_deleted: bool = False
    ) -> List[Dict[str, Any]]:
        """
        Получение типов работ компании
        
        Args:
            company_id: ID компании
            include_deleted: Включать удаленные типы работ
            
        Returns:
            Список типов работ
        """
        async with async_session() as session:
            try:
                query = select(WorkType).where(WorkType.company_id == company_id)
                
                if not include_deleted:
                    query = query.where(WorkType.is_deleted == False)
                
                query = query.order_by(WorkType.name)
                
                result = await session.execute(query)
                work_types = result.scalars().all()
                
                return [
                    {
                        'id': wt.work_type_id,  # Используем правильное поле
                        'work_type_id': wt.work_type_id,
                        'name': wt.name,
                        'unit': wt.unit,
                        'rate': wt.rate or wt.hourly_rate or wt.value or 0.0,
                        'company_id': wt.company_id,
                        'project_id': wt.project_id,
                        'is_deleted': wt.is_deleted or False,
                        'created_at': wt.created_at,
                        'updated_at': wt.updated_at
                    }
                    for wt in work_types
                ]
                
            except Exception as e:
                logger.error(f"Ошибка при получении типов работ компании {company_id}: {e}")
                return []

    @staticmethod
    async def get_work_type_by_id(work_type_id: int) -> Optional[Dict[str, Any]]:
        """
        Получение типа работы по ID

        Args:
            work_type_id: ID типа работы

        Returns:
            Данные типа работы или None
        """
        async with async_session() as session:
            try:
                query = select(WorkType).where(WorkType.work_type_id == work_type_id)
                result = await session.execute(query)
                work_type = result.scalar_one_or_none()

                if not work_type:
                    return None

                return {
                    'id': work_type.work_type_id,
                    'work_type_id': work_type.work_type_id,
                    'name': work_type.name,
                    'unit': work_type.unit,
                    'rate': work_type.rate or work_type.hourly_rate or work_type.value or 0.0,
                    'company_id': work_type.company_id,
                    'project_id': work_type.project_id,
                    'is_deleted': work_type.is_deleted or False,
                    'created_at': work_type.created_at,
                    'updated_at': work_type.updated_at
                }

            except Exception as e:
                logger.error(f"Ошибка при получении типа работы {work_type_id}: {e}")
                return None

    @staticmethod
    async def update_work_type(
        work_type_id: int,
        name: Optional[str] = None,
        unit: Optional[str] = None,
        rate: Optional[float] = None
    ) -> bool:
        """
        Обновление типа работы
        
        Args:
            work_type_id: ID типа работы
            name: Новое название (опционально)
            unit: Новая единица измерения (опционально)
            rate: Новая ставка (опционально)
            
        Returns:
            True при успехе, False при ошибке
        """
        async with async_session() as session:
            try:
                query = select(WorkType).where(WorkType.work_type_id == work_type_id)
                result = await session.execute(query)
                work_type = result.scalar_one_or_none()

                if not work_type:
                    logger.warning(f"Тип работы {work_type_id} не найден")
                    return False

                # Проверяем уникальность нового названия (пропускаем пока)
                # if name and name != work_type.name:
                #     existing_query = select(WorkType).where(
                #         and_(
                #             WorkType.company_id == work_type.company_id,
                #             WorkType.name == name,
                #             WorkType.work_type_id != work_type_id,
                #             WorkType.is_deleted == False
                #         )
                #     )
                #     existing_result = await session.execute(existing_query)
                #     if existing_result.scalar_one_or_none():
                #         logger.warning(f"Тип работы '{name}' уже существует в компании")
                #         return False

                # Обновляем поля
                if name is not None:
                    work_type.name = name
                if unit is not None:
                    work_type.unit = unit
                if rate is not None:
                    work_type.rate = rate
                    work_type.hourly_rate = rate  # Обновляем и основное поле
                    work_type.value = rate
                
                await session.commit()
                logger.info(f"Обновлен тип работы {work_type_id}")
                return True
                
            except IntegrityError as e:
                await session.rollback()
                logger.error(f"Ошибка целостности при обновлении типа работы: {e}")
                return False
            except Exception as e:
                await session.rollback()
                logger.error(f"Ошибка при обновлении типа работы {work_type_id}: {e}")
                return False

    @staticmethod
    async def delete_work_type(work_type_id: int, soft_delete: bool = True) -> bool:
        """
        Удаление типа работы
        
        Args:
            work_type_id: ID типа работы
            soft_delete: Мягкое удаление (по умолчанию True)
            
        Returns:
            True при успехе, False при ошибке
        """
        async with async_session() as session:
            try:
                query = select(WorkType).where(WorkType.work_type_id == work_type_id)
                result = await session.execute(query)
                work_type = result.scalar_one_or_none()
                
                if not work_type:
                    logger.warning(f"Тип работы {work_type_id} не найден")
                    return False
                
                if soft_delete:
                    # Мягкое удаление
                    work_type.is_deleted = True
                    await session.commit()
                    logger.info(f"Тип работы {work_type_id} помечен как удаленный")
                else:
                    # Жесткое удаление
                    await session.delete(work_type)
                    await session.commit()
                    logger.info(f"Тип работы {work_type_id} удален из базы данных")
                
                return True
                
            except Exception as e:
                await session.rollback()
                logger.error(f"Ошибка при удалении типа работы {work_type_id}: {e}")
                return False

    @staticmethod
    async def validate_work_type_data(
        name: str,
        unit: str,
        rate: float
    ) -> Dict[str, Any]:
        """
        Валидация данных типа работы
        
        Args:
            name: Название типа работы
            unit: Единица измерения
            rate: Ставка
            
        Returns:
            Словарь с результатом валидации
        """
        errors = []
        
        # Валидация названия
        if not name or not name.strip():
            errors.append("Название не может быть пустым")
        elif len(name.strip()) < 3:
            errors.append("Название должно содержать минимум 3 символа")
        elif len(name.strip()) > 100:
            errors.append("Название не должно превышать 100 символов")
        
        # Валидация единицы измерения
        if not unit or not unit.strip():
            errors.append("Единица измерения не может быть пустой")
        elif len(unit.strip()) > 20:
            errors.append("Единица измерения не должна превышать 20 символов")
        
        # Валидация ставки
        try:
            rate_float = float(rate)
            if rate_float <= 0:
                errors.append("Ставка должна быть больше нуля")
            elif rate_float > 10000:
                errors.append("Ставка не должна превышать 10000")
        except (ValueError, TypeError):
            errors.append("Ставка должна быть числом")
        
        return {
            'is_valid': len(errors) == 0,
            'errors': errors,
            'cleaned_data': {
                'name': name.strip() if name else '',
                'unit': unit.strip() if unit else '',
                'rate': float(rate) if rate else 0.0
            }
        }

    @staticmethod
    async def get_work_types_statistics(company_id: int) -> Dict[str, Any]:
        """
        Получение статистики по типам работ

        Args:
            company_id: ID компании

        Returns:
            Словарь со статистикой
        """
        async with async_session() as session:
            try:
                # Общее количество типов работ
                total_query = select(func.count(WorkType.work_type_id)).where(
                    and_(WorkType.company_id == company_id,
                         or_(WorkType.is_deleted == False, WorkType.is_deleted.is_(None)))
                )
                total_result = await session.execute(total_query)
                total_count = total_result.scalar()

                # Количество удаленных
                deleted_query = select(func.count(WorkType.work_type_id)).where(
                    and_(WorkType.company_id == company_id, WorkType.is_deleted == True)
                )
                deleted_result = await session.execute(deleted_query)
                deleted_count = deleted_result.scalar()

                # Средняя ставка (используем COALESCE для разных полей ставки)
                avg_rate_query = select(func.avg(
                    func.coalesce(WorkType.rate, WorkType.hourly_rate, WorkType.value, 0)
                )).where(
                    and_(WorkType.company_id == company_id,
                         or_(WorkType.is_deleted == False, WorkType.is_deleted.is_(None)))
                )
                avg_rate_result = await session.execute(avg_rate_query)
                avg_rate = avg_rate_result.scalar()

                return {
                    'total_count': total_count or 0,
                    'active_count': total_count or 0,
                    'deleted_count': deleted_count or 0,
                    'average_rate': round(float(avg_rate or 0), 2)
                }

            except Exception as e:
                logger.error(f"Ошибка при получении статистики типов работ: {e}")
                return {
                    'total_count': 0,
                    'active_count': 0,
                    'deleted_count': 0,
                    'average_rate': 0.0
                }
