"""
Утилиты для работы с dev-сессиями суперпользователя.

Управление виртуальными правами и фиктивными данными для тестирования.
"""
import os
import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

# Константы для фиктивных данных
MOCK_COMPANY_ID = 999
MOCK_PROJECT_ID = 9999
MOCK_COMPANY_NAME = "Test OY"
MOCK_PROJECT_NAME = "Test Project"

# Виртуальные права доступа для ролей
MOCK_PERMISSIONS = {
    "worker": {
        "can_add_work": True,
        "can_edit_own_work": True,
        "can_view_own_work": True,
        "can_export_own_data": True,
        "can_view_project_info": True
    },
    "director": {
        "can_manage_workers": True,
        "can_manage_projects": True,
        "can_manage_work_types": True,
        "can_view_company_reports": True,
        "can_export_company_data": True,
        "can_generate_worker_tokens": True,
        "can_edit_company_settings": True,
        # Дополнительные права для совместимости с декораторами
        "can_manage_companies": True,  # Для director_companies
        "can_view_reports": True,      # Для director_reports
        "can_export_data": True        # Для director_export
    },
    "admin": {
        "can_manage_companies": True,
        "can_manage_all_users": True,
        "can_view_all_data": True,
        "can_generate_tokens": True,
        "can_delete_companies": True,
        "can_system_settings": True
    }
}


def get_superuser_id() -> int:
    """Получает ID суперпользователя из .env"""
    return int(os.getenv("ADMIN_ID", "0"))


def is_superuser(user_id: int) -> bool:
    """Проверяет, является ли пользователь суперпользователем"""
    return user_id == get_superuser_id()


def get_mock_permissions(role: str) -> Dict[str, bool]:
    """
    Возвращает виртуальные права для указанной роли
    
    Args:
        role: Роль (worker, director, admin)
        
    Returns:
        Словарь с правами доступа
    """
    return MOCK_PERMISSIONS.get(role, {})


def get_mock_user_data(user_id: int, role: str) -> Dict[str, Any]:
    """
    Создает фиктивные данные пользователя для тестирования
    
    Args:
        user_id: Telegram ID пользователя
        role: Выбранная роль
        
    Returns:
        Фиктивные данные пользователя
    """
    role_names = {
        "worker": "Test Worker",
        "director": "Test Director", 
        "admin": "Test Admin"
    }
    
    return {
        "user_id": user_id,
        "display_name": role_names.get(role, f"Test {role.title()}"),
        "active_company_id": MOCK_COMPANY_ID,
        "active_project_id": MOCK_PROJECT_ID if role == "worker" else None,
        "created_at": None,
        "updated_at": None
    }


def get_mock_company_data() -> Dict[str, Any]:
    """
    Возвращает данные фиктивной компании
    
    Returns:
        Данные тестовой компании
    """
    return {
        "id": MOCK_COMPANY_ID,
        "name": MOCK_COMPANY_NAME,
        "business_id": "TEST-999999-9",
        "address": "Test Address, Helsinki, Finland",
        "is_deleted": False,
        "created_at": None,
        "updated_at": None
    }


def get_mock_project_data() -> Dict[str, Any]:
    """
    Возвращает данные фиктивного проекта
    
    Returns:
        Данные тестового проекта
    """
    return {
        "id": MOCK_PROJECT_ID,
        "company_id": MOCK_COMPANY_ID,
        "name": MOCK_PROJECT_NAME,
        "address": "Test Project Address, Helsinki",
        "is_deleted": False,
        "created_at": None,
        "updated_at": None
    }


def log_dev_action(user_id: int, action: str, role: str) -> None:
    """
    Логирует действия суперпользователя в dev-режиме
    
    Args:
        user_id: ID суперпользователя
        action: Выполненное действие
        role: Текущая роль
    """
    logger.info(f"🧪 DEV MODE | User: {user_id} | Role: {role} | Action: {action}")


def validate_dev_role(role: str) -> bool:
    """
    Проверяет валидность роли для dev-режима
    
    Args:
        role: Роль для проверки
        
    Returns:
        True если роль валидна
    """
    return role in ["worker", "director", "admin"]


def get_role_display_name(role: str) -> str:
    """
    Возвращает отображаемое название роли
    
    Args:
        role: Роль
        
    Returns:
        Отображаемое название
    """
    role_names = {
        "worker": "🧱 Рабочий",
        "director": "📋 Директор",
        "admin": "👑 Администратор"
    }
    return role_names.get(role, role)


def get_role_description(role: str) -> str:
    """
    Возвращает описание возможностей роли
    
    Args:
        role: Роль
        
    Returns:
        Описание роли
    """
    descriptions = {
        "worker": "Добавление записей о работе, просмотр своих данных, экспорт",
        "director": "Управление рабочими, проектами, отчеты, экспорт данных компании",
        "admin": "Полный доступ к системе, управление компаниями и пользователями"
    }
    return descriptions.get(role, "Неизвестная роль")


class DevSession:
    """Класс для управления dev-сессией суперпользователя"""
    
    def __init__(self, user_id: int):
        self.user_id = user_id
        self.is_superuser = is_superuser(user_id)
    
    def get_permissions(self, role: str) -> Dict[str, bool]:
        """Получить права для роли"""
        if not self.is_superuser:
            return {}
        return get_mock_permissions(role)
    
    def get_user_data(self, role: str) -> Dict[str, Any]:
        """Получить данные пользователя для роли"""
        if not self.is_superuser:
            return {}
        return get_mock_user_data(self.user_id, role)
    
    def log_action(self, action: str, role: str) -> None:
        """Залогировать действие"""
        if self.is_superuser:
            log_dev_action(self.user_id, action, role)
