"""
Обработчики управления проектами (только Reply-клавиатуры)

Создание проектов, управление типами работ.
"""
from aiogram import Router, types, F
from aiogram.fsm.context import FSMContext
from states import NewProjectStates
from services.project_service import ProjectService
from services.work_type_service import WorkTypeService
from middleware.rbac_middleware import require_permission
from keyboards.worker import create_projects_keyboard
from keyboards.common import create_back_keyboard

router = Router()


# ===== ФУНКЦИИ ДЛЯ REPLY-КЛАВИАТУР =====

async def show_projects_menu(message: types.Message):
    """Показать меню проектов"""
    keyboard = create_projects_keyboard()

    await message.answer(
        "🏗️ <b>Управление проектами</b>\n\n"
        "Выберите действие:",
        reply_markup=keyboard,
        parse_mode="HTML"
    )


# ===== ОБРАБОТЧИКИ REPLY-КНОПОК =====

@router.message(F.text == "➕ Создать проект")
async def handle_create_project_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Создать проект'"""
    await message.answer(
        "➕ <b>Создание проекта</b>\n\n"
        "🚧 Функция создания проекта в разработке\n\n"
        "Будет реализована возможность:\n"
        "• Создания новых проектов\n"
        "• Указания адреса проекта\n"
        "• Добавления описания\n"
        "• Назначения ответственных"
    )


@router.message(F.text == "📋 Список проектов")
async def handle_projects_list_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Список проектов'"""
    keyboard = create_projects_keyboard()
    await message.answer(
        "📋 <b>Список проектов</b>\n\n"
        "🏗️ <b>Активные проекты:</b>\n\n"
        "Проекты не найдены.\n\n"
        "💡 Создайте первый проект для начала работы.\n\n"
        "🚧 Функция в разработке",
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.message(F.text == "🛠️ Типы работ")
async def handle_work_types_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Типы работ'"""
    keyboard = create_projects_keyboard()
    await message.answer(
        "🛠️ <b>Типы работ</b>\n\n"
        "📝 <b>Доступные типы работ:</b>\n\n"
        "Типы работ не найдены.\n\n"
        "💡 Типы работ создаются в рамках проектов.\n"
        "Сначала создайте проект.\n\n"
        "🚧 Функция в разработке",
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.message(F.text == "📊 Статистика проектов")
async def handle_project_statistics_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Статистика проектов'"""
    keyboard = create_projects_keyboard()
    await message.answer(
        "📊 <b>Статистика проектов</b>\n\n"
        "📈 <b>Общая статистика:</b>\n"
        "• Всего проектов: 0\n"
        "• Активных проектов: 0\n"
        "• Завершённых проектов: 0\n"
        "• Типов работ: 0\n\n"
        "⏰ <b>По времени:</b>\n"
        "• Общее время: 0 часов\n"
        "• Среднее время на проект: 0 часов\n"
        "• Время за месяц: 0 часов\n\n"
        "🚧 Функция в разработке",
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.message(F.text == "🔍 Поиск проектов")
async def handle_search_projects_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Поиск проектов'"""
    keyboard = create_projects_keyboard()
    await message.answer(
        "🔍 <b>Поиск проектов</b>\n\n"
        "🔎 <b>Возможности поиска:</b>\n"
        "• По названию проекта\n"
        "• По адресу\n"
        "• По статусу\n"
        "• По дате создания\n"
        "• По ответственному\n\n"
        "📝 <b>Фильтры:</b>\n"
        "• Активные проекты\n"
        "• Завершённые проекты\n"
        "• Архивные проекты\n"
        "• Проекты с типами работ\n\n"
        "🚧 Функция в разработке",
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.message(F.text == "📁 Архив проектов")
async def handle_project_archive_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Архив проектов'"""
    keyboard = create_projects_keyboard()
    await message.answer(
        "📁 <b>Архив проектов</b>\n\n"
        "🗂️ <b>Архивные проекты:</b>\n\n"
        "Архивных проектов нет.\n\n"
        "💡 Завершённые проекты автоматически\n"
        "перемещаются в архив через 30 дней.\n\n"
        "🔧 <b>Возможности архива:</b>\n"
        "• Просмотр завершённых проектов\n"
        "• Восстановление проектов\n"
        "• Экспорт данных\n"
        "• Удаление старых проектов\n\n"
        "🚧 Функция в разработке",
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.message(F.text == "⚙️ Настройки проектов")
async def handle_project_settings_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Настройки проектов'"""
    keyboard = create_projects_keyboard()
    await message.answer(
        "⚙️ <b>Настройки проектов</b>\n\n"
        "🔧 <b>Общие настройки:</b>\n"
        "• Автоархивирование: 30 дней\n"
        "• Уведомления: Включены\n"
        "• Резервное копирование: Ежедневно\n"
        "• Права доступа: По ролям\n\n"
        "📊 <b>Настройки отчётов:</b>\n"
        "• Формат по умолчанию: Excel\n"
        "• Детализация: Полная\n"
        "• Период: Последний месяц\n\n"
        "🔐 <b>Безопасность:</b>\n"
        "• Логирование действий: Включено\n"
        "• Контроль доступа: Активен\n\n"
        "🚧 Функция в разработке",
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.message(F.text == "📋 Шаблоны проектов")
async def handle_project_templates_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Шаблоны проектов'"""
    keyboard = create_projects_keyboard()
    await message.answer(
        "📋 <b>Шаблоны проектов</b>\n\n"
        "📝 <b>Доступные шаблоны:</b>\n\n"
        "Шаблоны не созданы.\n\n"
        "💡 Создайте шаблоны для быстрого\n"
        "создания типовых проектов:\n\n"
        "🏗️ <b>Типы шаблонов:</b>\n"
        "• Строительные проекты\n"
        "• Ремонтные работы\n"
        "• Отделочные работы\n"
        "• Инженерные работы\n\n"
        "🔧 <b>Возможности:</b>\n"
        "• Создание шаблонов\n"
        "• Редактирование шаблонов\n"
        "• Копирование шаблонов\n"
        "• Импорт/экспорт шаблонов\n\n"
        "🚧 Функция в разработке",
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.message(F.text == "📊 Отчёты по проектам")
async def handle_project_reports_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Отчёты по проектам'"""
    keyboard = create_projects_keyboard()
    await message.answer(
        "📊 <b>Отчёты по проектам</b>\n\n"
        "📈 <b>Доступные отчёты:</b>\n\n"
        "🔧 <b>По проектам:</b>\n"
        "• Сводный отчёт по всем проектам\n"
        "• Детальный отчёт по проекту\n"
        "• Отчёт по времени выполнения\n"
        "• Отчёт по затратам\n\n"
        "👷 <b>По работникам:</b>\n"
        "• Время работы по проектам\n"
        "• Эффективность работников\n"
        "• Распределение нагрузки\n\n"
        "📅 <b>По периодам:</b>\n"
        "• Ежедневные отчёты\n"
        "• Еженедельные отчёты\n"
        "• Ежемесячные отчёты\n"
        "• Годовые отчёты\n\n"
        "🚧 Функция в разработке",
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.message(F.text == "🔄 Импорт проектов")
async def handle_import_projects_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Импорт проектов'"""
    keyboard = create_projects_keyboard()
    await message.answer(
        "🔄 <b>Импорт проектов</b>\n\n"
        "📥 <b>Поддерживаемые форматы:</b>\n"
        "• Excel (.xlsx)\n"
        "• CSV (.csv)\n"
        "• JSON (.json)\n"
        "• XML (.xml)\n\n"
        "📋 <b>Импортируемые данные:</b>\n"
        "• Информация о проектах\n"
        "• Типы работ\n"
        "• Ставки и расценки\n"
        "• Адреса и описания\n\n"
        "⚙️ <b>Настройки импорта:</b>\n"
        "• Проверка дубликатов\n"
        "• Валидация данных\n"
        "• Автоматическое сопоставление\n"
        "• Резервное копирование\n\n"
        "🚧 Функция в разработке",
        reply_markup=keyboard,
        parse_mode="HTML"
    )
