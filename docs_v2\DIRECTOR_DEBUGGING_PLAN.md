# 🔧 План отладки меню директора

**Дата**: 29.06.2025  
**Статус**: 🔄 ГОТОВ К ВЫПОЛНЕНИЮ  
**Приоритет**: ВЫСОКИЙ

## 🎯 Текущее состояние

### ✅ Что работает
- **Система виртуальных прав** — полностью функциональна
- **Главное меню директора** — отображается корректно
- **Переходы между меню** — кнопки "Назад" работают
- **Раздел "📊 Просмотр отчётов"** — базовая навигация работает

### ❌ Что требует отладки
- **🏢 Управление компаниями** — кнопки не отвечают
- **👷 Управление рабочими** — кнопки не отвечают  
- **📁 Экспорт / Импорт данных** — кнопки не отвечают
- **🛠️ Редактирование типов работ** — кнопки не отвечают
- **ℹ️ Информация** — кнопки не отвечают

## 🔍 План диагностики

### Этап 1: Анализ логов
1. **Запустить бот с логированием**
2. **Протестировать каждую кнопку директора**
3. **Проанализировать сообщения "is not handled"**
4. **Выявить отсутствующие обработчики**

### Этап 2: Проверка обработчиков
1. **Проверить наличие всех callback_query обработчиков**
2. **Сопоставить callback_data с обработчиками**
3. **Проверить декораторы @require_permission**
4. **Убедиться в корректности прав в AuthService**

### Этап 3: Проверка прав доступа
1. **Проверить VIRTUAL_PERMISSIONS в dev_session.py**
2. **Сопоставить с ROLE_PERMISSIONS в auth_service.py**
3. **Убедиться в синхронизации прав**
4. **Проверить middleware rbac_middleware.py**

## 🛠️ Конкретные задачи отладки

### 1. Управление компаниями (director_companies)
**Файл**: `handlers/director.py`
**Проблема**: Кнопки не отвечают
**Проверить**:
- [ ] Обработчик `director_companies` существует
- [ ] Права `can_manage_companies` в виртуальных правах
- [ ] Callback_data в кнопках совпадают
- [ ] Нет конфликтов в роутинге

### 2. Управление рабочими (director_workers)  
**Файл**: `handlers/director.py`
**Проблема**: Кнопки не отвечают
**Проверить**:
- [ ] Обработчик `director_workers` существует
- [ ] Права `can_manage_workers` в виртуальных правах
- [ ] Подменю рабочих корректно
- [ ] Обработчики для всех действий с рабочими

### 3. Экспорт/Импорт (director_export)
**Файл**: `handlers/director.py`  
**Проблема**: Кнопки не отвечают
**Проверить**:
- [ ] Обработчик `director_export` существует
- [ ] Права `can_export_data` в виртуальных правах
- [ ] Интеграция с examples/export.py
- [ ] Адаптация под aiogram v3.x

### 4. Типы работ (director_work_types)
**Файл**: `handlers/director.py`
**Проблема**: Кнопки не отвечают  
**Проверить**:
- [ ] Обработчик `director_work_types` существует
- [ ] Права `can_manage_work_types` в виртуальных правах
- [ ] Связь с WorkTypeService
- [ ] CRUD операции для типов работ

### 5. Информация (director_info)
**Файл**: `handlers/director.py`
**Проблема**: Кнопки не отвечают
**Проверить**:
- [ ] Обработчик `director_info` существует
- [ ] Отображение информации о компании
- [ ] Использование фиктивных данных в dev-режиме

## 🔧 Методология отладки

### 1. Пошаговое тестирование
```bash
# Запуск с детальным логированием
python bot.py

# Тестирование каждой кнопки:
# 1. /start → Директор
# 2. Нажать каждую кнопку главного меню
# 3. Записать результат (работает/не работает)
# 4. Проанализировать логи
```

### 2. Проверка кода
```python
# Поиск всех обработчиков director_*
grep -r "director_" handlers/

# Проверка прав в dev_session.py
grep -A 20 "director" utils/dev_session.py

# Проверка middleware
grep -A 10 "virtual" middleware/rbac_middleware.py
```

### 3. Исправление проблем
1. **Добавить отсутствующие обработчики**
2. **Синхронизировать права**
3. **Исправить callback_data**
4. **Протестировать каждую функцию**

## 📋 Чек-лист готовности

### Перед началом отладки:
- [ ] Бот запущен и работает
- [ ] Система виртуальных прав активна
- [ ] Логирование включено
- [ ] Доступ к меню директора есть

### После отладки:
- [ ] Все кнопки главного меню работают
- [ ] Подменю открываются корректно
- [ ] Кнопки "Назад" функциональны
- [ ] Права доступа корректны
- [ ] Фиктивные данные используются правильно

## 🎯 Ожидаемый результат

**После завершения отладки:**
- ✅ **Все функции директора работают** корректно
- ✅ **Система виртуальных прав** полностью функциональна
- ✅ **Готовность к CHECKPOINT 7** (Команды рабочего)

**Время выполнения**: 2-3 часа активной работы
