"""
Тесты для AuthService
"""
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from services.auth_service import AuthService


class TestAuthServicePermissions:
    """Тесты системы прав доступа AuthService"""
    
    def test_role_permissions_structure(self):
        """Тест структуры прав ролей"""
        permissions = AuthService.ROLE_PERMISSIONS
        
        # Проверяем что все роли определены
        assert "admin" in permissions
        assert "director" in permissions
        assert "worker" in permissions
        
        # Проверяем что все права - булевы значения
        for role, role_permissions in permissions.items():
            for permission, value in role_permissions.items():
                assert isinstance(value, bool), f"Permission {permission} for {role} should be boolean"
    
    def test_admin_permissions(self):
        """Тест прав администратора"""
        admin_perms = AuthService.ROLE_PERMISSIONS["admin"]
        
        # Админ должен иметь все ключевые права
        expected_permissions = [
            "can_manage_users",
            "can_manage_companies", 
            "can_view_all_data",
            "can_generate_tokens",
            "can_generate_worker_tokens",  # Добавлено в нашем исправлении
            "can_delete_companies",
            "can_view_statistics"
        ]
        
        for permission in expected_permissions:
            assert admin_perms.get(permission) is True, f"Admin should have {permission}"
    
    def test_director_permissions(self):
        """Тест прав директора"""
        director_perms = AuthService.ROLE_PERMISSIONS["director"]
        
        # Директор должен иметь права управления компанией
        expected_permissions = [
            "can_manage_workers",
            "can_manage_projects",
            "can_manage_work_types",
            "can_view_company_reports",
            "can_export_company_data",
            "can_generate_worker_tokens",
            "can_edit_company_settings"
        ]
        
        for permission in expected_permissions:
            assert director_perms.get(permission) is True, f"Director should have {permission}"
        
        # Директор НЕ должен иметь права создания токенов директоров
        assert "can_generate_tokens" not in director_perms
    
    def test_worker_permissions(self):
        """Тест прав рабочего"""
        worker_perms = AuthService.ROLE_PERMISSIONS["worker"]
        
        # Рабочий должен иметь права работы с собственными данными
        expected_permissions = [
            "can_add_work",
            "can_edit_own_work",
            "can_delete_own_work",
            "can_view_own_work",
            "can_create_own_reports",
            "can_export_own_data",
            "can_manage_own_projects"
        ]
        
        for permission in expected_permissions:
            assert worker_perms.get(permission) is True, f"Worker should have {permission}"
        
        # Рабочий НЕ должен иметь права создания токенов
        assert "can_generate_tokens" not in worker_perms
        assert "can_generate_worker_tokens" not in worker_perms
    
    def test_permission_hierarchy(self):
        """Тест иерархии прав: админ > директор > рабочий"""
        admin_perms = set(AuthService.ROLE_PERMISSIONS["admin"].keys())
        director_perms = set(AuthService.ROLE_PERMISSIONS["director"].keys())
        worker_perms = set(AuthService.ROLE_PERMISSIONS["worker"].keys())
        
        # Админ должен иметь больше прав чем директор
        assert len(admin_perms) >= len(director_perms)
        
        # Директор должен иметь больше прав чем рабочий
        assert len(director_perms) >= len(worker_perms)
        
        # Проверяем что админ имеет все права директора для создания токенов рабочих
        assert "can_generate_worker_tokens" in admin_perms
        assert "can_generate_worker_tokens" in director_perms


class TestAuthServiceMethods:
    """Тесты методов AuthService"""
    
    @pytest.mark.asyncio
    async def test_get_user_permissions_for_admin(self):
        """Тест получения прав администратора"""
        # Мокаем метод get_user_role для возврата роли админа
        with patch.object(AuthService, 'get_user_role', return_value='admin'):
            permissions = await AuthService.get_user_permissions(*********, 1)

            # Проверяем что получены права админа
            expected_admin_perms = AuthService.ROLE_PERMISSIONS["admin"]
            assert permissions == expected_admin_perms
    
    @pytest.mark.asyncio
    async def test_get_user_permissions_for_director(self):
        """Тест получения прав директора"""
        # Мокаем метод get_user_role для возврата роли директора
        with patch.object(AuthService, 'get_user_role', return_value='director'):
            permissions = await AuthService.get_user_permissions(987654321, 1)

            # Проверяем что получены права директора
            expected_director_perms = AuthService.ROLE_PERMISSIONS["director"]
            assert permissions == expected_director_perms
    
    @pytest.mark.asyncio
    async def test_get_user_permissions_for_worker(self):
        """Тест получения прав рабочего"""
        # Мокаем метод get_user_role для возврата роли рабочего
        with patch.object(AuthService, 'get_user_role', return_value='worker'):
            permissions = await AuthService.get_user_permissions(555666777, 1)

            # Проверяем что получены права рабочего
            expected_worker_perms = AuthService.ROLE_PERMISSIONS["worker"]
            assert permissions == expected_worker_perms
    
    @pytest.mark.asyncio
    async def test_get_user_permissions_for_unregistered_user(self):
        """Тест получения прав незарегистрированного пользователя"""
        with patch.object(AuthService, 'get_user_role', return_value=None):
            permissions = await AuthService.get_user_permissions(*********, 1)

            # Незарегистрированный пользователь не должен иметь прав
            assert permissions == {}
    
    @pytest.mark.asyncio
    async def test_get_user_permissions_wrong_company(self):
        """Тест получения прав пользователя для неправильной компании"""
        # Мокаем пользователя без роли в компании 2
        with patch.object(AuthService, 'get_user_role', return_value=None):
            permissions = await AuthService.get_user_permissions(*********, 2)  # Другая компания

            # Пользователь не должен иметь прав в чужой компании
            assert permissions == {}


class TestTokenPermissionIntegration:
    """Интеграционные тесты прав для создания токенов"""
    
    @pytest.mark.asyncio
    async def test_admin_can_create_all_tokens(self):
        """Тест что админ может создавать все типы токенов"""
        with patch.object(AuthService, 'get_user_role', return_value='admin'):
            permissions = await AuthService.get_user_permissions(*********, 1)

            # Админ должен иметь права на создание всех токенов
            assert permissions.get("can_generate_tokens") is True
            assert permissions.get("can_generate_worker_tokens") is True
    
    @pytest.mark.asyncio
    async def test_director_can_create_worker_tokens_only(self):
        """Тест что директор может создавать только токены рабочих"""
        with patch.object(AuthService, 'get_user_role', return_value='director'):
            permissions = await AuthService.get_user_permissions(987654321, 1)

            # Директор должен иметь права только на создание токенов рабочих
            assert permissions.get("can_generate_worker_tokens") is True
            assert permissions.get("can_generate_tokens") is not True  # None или False
    
    @pytest.mark.asyncio
    async def test_worker_cannot_create_tokens(self):
        """Тест что рабочий не может создавать токены"""
        with patch.object(AuthService, 'get_user_role', return_value='worker'):
            permissions = await AuthService.get_user_permissions(555666777, 1)

            # Рабочий не должен иметь прав на создание токенов
            assert permissions.get("can_generate_tokens") is not True
            assert permissions.get("can_generate_worker_tokens") is not True


if __name__ == "__main__":
    pytest.main([__file__])
