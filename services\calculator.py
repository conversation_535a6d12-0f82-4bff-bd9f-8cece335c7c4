"""
Сервис расчетов для Worklog Bot (адаптировано из examples).

Функции:
- calculate_sum() - расчет суммы за работу
- validate_numeric_input() - валидация числовых значений
- format_currency() - форматирование в евро (Финляндия)
- format_quantity() - форматирование количества

Типы ставок:
- 'fixed' - фиксированная ставка за всю работу
- 'per_unit' - ставка за единицу (количество * ставка)
"""
from typing import Union
from decimal import Decimal, ROUND_HALF_UP


def calculate_sum(work_type, quantity: Union[float, int]) -> float:
    """
    Рассчитывает общую сумму за выполненную работу.
    
    Args:
        work_type: Объект WorkType с полями rate_type и value
        quantity: Количество выполненной работы
        
    Returns:
        Рассчитанная сумма в евро
        
    Raises:
        ValueError: При некорректных входных данных
    """
    if not work_type:
        raise ValueError("Тип работы не может быть None")
    
    if quantity <= 0:
        raise ValueError("Количество должно быть положительным числом")
    
    # Получаем тип ставки и значение
    rate_type = getattr(work_type, 'rate_type', 'per_unit')
    rate_value = float(getattr(work_type, 'value', 0) or getattr(work_type, 'hourly_rate', 0))
    
    if rate_value < 0:
        raise ValueError("Ставка не может быть отрицательной")
    
    # Расчет в зависимости от типа ставки
    if rate_type == 'fixed':
        # Фиксированная ставка - не зависит от количества
        total_sum = rate_value
    elif rate_type == 'per_unit':
        # Ставка за единицу - умножаем на количество
        total_sum = rate_value * float(quantity)
    else:
        # По умолчанию используем ставку за единицу
        total_sum = rate_value * float(quantity)
    
    # Округляем до 2 знаков после запятой
    return round(total_sum, 2)


def validate_numeric_input(value: str, field_name: str = "значение") -> float:
    """
    Валидирует и преобразует строковое значение в число.
    Поддерживает финляндский формат (запятая как разделитель).
    
    Args:
        value: Строковое значение для валидации
        field_name: Название поля для сообщений об ошибках
        
    Returns:
        Преобразованное число
        
    Raises:
        ValueError: При некорректном формате числа
    """
    if not value or not value.strip():
        raise ValueError(f"{field_name} не может быть пустым")
    
    # Заменяем запятую на точку для поддержки финляндского формата
    clean_value = value.replace(",", ".").strip()
    
    try:
        number = float(clean_value)
    except ValueError:
        raise ValueError(f"{field_name} должно быть числом")
    
    if number < 0:
        raise ValueError(f"{field_name} не может быть отрицательным")
    
    return number


def validate_positive_number(value: Union[str, float, int], field_name: str = "значение") -> float:
    """
    Валидирует положительное число.
    
    Args:
        value: Значение для валидации
        field_name: Название поля для сообщений об ошибках
        
    Returns:
        Валидированное число
        
    Raises:
        ValueError: При некорректном значении
    """
    if isinstance(value, str):
        number = validate_numeric_input(value, field_name)
    else:
        number = float(value)
    
    if number <= 0:
        raise ValueError(f"{field_name} должно быть положительным числом")
    
    return number


def format_currency(amount: Union[float, int], currency: str = "€") -> str:
    """
    Форматирует сумму в финляндском валютном формате.
    
    Args:
        amount: Сумма для форматирования
        currency: Валюта (по умолчанию "€")
        
    Returns:
        Отформатированная строка в формате "123,45 €"
    """
    # Округляем до 2 знаков
    rounded_amount = round(float(amount), 2)
    
    # Форматируем с запятой как разделителем (финляндский стандарт)
    formatted = f"{rounded_amount:.2f}".replace(".", ",")
    
    return f"{formatted} {currency}"


def format_quantity(quantity: Union[float, int], unit: str = "") -> str:
    """
    Форматирует количество с единицей измерения.
    
    Args:
        quantity: Количество для форматирования
        unit: Единица измерения
        
    Returns:
        Отформатированная строка
    """
    # Убираем лишние нули после запятой
    if float(quantity).is_integer():
        formatted = str(int(quantity))
    else:
        formatted = f"{quantity:.2f}".rstrip('0').rstrip('.').replace(".", ",")
    
    if unit:
        return f"{formatted} {unit}"
    return formatted


def calculate_work_statistics(entries: list) -> dict:
    """
    Рассчитывает статистику по записям о работе.
    
    Args:
        entries: Список записей о работе
        
    Returns:
        Словарь со статистикой
    """
    if not entries:
        return {
            "total_entries": 0,
            "total_sum": 0.0,
            "total_quantity": 0.0,
            "average_sum_per_entry": 0.0,
            "work_types_count": 0
        }
    
    total_sum = 0.0
    total_quantity = 0.0
    work_types = set()
    
    for entry in entries:
        # Получаем сумму
        entry_sum = getattr(entry, 'calculated_amount', 0) or getattr(entry, 'sum_total', 0) or 0
        total_sum += float(entry_sum)
        
        # Получаем количество
        entry_quantity = getattr(entry, 'quantity', 0) or 0
        total_quantity += float(entry_quantity)
        
        # Получаем тип работы
        work_type = getattr(entry, 'work_type', None)
        if work_type:
            work_type_name = getattr(work_type, 'name', '')
            if work_type_name:
                work_types.add(work_type_name)
    
    total_entries = len(entries)
    average_sum = total_sum / total_entries if total_entries > 0 else 0.0
    
    return {
        "total_entries": total_entries,
        "total_sum": round(total_sum, 2),
        "total_quantity": round(total_quantity, 2),
        "average_sum_per_entry": round(average_sum, 2),
        "work_types_count": len(work_types)
    }


# Константы для валидации
MIN_QUANTITY = 0.01
MAX_QUANTITY = 999999.99
MIN_RATE = 0.01
MAX_RATE = 999999.99

def validate_work_entry_data(quantity: float, rate: float) -> tuple[bool, str]:
    """
    Валидирует данные записи о работе.
    
    Args:
        quantity: Количество работы
        rate: Ставка
        
    Returns:
        Кортеж (валидно, сообщение об ошибке)
    """
    if quantity < MIN_QUANTITY:
        return False, f"Количество должно быть не менее {MIN_QUANTITY}"
    
    if quantity > MAX_QUANTITY:
        return False, f"Количество должно быть не более {MAX_QUANTITY}"
    
    if rate < MIN_RATE:
        return False, f"Ставка должна быть не менее {MIN_RATE}"
    
    if rate > MAX_RATE:
        return False, f"Ставка должна быть не более {MAX_RATE}"
    
    return True, ""


def validate_finnish_decimal(value_str: str) -> float:
    """
    Валидирует финляндский формат числа (запятая как разделитель).
    
    Args:
        value_str: Строка с числом
        
    Returns:
        Число с точкой как разделителем
    """
    return float(value_str.replace(",", "."))
