"""
ImportService для импорта данных из Excel файлов

Предоставляет функции для импорта типов работ и других данных из Excel.
"""
import logging
import tempfile
import os
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path

logger = logging.getLogger(__name__)

try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    logger.warning("pandas не установлен. Excel импорт недоступен.")
    PANDAS_AVAILABLE = False

try:
    from openpyxl import load_workbook
    OPENPYXL_AVAILABLE = True
except ImportError:
    logger.warning("openpyxl не установлен. Excel импорт недоступен.")
    OPENPYXL_AVAILABLE = False

from services.work_type_service import WorkTypeService


class ImportService:
    """Сервис для импорта данных из Excel файлов"""

    # Ожидаемые столбцы для импорта типов работ
    WORK_TYPES_COLUMNS = {
        'name': ['название', 'name', 'тип работы', 'work_type'],
        'unit': ['единица', 'unit', 'ед.изм.', 'единица измерения'],
        'rate': ['ставка', 'rate', 'цена', 'price', 'стоимость']
    }

    @staticmethod
    async def import_work_types_from_excel(
        file_path: str,
        company_id: int,
        created_by: int,
        sheet_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Импорт типов работ из Excel файла
        
        Args:
            file_path: Путь к Excel файлу
            company_id: ID компании
            created_by: ID пользователя-создателя
            sheet_name: Название листа (если None - первый лист)
            
        Returns:
            Результат импорта с статистикой
        """
        if not PANDAS_AVAILABLE:
            return {
                'success': False,
                'error': 'pandas не установлен. Импорт недоступен.',
                'imported': 0,
                'errors': [],
                'skipped': 0
            }
        
        try:
            # Проверяем существование файла
            if not os.path.exists(file_path):
                return {
                    'success': False,
                    'error': 'Файл не найден',
                    'imported': 0,
                    'errors': [],
                    'skipped': 0
                }
            
            # Читаем Excel файл
            try:
                if sheet_name:
                    df = pd.read_excel(file_path, sheet_name=sheet_name)
                else:
                    df = pd.read_excel(file_path)
            except Exception as e:
                return {
                    'success': False,
                    'error': f'Ошибка чтения Excel файла: {str(e)}',
                    'imported': 0,
                    'errors': [],
                    'skipped': 0
                }
            
            # Проверяем, что файл не пустой
            if df.empty:
                return {
                    'success': False,
                    'error': 'Excel файл пустой',
                    'imported': 0,
                    'errors': [],
                    'skipped': 0
                }
            
            # Определяем столбцы
            column_mapping = ImportService._detect_columns(df.columns.tolist())
            
            if not all(column_mapping.values()):
                missing_columns = [k for k, v in column_mapping.items() if not v]
                return {
                    'success': False,
                    'error': f'Не найдены обязательные столбцы: {", ".join(missing_columns)}',
                    'imported': 0,
                    'errors': [],
                    'skipped': 0
                }
            
            # Обрабатываем данные
            imported_count = 0
            skipped_count = 0
            errors = []
            
            for index, row in df.iterrows():
                try:
                    # Извлекаем данные
                    name = str(row[column_mapping['name']]).strip()
                    unit = str(row[column_mapping['unit']]).strip()
                    rate_str = str(row[column_mapping['rate']]).strip()
                    
                    # Пропускаем пустые строки
                    if not name or name.lower() in ['nan', 'none', '']:
                        skipped_count += 1
                        continue
                    
                    # Валидируем и конвертируем ставку
                    try:
                        rate = float(rate_str.replace(',', '.'))
                    except (ValueError, AttributeError):
                        errors.append(f"Строка {index + 2}: Некорректная ставка '{rate_str}'")
                        skipped_count += 1
                        continue
                    
                    # Валидируем данные
                    validation = await WorkTypeService.validate_work_type_data(name, unit, rate)
                    
                    if not validation['is_valid']:
                        errors.append(f"Строка {index + 2}: {'; '.join(validation['errors'])}")
                        skipped_count += 1
                        continue
                    
                    # Создаем тип работы
                    result = await WorkTypeService.create_work_type(
                        company_id=company_id,
                        name=validation['cleaned_data']['name'],
                        unit=validation['cleaned_data']['unit'],
                        rate=validation['cleaned_data']['rate'],
                        created_by=created_by
                    )
                    
                    if result:
                        imported_count += 1
                        logger.info(f"Импортирован тип работы: {name}")
                    else:
                        errors.append(f"Строка {index + 2}: Тип работы '{name}' уже существует")
                        skipped_count += 1
                
                except Exception as e:
                    errors.append(f"Строка {index + 2}: Ошибка обработки - {str(e)}")
                    skipped_count += 1
                    continue
            
            return {
                'success': True,
                'imported': imported_count,
                'skipped': skipped_count,
                'errors': errors,
                'total_rows': len(df)
            }
            
        except Exception as e:
            logger.error(f"Ошибка при импорте типов работ: {e}")
            return {
                'success': False,
                'error': f'Общая ошибка импорта: {str(e)}',
                'imported': 0,
                'errors': [],
                'skipped': 0
            }

    @staticmethod
    def _detect_columns(columns: List[str]) -> Dict[str, Optional[str]]:
        """
        Автоматическое определение столбцов в Excel файле
        
        Args:
            columns: Список названий столбцов
            
        Returns:
            Словарь с сопоставлением полей и столбцов
        """
        column_mapping = {'name': None, 'unit': None, 'rate': None}
        
        # Приводим к нижнему регистру для поиска
        columns_lower = [col.lower().strip() for col in columns]
        
        for field, possible_names in ImportService.WORK_TYPES_COLUMNS.items():
            for possible_name in possible_names:
                if possible_name.lower() in columns_lower:
                    # Находим оригинальное название столбца
                    original_index = columns_lower.index(possible_name.lower())
                    column_mapping[field] = columns[original_index]
                    break
        
        return column_mapping

    @staticmethod
    async def validate_excel_file(file_path: str) -> Dict[str, Any]:
        """
        Валидация Excel файла перед импортом
        
        Args:
            file_path: Путь к файлу
            
        Returns:
            Результат валидации
        """
        if not PANDAS_AVAILABLE:
            return {
                'is_valid': False,
                'error': 'pandas не установлен',
                'details': {}
            }
        
        try:
            # Проверяем существование файла
            if not os.path.exists(file_path):
                return {
                    'is_valid': False,
                    'error': 'Файл не найден',
                    'details': {}
                }
            
            # Проверяем расширение
            file_ext = Path(file_path).suffix.lower()
            if file_ext not in ['.xlsx', '.xls']:
                return {
                    'is_valid': False,
                    'error': 'Неподдерживаемый формат файла. Используйте .xlsx или .xls',
                    'details': {}
                }
            
            # Читаем файл
            df = pd.read_excel(file_path)
            
            if df.empty:
                return {
                    'is_valid': False,
                    'error': 'Файл пустой',
                    'details': {}
                }
            
            # Проверяем столбцы
            column_mapping = ImportService._detect_columns(df.columns.tolist())
            missing_columns = [k for k, v in column_mapping.items() if not v]
            
            if missing_columns:
                return {
                    'is_valid': False,
                    'error': f'Отсутствуют обязательные столбцы: {", ".join(missing_columns)}',
                    'details': {
                        'found_columns': df.columns.tolist(),
                        'missing_columns': missing_columns,
                        'expected_columns': ImportService.WORK_TYPES_COLUMNS
                    }
                }
            
            # Подсчитываем валидные строки
            valid_rows = 0
            for index, row in df.iterrows():
                name = str(row[column_mapping['name']]).strip()
                if name and name.lower() not in ['nan', 'none', '']:
                    valid_rows += 1
            
            return {
                'is_valid': True,
                'details': {
                    'total_rows': len(df),
                    'valid_rows': valid_rows,
                    'columns': df.columns.tolist(),
                    'column_mapping': column_mapping
                }
            }
            
        except Exception as e:
            return {
                'is_valid': False,
                'error': f'Ошибка валидации файла: {str(e)}',
                'details': {}
            }

    @staticmethod
    def get_import_template() -> Dict[str, Any]:
        """
        Получение шаблона для импорта типов работ
        
        Returns:
            Информация о шаблоне
        """
        return {
            'required_columns': [
                {'name': 'Название', 'description': 'Название типа работы', 'example': 'Монтаж'},
                {'name': 'Единица', 'description': 'Единица измерения', 'example': 'час'},
                {'name': 'Ставка', 'description': 'Ставка за единицу', 'example': '25.50'}
            ],
            'optional_columns': [],
            'format': 'Excel (.xlsx или .xls)',
            'notes': [
                'Первая строка должна содержать заголовки столбцов',
                'Названия столбцов могут быть на русском или английском языке',
                'Ставка должна быть числом (можно использовать точку или запятую)',
                'Пустые строки будут пропущены',
                'Дублирующиеся названия типов работ будут пропущены'
            ]
        }

    @staticmethod
    def is_available() -> bool:
        """
        Проверка доступности импорта
        
        Returns:
            True если импорт доступен
        """
        return PANDAS_AVAILABLE
