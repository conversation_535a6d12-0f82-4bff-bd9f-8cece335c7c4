"""
Тесты для ExportService

Проверяет все методы сервиса экспорта данных.
"""
import pytest
import os
import tempfile
from datetime import date
from unittest.mock import patch, mock_open, AsyncMock

from services.export_service import ExportService


class TestExportService:
    """Тесты для ExportService"""

    @pytest.fixture
    def sample_report_data(self):
        """Тестовые данные отчета"""
        return {
            'period': {
                'start_date': date.today(),
                'end_date': date.today(),
                'days': 1
            },
            'summary': {
                'total_entries': 2,
                'total_hours': 14.0,
                'total_amount': 380.0,
                'workers_count': 2,
                'projects_count': 1,
                'work_types_count': 2
            },
            'entries': [
                {
                    'date': date.today(),
                    'worker': '<PERSON><PERSON><PERSON><PERSON>',
                    'project': 'Проект А',
                    'work_type': 'Монтаж',
                    'description': 'Тестовая работа 1',
                    'hours': 8.0,
                    'rate': 25.0,
                    'amount': 200.0
                },
                {
                    'date': date.today(),
                    'worker': 'Петр Петров',
                    'project': 'Проект А',
                    'work_type': 'Демонтаж',
                    'description': 'Тестовая работа 2',
                    'hours': 6.0,
                    'rate': 30.0,
                    'amount': 180.0
                }
            ]
        }

    @pytest.mark.asyncio
    async def test_export_to_excel_success(self, sample_report_data):
        """Тест успешного экспорта в Excel"""
        with patch('services.export_service.EXCEL_AVAILABLE', True):
            with patch('services.export_service.Workbook') as mock_workbook:
                with patch('services.export_service.tempfile.gettempdir') as mock_tempdir:
                    with patch('services.export_service.os.path.join') as mock_join:
                        
                        # Настройка моков
                        mock_tempdir.return_value = '/tmp'
                        mock_join.return_value = '/tmp/test_file.xlsx'
                        
                        mock_wb = AsyncMock()
                        mock_ws = AsyncMock()
                        mock_wb.active = mock_ws
                        mock_workbook.return_value = mock_wb
                        
                        # Мок для ячеек
                        mock_cell = AsyncMock()
                        mock_ws.cell.return_value = mock_cell
                        mock_ws.__getitem__ = AsyncMock(return_value=mock_cell)
                        
                        result = await ExportService.export_to_excel(sample_report_data)
                        
                        # Проверки
                        assert result == '/tmp/test_file.xlsx'
                        mock_wb.save.assert_called_once()

    @pytest.mark.asyncio
    async def test_export_to_excel_not_available(self, sample_report_data):
        """Тест экспорта в Excel когда библиотека недоступна"""
        with patch('services.export_service.EXCEL_AVAILABLE', False):
            result = await ExportService.export_to_excel(sample_report_data)
            
            # Проверки
            assert result is None

    @pytest.mark.asyncio
    async def test_export_to_excel_with_selected_columns(self, sample_report_data):
        """Тест экспорта в Excel с выбранными столбцами"""
        with patch('services.export_service.EXCEL_AVAILABLE', True):
            with patch('services.export_service.Workbook') as mock_workbook:
                with patch('services.export_service.tempfile.gettempdir') as mock_tempdir:
                    with patch('services.export_service.os.path.join') as mock_join:
                        
                        mock_tempdir.return_value = '/tmp'
                        mock_join.return_value = '/tmp/test_file.xlsx'
                        
                        mock_wb = AsyncMock()
                        mock_ws = AsyncMock()
                        mock_wb.active = mock_ws
                        mock_workbook.return_value = mock_wb
                        
                        mock_cell = AsyncMock()
                        mock_ws.cell.return_value = mock_cell
                        mock_ws.__getitem__ = AsyncMock(return_value=mock_cell)
                        
                        selected_columns = ['Дата', 'Рабочий', 'Часы']
                        
                        result = await ExportService.export_to_excel(
                            sample_report_data,
                            selected_columns=selected_columns
                        )
                        
                        # Проверки
                        assert result == '/tmp/test_file.xlsx'
                        mock_wb.save.assert_called_once()

    @pytest.mark.asyncio
    async def test_export_to_excel_exception_handling(self, sample_report_data):
        """Тест обработки исключений при экспорте в Excel"""
        with patch('services.export_service.EXCEL_AVAILABLE', True):
            with patch('services.export_service.Workbook') as mock_workbook:
                
                # Мок исключения
                mock_workbook.side_effect = Exception("Excel error")
                
                result = await ExportService.export_to_excel(sample_report_data)
                
                # Проверки
                assert result is None

    @pytest.mark.asyncio
    async def test_export_to_pdf_success(self, sample_report_data):
        """Тест успешного экспорта в PDF"""
        with patch('services.export_service.PDF_AVAILABLE', True):
            with patch('services.export_service.SimpleDocTemplate') as mock_doc:
                with patch('services.export_service.tempfile.gettempdir') as mock_tempdir:
                    with patch('services.export_service.os.path.join') as mock_join:
                        
                        # Настройка моков
                        mock_tempdir.return_value = '/tmp'
                        mock_join.return_value = '/tmp/test_file.pdf'
                        
                        mock_doc_instance = AsyncMock()
                        mock_doc.return_value = mock_doc_instance
                        
                        result = await ExportService.export_to_pdf(sample_report_data)
                        
                        # Проверки
                        assert result == '/tmp/test_file.pdf'
                        mock_doc_instance.build.assert_called_once()

    @pytest.mark.asyncio
    async def test_export_to_pdf_not_available(self, sample_report_data):
        """Тест экспорта в PDF когда библиотека недоступна"""
        with patch('services.export_service.PDF_AVAILABLE', False):
            result = await ExportService.export_to_pdf(sample_report_data)
            
            # Проверки
            assert result is None

    @pytest.mark.asyncio
    async def test_export_to_pdf_with_custom_params(self, sample_report_data):
        """Тест экспорта в PDF с кастомными параметрами"""
        with patch('services.export_service.PDF_AVAILABLE', True):
            with patch('services.export_service.SimpleDocTemplate') as mock_doc:
                with patch('services.export_service.tempfile.gettempdir') as mock_tempdir:
                    with patch('services.export_service.os.path.join') as mock_join:
                        
                        mock_tempdir.return_value = '/tmp'
                        mock_join.return_value = '/tmp/custom_report.pdf'
                        
                        mock_doc_instance = AsyncMock()
                        mock_doc.return_value = mock_doc_instance
                        
                        result = await ExportService.export_to_pdf(
                            sample_report_data,
                            filename_prefix="custom_report",
                            company_name="Тестовая Компания",
                            include_signatures=False
                        )
                        
                        # Проверки
                        assert result == '/tmp/custom_report.pdf'
                        mock_doc_instance.build.assert_called_once()

    @pytest.mark.asyncio
    async def test_export_to_pdf_exception_handling(self, sample_report_data):
        """Тест обработки исключений при экспорте в PDF"""
        with patch('services.export_service.PDF_AVAILABLE', True):
            with patch('services.export_service.SimpleDocTemplate') as mock_doc:
                
                # Мок исключения
                mock_doc.side_effect = Exception("PDF error")
                
                result = await ExportService.export_to_pdf(sample_report_data)
                
                # Проверки
                assert result is None

    def test_get_available_formats_all_available(self):
        """Тест получения доступных форматов когда все библиотеки установлены"""
        with patch('services.export_service.EXCEL_AVAILABLE', True):
            with patch('services.export_service.PDF_AVAILABLE', True):
                
                result = ExportService.get_available_formats()
                
                # Проверки
                assert result['excel'] is True
                assert result['pdf'] is True

    def test_get_available_formats_none_available(self):
        """Тест получения доступных форматов когда библиотеки не установлены"""
        with patch('services.export_service.EXCEL_AVAILABLE', False):
            with patch('services.export_service.PDF_AVAILABLE', False):
                
                result = ExportService.get_available_formats()
                
                # Проверки
                assert result['excel'] is False
                assert result['pdf'] is False

    def test_available_columns_constant(self):
        """Тест константы доступных столбцов"""
        expected_columns = {
            "Дата": "date",
            "Рабочий": "worker",
            "Проект": "project",
            "Тип работы": "work_type",
            "Описание": "description",
            "Часы": "hours",
            "Ставка": "rate",
            "Сумма": "amount"
        }
        
        assert ExportService.AVAILABLE_COLUMNS == expected_columns

    @pytest.mark.asyncio
    async def test_export_to_excel_empty_data(self):
        """Тест экспорта пустых данных в Excel"""
        empty_data = {
            'period': {
                'start_date': date.today(),
                'end_date': date.today(),
                'days': 1
            },
            'summary': {
                'total_entries': 0,
                'total_hours': 0.0,
                'total_amount': 0.0
            },
            'entries': []
        }
        
        with patch('services.export_service.EXCEL_AVAILABLE', True):
            with patch('services.export_service.Workbook') as mock_workbook:
                with patch('services.export_service.tempfile.gettempdir') as mock_tempdir:
                    with patch('services.export_service.os.path.join') as mock_join:
                        
                        mock_tempdir.return_value = '/tmp'
                        mock_join.return_value = '/tmp/empty_report.xlsx'
                        
                        mock_wb = AsyncMock()
                        mock_ws = AsyncMock()
                        mock_wb.active = mock_ws
                        mock_workbook.return_value = mock_wb
                        
                        mock_cell = AsyncMock()
                        mock_ws.cell.return_value = mock_cell
                        mock_ws.__getitem__ = AsyncMock(return_value=mock_cell)
                        
                        result = await ExportService.export_to_excel(empty_data)
                        
                        # Проверки
                        assert result == '/tmp/empty_report.xlsx'
                        mock_wb.save.assert_called_once()

    @pytest.mark.asyncio
    async def test_export_to_pdf_empty_data(self):
        """Тест экспорта пустых данных в PDF"""
        empty_data = {
            'period': {
                'start_date': date.today(),
                'end_date': date.today(),
                'days': 1
            },
            'summary': {
                'total_entries': 0,
                'total_hours': 0.0,
                'total_amount': 0.0
            },
            'entries': []
        }
        
        with patch('services.export_service.PDF_AVAILABLE', True):
            with patch('services.export_service.SimpleDocTemplate') as mock_doc:
                with patch('services.export_service.tempfile.gettempdir') as mock_tempdir:
                    with patch('services.export_service.os.path.join') as mock_join:
                        
                        mock_tempdir.return_value = '/tmp'
                        mock_join.return_value = '/tmp/empty_report.pdf'
                        
                        mock_doc_instance = AsyncMock()
                        mock_doc.return_value = mock_doc_instance
                        
                        result = await ExportService.export_to_pdf(empty_data)
                        
                        # Проверки
                        assert result == '/tmp/empty_report.pdf'
                        mock_doc_instance.build.assert_called_once()
