# 🗄️ ДИЗАЙН БАЗЫ ДАННЫХ WORKLOG MVP v2.0

## 🎯 Принципы проектирования

### Основные принципы
1. **Нормализация** - устранение дублирования данных
2. **Производительность** - оптимизация для частых запросов
3. **Масштабируемость** - готовность к росту данных
4. **Безопасность** - изоляция данных между компаниями
5. **Целостность** - ограничения и связи для консистентности

### Архитектурные решения
- **Multi-tenancy** - изоляция данных по компаниям
- **Soft Delete** - мягкое удаление для аудита
- **Audit Trail** - отслеживание изменений
- **Денормализация** - для отчётов и аналитики

## 📊 ER-диаграмма

```mermaid
erDiagram
    companies ||--o{ users : "employs"
    companies ||--o{ projects : "owns"
    companies ||--o{ work_categories : "defines"
    
    users ||--o{ work_entries : "creates"
    users ||--o{ registration_tokens : "creates"
    users ||--o{ export_logs : "requests"
    
    projects ||--o{ work_entries : "contains"
    projects ||--o{ project_assignments : "has"
    
    users ||--o{ project_assignments : "assigned_to"
    work_categories ||--o{ work_entries : "categorizes"
    
    companies {
        bigint id PK
        varchar name
        varchar legal_name
        varchar inn
        varchar phone
        varchar email
        text address
        boolean is_active
        decimal default_hourly_rate
        decimal default_piece_rate
        text description
        text notes
        timestamp created_at
        timestamp updated_at
    }
    
    users {
        bigint id PK
        bigint telegram_id UK
        varchar username
        varchar first_name
        varchar last_name
        varchar role
        bigint company_id FK
        boolean is_active
        boolean is_blocked
        varchar phone
        text notes
        timestamp created_at
        timestamp updated_at
    }
    
    projects {
        bigint id PK
        varchar name
        bigint company_id FK
        text address
        text description
        decimal hourly_rate
        decimal piece_rate
        boolean is_active
        varchar client_name
        varchar client_contact
        text notes
        timestamp created_at
        timestamp updated_at
    }
    
    work_entries {
        bigint id PK
        bigint user_id FK
        bigint project_id FK
        bigint work_category_id FK
        date work_date
        text description
        decimal quantity
        varchar work_type
        decimal rate
        decimal total_amount
        text notes
        timestamp created_at
        timestamp updated_at
    }
    
    work_categories {
        bigint id PK
        varchar name
        bigint company_id FK
        varchar unit_type
        decimal default_rate
        text description
        boolean is_active
        timestamp created_at
        timestamp updated_at
    }
    
    project_assignments {
        bigint id PK
        bigint project_id FK
        bigint user_id FK
        date assigned_date
        date unassigned_date
        boolean is_active
        text notes
        timestamp created_at
        timestamp updated_at
    }
    
    registration_tokens {
        bigint id PK
        varchar token UK
        varchar token_type
        bigint company_id FK
        bigint created_by_user_id FK
        bigint used_by_user_id FK
        text description
        timestamp expires_at
        timestamp used_at
        boolean is_active
        timestamp created_at
        timestamp updated_at
    }
    
    export_logs {
        bigint id PK
        bigint user_id FK
        varchar export_type
        text filters
        varchar file_format
        varchar file_path
        bigint file_size
        varchar status
        text error_message
        timestamp created_at
        timestamp completed_at
    }
```

## 🏗️ Структура таблиц

### 1. companies (Компании)
```sql
CREATE TABLE companies (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    legal_name VARCHAR(255),
    inn VARCHAR(12),
    phone VARCHAR(20),
    email VARCHAR(255),
    address TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    default_hourly_rate DECIMAL(10,2) DEFAULT 500.00,
    default_piece_rate DECIMAL(10,2) DEFAULT 100.00,
    description TEXT,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT companies_name_unique UNIQUE (name),
    CONSTRAINT companies_inn_unique UNIQUE (inn),
    CONSTRAINT companies_email_unique UNIQUE (email),
    CONSTRAINT companies_hourly_rate_positive CHECK (default_hourly_rate > 0),
    CONSTRAINT companies_piece_rate_positive CHECK (default_piece_rate > 0)
);

-- Индексы
CREATE INDEX idx_companies_is_active ON companies (is_active);
CREATE INDEX idx_companies_created_at ON companies (created_at);
```

### 2. users (Пользователи)
```sql
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    telegram_id BIGINT NOT NULL,
    username VARCHAR(255),
    first_name VARCHAR(255) NOT NULL,
    last_name VARCHAR(255),
    role VARCHAR(50) NOT NULL DEFAULT 'ROLE_WORKER',
    company_id BIGINT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    is_blocked BOOLEAN DEFAULT FALSE,
    phone VARCHAR(20),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT users_telegram_id_unique UNIQUE (telegram_id),
    CONSTRAINT users_role_check CHECK (role IN ('ROLE_WORKER', 'ROLE_DIRECTOR', 'ROLE_ADMIN')),
    CONSTRAINT fk_users_company FOREIGN KEY (company_id) REFERENCES companies (id) ON DELETE RESTRICT
);

-- Индексы
CREATE INDEX idx_users_telegram_id ON users (telegram_id);
CREATE INDEX idx_users_company_id ON users (company_id);
CREATE INDEX idx_users_role ON users (role);
CREATE INDEX idx_users_is_active ON users (is_active);
CREATE INDEX idx_users_created_at ON users (created_at);
```

### 3. projects (Проекты)
```sql
CREATE TABLE projects (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    company_id BIGINT NOT NULL,
    address TEXT,
    description TEXT,
    hourly_rate DECIMAL(10,2),
    piece_rate DECIMAL(10,2),
    is_active BOOLEAN DEFAULT TRUE,
    client_name VARCHAR(255),
    client_contact VARCHAR(255),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT projects_hourly_rate_positive CHECK (hourly_rate IS NULL OR hourly_rate > 0),
    CONSTRAINT projects_piece_rate_positive CHECK (piece_rate IS NULL OR piece_rate > 0),
    CONSTRAINT fk_projects_company FOREIGN KEY (company_id) REFERENCES companies (id) ON DELETE RESTRICT
);

-- Индексы
CREATE INDEX idx_projects_company_id ON projects (company_id);
CREATE INDEX idx_projects_is_active ON projects (is_active);
CREATE INDEX idx_projects_name ON projects (name);
CREATE INDEX idx_projects_created_at ON projects (created_at);

-- Уникальность названия проекта в рамках компании
CREATE UNIQUE INDEX idx_projects_company_name_unique ON projects (company_id, name) WHERE is_active = TRUE;
```

### 4. work_categories (Категории работ)
```sql
CREATE TABLE work_categories (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    company_id BIGINT NOT NULL,
    unit_type VARCHAR(50) NOT NULL DEFAULT 'hours',
    default_rate DECIMAL(10,2) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT work_categories_unit_type_check CHECK (unit_type IN ('hours', 'pieces', 'square_meters', 'linear_meters')),
    CONSTRAINT work_categories_default_rate_positive CHECK (default_rate > 0),
    CONSTRAINT fk_work_categories_company FOREIGN KEY (company_id) REFERENCES companies (id) ON DELETE RESTRICT
);

-- Индексы
CREATE INDEX idx_work_categories_company_id ON work_categories (company_id);
CREATE INDEX idx_work_categories_is_active ON work_categories (is_active);
CREATE INDEX idx_work_categories_unit_type ON work_categories (unit_type);

-- Уникальность названия категории в рамках компании
CREATE UNIQUE INDEX idx_work_categories_company_name_unique ON work_categories (company_id, name) WHERE is_active = TRUE;
```

### 5. work_entries (Записи о работе)
```sql
CREATE TABLE work_entries (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    project_id BIGINT NOT NULL,
    work_category_id BIGINT,
    work_date DATE NOT NULL DEFAULT CURRENT_DATE,
    description TEXT NOT NULL,
    quantity DECIMAL(10,2) NOT NULL,
    work_type VARCHAR(50) NOT NULL DEFAULT 'hourly',
    rate DECIMAL(10,2) NOT NULL,
    total_amount DECIMAL(12,2) GENERATED ALWAYS AS (quantity * rate) STORED,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT work_entries_work_type_check CHECK (work_type IN ('hourly', 'piece')),
    CONSTRAINT work_entries_quantity_positive CHECK (quantity > 0),
    CONSTRAINT work_entries_rate_positive CHECK (rate > 0),
    CONSTRAINT work_entries_description_not_empty CHECK (LENGTH(TRIM(description)) >= 3),
    CONSTRAINT fk_work_entries_user FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE RESTRICT,
    CONSTRAINT fk_work_entries_project FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE RESTRICT,
    CONSTRAINT fk_work_entries_work_category FOREIGN KEY (work_category_id) REFERENCES work_categories (id) ON DELETE SET NULL
);

-- Индексы для производительности
CREATE INDEX idx_work_entries_user_id ON work_entries (user_id);
CREATE INDEX idx_work_entries_project_id ON work_entries (project_id);
CREATE INDEX idx_work_entries_work_date ON work_entries (work_date);
CREATE INDEX idx_work_entries_work_type ON work_entries (work_type);
CREATE INDEX idx_work_entries_created_at ON work_entries (created_at);

-- Составные индексы для отчётов
CREATE INDEX idx_work_entries_user_date ON work_entries (user_id, work_date);
CREATE INDEX idx_work_entries_project_date ON work_entries (project_id, work_date);
CREATE INDEX idx_work_entries_user_project_date ON work_entries (user_id, project_id, work_date);
```

### 6. project_assignments (Назначения на проекты)
```sql
CREATE TABLE project_assignments (
    id BIGSERIAL PRIMARY KEY,
    project_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    assigned_date DATE NOT NULL DEFAULT CURRENT_DATE,
    unassigned_date DATE,
    is_active BOOLEAN DEFAULT TRUE,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT project_assignments_dates_check CHECK (unassigned_date IS NULL OR unassigned_date >= assigned_date),
    CONSTRAINT fk_project_assignments_project FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE,
    CONSTRAINT fk_project_assignments_user FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
);

-- Индексы
CREATE INDEX idx_project_assignments_project_id ON project_assignments (project_id);
CREATE INDEX idx_project_assignments_user_id ON project_assignments (user_id);
CREATE INDEX idx_project_assignments_is_active ON project_assignments (is_active);
CREATE INDEX idx_project_assignments_assigned_date ON project_assignments (assigned_date);

-- Уникальность активного назначения
CREATE UNIQUE INDEX idx_project_assignments_unique_active ON project_assignments (project_id, user_id) WHERE is_active = TRUE;
```

### 7. registration_tokens (Токены регистрации)
```sql
CREATE TABLE registration_tokens (
    id BIGSERIAL PRIMARY KEY,
    token VARCHAR(255) NOT NULL,
    token_type VARCHAR(50) NOT NULL DEFAULT 'WORKER',
    company_id BIGINT NOT NULL,
    created_by_user_id BIGINT NOT NULL,
    used_by_user_id BIGINT,
    description TEXT,
    expires_at TIMESTAMP NOT NULL,
    used_at TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT registration_tokens_token_unique UNIQUE (token),
    CONSTRAINT registration_tokens_token_type_check CHECK (token_type IN ('WORKER', 'DIRECTOR')),
    CONSTRAINT registration_tokens_expires_future CHECK (expires_at > created_at),
    CONSTRAINT fk_registration_tokens_company FOREIGN KEY (company_id) REFERENCES companies (id) ON DELETE CASCADE,
    CONSTRAINT fk_registration_tokens_created_by FOREIGN KEY (created_by_user_id) REFERENCES users (id) ON DELETE RESTRICT,
    CONSTRAINT fk_registration_tokens_used_by FOREIGN KEY (used_by_user_id) REFERENCES users (id) ON DELETE SET NULL
);

-- Индексы
CREATE INDEX idx_registration_tokens_token ON registration_tokens (token);
CREATE INDEX idx_registration_tokens_company_id ON registration_tokens (company_id);
CREATE INDEX idx_registration_tokens_is_active ON registration_tokens (is_active);
CREATE INDEX idx_registration_tokens_expires_at ON registration_tokens (expires_at);
```

### 8. export_logs (Логи экспорта)
```sql
CREATE TABLE export_logs (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    export_type VARCHAR(50) NOT NULL,
    filters JSONB,
    file_format VARCHAR(10) NOT NULL,
    file_path VARCHAR(500),
    file_size BIGINT,
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,
    
    CONSTRAINT export_logs_export_type_check CHECK (export_type IN ('work_entries', 'project_report', 'user_report', 'company_report')),
    CONSTRAINT export_logs_file_format_check CHECK (file_format IN ('pdf', 'excel', 'csv')),
    CONSTRAINT export_logs_status_check CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
    CONSTRAINT export_logs_file_size_positive CHECK (file_size IS NULL OR file_size > 0),
    CONSTRAINT fk_export_logs_user FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE RESTRICT
);

-- Индексы
CREATE INDEX idx_export_logs_user_id ON export_logs (user_id);
CREATE INDEX idx_export_logs_status ON export_logs (status);
CREATE INDEX idx_export_logs_created_at ON export_logs (created_at);
CREATE INDEX idx_export_logs_export_type ON export_logs (export_type);
```

## 🔍 Представления (Views)

### 1. Активные пользователи с компаниями
```sql
CREATE VIEW active_users_with_companies AS
SELECT 
    u.id,
    u.telegram_id,
    u.username,
    u.first_name,
    u.last_name,
    u.role,
    u.phone,
    c.id as company_id,
    c.name as company_name,
    c.legal_name as company_legal_name
FROM users u
JOIN companies c ON u.company_id = c.id
WHERE u.is_active = TRUE 
  AND u.is_blocked = FALSE 
  AND c.is_active = TRUE;
```

### 2. Статистика работ по пользователям
```sql
CREATE VIEW user_work_statistics AS
SELECT 
    u.id as user_id,
    u.first_name,
    u.last_name,
    u.company_id,
    COUNT(we.id) as total_entries,
    SUM(we.quantity) as total_quantity,
    SUM(we.total_amount) as total_amount,
    AVG(we.rate) as average_rate,
    MIN(we.work_date) as first_work_date,
    MAX(we.work_date) as last_work_date
FROM users u
LEFT JOIN work_entries we ON u.id = we.user_id
WHERE u.is_active = TRUE
GROUP BY u.id, u.first_name, u.last_name, u.company_id;
```

### 3. Статистика проектов
```sql
CREATE VIEW project_statistics AS
SELECT 
    p.id as project_id,
    p.name as project_name,
    p.company_id,
    COUNT(DISTINCT we.user_id) as workers_count,
    COUNT(we.id) as total_entries,
    SUM(we.quantity) as total_quantity,
    SUM(we.total_amount) as total_amount,
    MIN(we.work_date) as first_work_date,
    MAX(we.work_date) as last_work_date,
    p.is_active
FROM projects p
LEFT JOIN work_entries we ON p.id = we.project_id
GROUP BY p.id, p.name, p.company_id, p.is_active;
```

## 🚀 Функции и триггеры

### 1. Автоматическое обновление updated_at
```sql
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Применение к таблицам
CREATE TRIGGER update_companies_updated_at BEFORE UPDATE ON companies FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON projects FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_work_entries_updated_at BEFORE UPDATE ON work_entries FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_work_categories_updated_at BEFORE UPDATE ON work_categories FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_project_assignments_updated_at BEFORE UPDATE ON project_assignments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_registration_tokens_updated_at BEFORE UPDATE ON registration_tokens FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

### 2. Проверка доступа к проекту
```sql
CREATE OR REPLACE FUNCTION check_project_access(user_id_param BIGINT, project_id_param BIGINT)
RETURNS BOOLEAN AS $$
DECLARE
    user_company_id BIGINT;
    project_company_id BIGINT;
    user_role VARCHAR(50);
    is_assigned BOOLEAN;
BEGIN
    -- Получаем информацию о пользователе
    SELECT company_id, role INTO user_company_id, user_role
    FROM users 
    WHERE id = user_id_param AND is_active = TRUE AND is_blocked = FALSE;
    
    IF NOT FOUND THEN
        RETURN FALSE;
    END IF;
    
    -- Получаем компанию проекта
    SELECT company_id INTO project_company_id
    FROM projects 
    WHERE id = project_id_param AND is_active = TRUE;
    
    IF NOT FOUND THEN
        RETURN FALSE;
    END IF;
    
    -- Проверяем, что пользователь из той же компании
    IF user_company_id != project_company_id THEN
        RETURN FALSE;
    END IF;
    
    -- Директора и админы имеют доступ ко всем проектам компании
    IF user_role IN ('ROLE_DIRECTOR', 'ROLE_ADMIN') THEN
        RETURN TRUE;
    END IF;
    
    -- Рабочие имеют доступ только к назначенным проектам
    SELECT EXISTS(
        SELECT 1 FROM project_assignments 
        WHERE project_id = project_id_param 
          AND user_id = user_id_param 
          AND is_active = TRUE
    ) INTO is_assigned;
    
    RETURN is_assigned;
END;
$$ LANGUAGE plpgsql;
```

## 📊 Оптимизация производительности

### Партиционирование work_entries по дате
```sql
-- Создание партиционированной таблицы (PostgreSQL 12+)
CREATE TABLE work_entries_partitioned (
    LIKE work_entries INCLUDING ALL
) PARTITION BY RANGE (work_date);

-- Создание партиций по месяцам
CREATE TABLE work_entries_2025_01 PARTITION OF work_entries_partitioned
    FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');

CREATE TABLE work_entries_2025_02 PARTITION OF work_entries_partitioned
    FOR VALUES FROM ('2025-02-01') TO ('2025-03-01');

-- И так далее...
```

### Материализованные представления для отчётов
```sql
CREATE MATERIALIZED VIEW monthly_user_statistics AS
SELECT 
    DATE_TRUNC('month', we.work_date) as month,
    we.user_id,
    u.first_name,
    u.last_name,
    u.company_id,
    COUNT(we.id) as entries_count,
    SUM(we.quantity) as total_quantity,
    SUM(we.total_amount) as total_amount
FROM work_entries we
JOIN users u ON we.user_id = u.id
GROUP BY DATE_TRUNC('month', we.work_date), we.user_id, u.first_name, u.last_name, u.company_id;

-- Создание индекса
CREATE INDEX idx_monthly_user_statistics_month_user ON monthly_user_statistics (month, user_id);

-- Обновление материализованного представления (можно автоматизировать через cron)
REFRESH MATERIALIZED VIEW monthly_user_statistics;
```

## 🔒 Безопасность данных

### Row Level Security (RLS)
```sql
-- Включение RLS для таблицы пользователей
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Политика: пользователи видят только данные своей компании
CREATE POLICY users_company_isolation ON users
    FOR ALL
    TO application_role
    USING (company_id = current_setting('app.current_company_id')::BIGINT);

-- Аналогично для других таблиц
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
CREATE POLICY projects_company_isolation ON projects
    FOR ALL
    TO application_role
    USING (company_id = current_setting('app.current_company_id')::BIGINT);

ALTER TABLE work_entries ENABLE ROW LEVEL SECURITY;
CREATE POLICY work_entries_company_isolation ON work_entries
    FOR ALL
    TO application_role
    USING (
        EXISTS (
            SELECT 1 FROM users u 
            WHERE u.id = work_entries.user_id 
              AND u.company_id = current_setting('app.current_company_id')::BIGINT
        )
    );
```

### Роли и права доступа
```sql
-- Создание ролей
CREATE ROLE worklog_app_role;
CREATE ROLE worklog_readonly_role;

-- Права для основного приложения
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO worklog_app_role;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO worklog_app_role;

-- Права только для чтения (для отчётов)
GRANT SELECT ON ALL TABLES IN SCHEMA public TO worklog_readonly_role;
```

## 📋 Миграции

### Пример миграции Alembic
```python
"""Initial database schema

Revision ID: 001_initial_schema
Revises: 
Create Date: 2025-06-27 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers
revision = '001_initial_schema'
down_revision = None
branch_labels = None
depends_on = None

def upgrade():
    # Создание таблицы companies
    op.create_table('companies',
        sa.Column('id', sa.BigInteger(), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('legal_name', sa.String(length=255), nullable=True),
        sa.Column('inn', sa.String(length=12), nullable=True),
        sa.Column('phone', sa.String(length=20), nullable=True),
        sa.Column('email', sa.String(length=255), nullable=True),
        sa.Column('address', sa.Text(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('default_hourly_rate', sa.Numeric(precision=10, scale=2), nullable=True),
        sa.Column('default_piece_rate', sa.Numeric(precision=10, scale=2), nullable=True),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('notes', sa.Text(), nullable=True),
        sa.Column('created_at', sa.TIMESTAMP(), nullable=True),
        sa.Column('updated_at', sa.TIMESTAMP(), nullable=True),
        sa.CheckConstraint('default_hourly_rate > 0', name='companies_hourly_rate_positive'),
        sa.CheckConstraint('default_piece_rate > 0', name='companies_piece_rate_positive'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('email'),
        sa.UniqueConstraint('inn'),
        sa.UniqueConstraint('name')
    )
    
    # Создание индексов
    op.create_index('idx_companies_is_active', 'companies', ['is_active'])
    op.create_index('idx_companies_created_at', 'companies', ['created_at'])
    
    # Остальные таблицы...

def downgrade():
    op.drop_table('companies')
    # Удаление остальных таблиц в обратном порядке...
```
