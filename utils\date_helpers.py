"""
Утилиты для работы с датами (адаптировано из examples).

Функции:
- parse_finnish_date() - парсинг финляндского формата даты
- validate_date_range() - валидация диапазона дат
- get_last_week_range() - получение диапазона последней недели
- get_last_month_range() - получение диапазона последнего месяца
"""
import re
from datetime import datetime, date, timedelta
from typing import Tuple, Optional


def parse_finnish_date(date_str: str) -> date:
    """
    Парсит дату в финляндском формате ДД.ММ.ГГГГ.
    
    Args:
        date_str: Строка с датой в формате ДД.ММ.ГГГГ
        
    Returns:
        Объект date
        
    Raises:
        ValueError: При некорректном формате даты
    """
    if not date_str or not date_str.strip():
        raise ValueError("Дата не может быть пустой")
    
    # Убираем лишние пробелы
    date_str = date_str.strip()
    
    # Проверяем формат ДД.ММ.ГГГГ
    pattern = r'^(\d{1,2})\.(\d{1,2})\.(\d{4})$'
    match = re.match(pattern, date_str)
    
    if not match:
        raise ValueError("Неверный формат даты. Используйте ДД.ММ.ГГГГ (например: 15.01.2024)")
    
    day, month, year = map(int, match.groups())
    
    try:
        parsed_date = date(year, month, day)
    except ValueError as e:
        raise ValueError(f"Некорректная дата: {e}")
    
    # Проверяем разумные границы
    today = date.today()
    min_date = date(2020, 1, 1)
    max_date = today + timedelta(days=365)  # Максимум год вперед
    
    if parsed_date < min_date:
        raise ValueError(f"Дата не может быть раньше {min_date.strftime('%d.%m.%Y')}")
    
    if parsed_date > max_date:
        raise ValueError(f"Дата не может быть позже {max_date.strftime('%d.%m.%Y')}")
    
    return parsed_date


def validate_date_range(date_range_str: str) -> Tuple[bool, str, Optional[Tuple[date, date]]]:
    """
    Валидирует диапазон дат в формате ДД.ММ.ГГГГ-ДД.ММ.ГГГГ.
    
    Args:
        date_range_str: Строка с диапазоном дат
        
    Returns:
        Кортеж (валидно, сообщение об ошибке, (начальная_дата, конечная_дата))
    """
    if not date_range_str or not date_range_str.strip():
        return False, "Диапазон дат не может быть пустым", None
    
    date_range_str = date_range_str.strip()
    
    # Проверяем формат диапазона
    if '-' not in date_range_str:
        # Возможно, это одна дата - пробуем парсить как одну дату
        try:
            single_date = parse_finnish_date(date_range_str)
            return True, "", (single_date, single_date)
        except ValueError as e:
            return False, f"Неверный формат. Используйте ДД.ММ.ГГГГ-ДД.ММ.ГГГГ или ДД.ММ.ГГГГ: {e}", None
    
    # Разделяем на начальную и конечную даты
    parts = date_range_str.split('-')
    if len(parts) != 2:
        return False, "Неверный формат диапазона. Используйте ДД.ММ.ГГГГ-ДД.ММ.ГГГГ", None
    
    start_str, end_str = parts[0].strip(), parts[1].strip()
    
    try:
        start_date = parse_finnish_date(start_str)
    except ValueError as e:
        return False, f"Ошибка в начальной дате: {e}", None
    
    try:
        end_date = parse_finnish_date(end_str)
    except ValueError as e:
        return False, f"Ошибка в конечной дате: {e}", None
    
    # Проверяем, что начальная дата не позже конечной
    if start_date > end_date:
        return False, "Начальная дата не может быть позже конечной", None
    
    # Проверяем разумный диапазон (не более 2 лет)
    if (end_date - start_date).days > 730:
        return False, "Диапазон не может превышать 2 года", None
    
    return True, "", (start_date, end_date)


def get_last_week_range() -> Tuple[date, date]:
    """
    Возвращает диапазон дат за последнюю неделю.
    
    Returns:
        Кортеж (начальная_дата, конечная_дата)
    """
    today = date.today()
    start_date = today - timedelta(days=7)
    return start_date, today


def get_last_month_range() -> Tuple[date, date]:
    """
    Возвращает диапазон дат за последний месяц.
    
    Returns:
        Кортеж (начальная_дата, конечная_дата)
    """
    today = date.today()
    
    # Вычисляем дату месяц назад
    if today.month == 1:
        start_date = date(today.year - 1, 12, today.day)
    else:
        try:
            start_date = date(today.year, today.month - 1, today.day)
        except ValueError:
            # Если в предыдущем месяце нет такого дня (например, 31 число)
            # Берем последний день предыдущего месяца
            if today.month == 1:
                start_date = date(today.year - 1, 12, 31)
            else:
                # Находим последний день предыдущего месяца
                first_day_current_month = date(today.year, today.month, 1)
                start_date = first_day_current_month - timedelta(days=1)
    
    return start_date, today


def get_current_month_range() -> Tuple[date, date]:
    """
    Возвращает диапазон дат текущего месяца.
    
    Returns:
        Кортеж (начальная_дата, конечная_дата)
    """
    today = date.today()
    start_date = date(today.year, today.month, 1)
    return start_date, today


def format_date_finnish(date_obj: date) -> str:
    """
    Форматирует дату в финляндском формате ДД.ММ.ГГГГ.
    
    Args:
        date_obj: Объект date
        
    Returns:
        Строка с датой в формате ДД.ММ.ГГГГ
    """
    return date_obj.strftime("%d.%m.%Y")


def format_date_range_finnish(start_date: date, end_date: date) -> str:
    """
    Форматирует диапазон дат в финляндском формате.
    
    Args:
        start_date: Начальная дата
        end_date: Конечная дата
        
    Returns:
        Строка с диапазоном дат
    """
    if start_date == end_date:
        return format_date_finnish(start_date)
    
    return f"{format_date_finnish(start_date)}-{format_date_finnish(end_date)}"


def is_weekend(date_obj: date) -> bool:
    """
    Проверяет, является ли дата выходным днем.
    
    Args:
        date_obj: Объект date
        
    Returns:
        True если суббота или воскресенье
    """
    return date_obj.weekday() >= 5  # 5 = суббота, 6 = воскресенье


def get_working_days_count(start_date: date, end_date: date) -> int:
    """
    Подсчитывает количество рабочих дней в диапазоне.
    
    Args:
        start_date: Начальная дата
        end_date: Конечная дата
        
    Returns:
        Количество рабочих дней
    """
    working_days = 0
    current_date = start_date
    
    while current_date <= end_date:
        if not is_weekend(current_date):
            working_days += 1
        current_date += timedelta(days=1)
    
    return working_days


def parse_time_duration(duration_str: str) -> float:
    """
    Парсит продолжительность времени в различных форматах.
    
    Поддерживаемые форматы:
    - "8" или "8.0" - часы
    - "8,5" - часы с запятой как разделителем
    - "8:30" - часы:минуты
    
    Args:
        duration_str: Строка с продолжительностью
        
    Returns:
        Продолжительность в часах
        
    Raises:
        ValueError: При некорректном формате
    """
    if not duration_str or not duration_str.strip():
        raise ValueError("Продолжительность не может быть пустой")
    
    duration_str = duration_str.strip()
    
    # Формат часы:минуты
    if ':' in duration_str:
        parts = duration_str.split(':')
        if len(parts) != 2:
            raise ValueError("Неверный формат времени. Используйте ЧЧ:ММ")
        
        try:
            hours = int(parts[0])
            minutes = int(parts[1])
        except ValueError:
            raise ValueError("Часы и минуты должны быть числами")
        
        if hours < 0 or minutes < 0 or minutes >= 60:
            raise ValueError("Некорректное время")
        
        return hours + minutes / 60.0
    
    # Формат с запятой или точкой
    duration_str = duration_str.replace(',', '.')
    
    try:
        hours = float(duration_str)
    except ValueError:
        raise ValueError("Продолжительность должна быть числом")
    
    if hours < 0:
        raise ValueError("Продолжительность не может быть отрицательной")
    
    if hours > 24:
        raise ValueError("Продолжительность не может превышать 24 часа")
    
    return hours


def get_week_start_end(date_obj: date) -> Tuple[date, date]:
    """
    Возвращает начало и конец недели для заданной даты.
    Неделя начинается с понедельника.
    
    Args:
        date_obj: Дата
        
    Returns:
        Кортеж (понедельник, воскресенье)
    """
    # Понедельник = 0, воскресенье = 6
    days_since_monday = date_obj.weekday()
    week_start = date_obj - timedelta(days=days_since_monday)
    week_end = week_start + timedelta(days=6)
    
    return week_start, week_end
