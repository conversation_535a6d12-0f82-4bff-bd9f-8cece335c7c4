"""
Конфигурация базы данных PostgreSQL с SQLAlchemy 2.0
"""
import os
from sqlalchemy.ext.asyncio import create_async_engine, async_sessionmaker, AsyncSession
from sqlalchemy.orm import DeclarativeBase
from dotenv import load_dotenv

load_dotenv()


class Base(DeclarativeBase):
    """Базовый класс для всех моделей"""
    pass


# Создание движка базы данных
DATABASE_URL = os.getenv("DATABASE_URL")
if not DATABASE_URL:
    raise ValueError("DATABASE_URL не найден в переменных окружения")

engine = create_async_engine(
    DATABASE_URL,
    echo=True,  # Логирование SQL запросов в режиме разработки
    pool_pre_ping=True,
    pool_recycle=300
)

# Создание фабрики сессий
async_session = async_sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False
)


async def init_db():
    """Инициализация базы данных - создание всех таблиц"""
    # Импортируем все модели для создания таблиц
    from . import models  # noqa

    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)


async def get_session() -> AsyncSession:
    """Получение сессии базы данных"""
    async with async_session() as session:
        try:
            yield session
        finally:
            await session.close()
