# 🎭 КОМАНДЫ ПО РОЛЯМ WORKLOG MVP v2.0 (ФИНЛЯНДИЯ)

## 🎯 Общие принципы v2.0

### Система команд v2.0 (Финальная версия)
- **Только inline-клавиатуры** - никаких текстовых команд в меню
- **Токен-система регистрации** - регистрация только по ссылкам
- **Контекстные меню** - команды зависят от роли пользователя
- **Единый стиль** - одинаковое оформление для всех ролей
- **Безопасность** - проверка прав на каждом уровне
- **Финляндская локализация** - евро, финские адреса, местные стандарты

### Система ролей (Финальная)
- **Администратор** - управление всей системой через токен-ссылки
- **Директор** - управление компанией и рабочими
- **Рабочий** - ведение учета рабочего времени

### Регистрация пользователей
- Только через токен-ссылки, генерируемые администратором/директором
- Токены одноразовые с истечением срока (24 часа)
- Автоматическое назначение роли и привязка к компании

---

## 👑 КОМАНДЫ АДМИНИСТРАТОРА

### Главное меню администратора
```
📍 Вы — администратор
👇 Выберите действие:
[➕ Добавить пользователя]
[📋 Список директоров и компаний]
[👷 Список рабочих]
[🗂 Удалённые компании]
[ℹ️ Инфо]
```

### Возможности администратора

| Возможность | Доступна? | Примечание |
|-------------|-----------|------------|
| Генерация токенов | ✅ | Для директора и рабочего |
| Просмотр всех компаний | ✅ | Включая soft-deleted |
| Просмотр всех рабочих | ✅ | С указанием компании |
| Удаление компании (soft delete) | ✅ | Удалённые не видны в остальных функциях |
| Восстановление компании | ✅ | Через меню |
| Инфо панель | ✅ | Статистика |
| Регистрация компании напрямую | ❌ | Только через токен |
| Регистрация рабочего напрямую | ❌ | Только через токен |

---

## 👨‍💼 КОМАНДЫ ДИРЕКТОРА

### Главное меню директора
```
🏢 Управление компаниями
👷 Управление рабочими
📊 Просмотр отчётов
📁 Экспорт / Импорт данных
🛠️ Редактировать типы работ
ℹ️ Инфо
```

### Возможности директора

| Возможность | Доступна? | Примечание |
|-------------|-----------|------------|
| Управление компаниями | ✅ | Создание, переключение, удаление |
| Управление рабочими | ✅ | Добавление через токены, список |
| Просмотр отчётов | ✅ | По дате, рабочему, проекту |
| Экспорт данных | ✅ | Excel, PDF с фильтрами |
| Редактирование типов работ | ✅ | Полное управление |
| Импорт типов работ | ✅ | Из Excel файлов |

---

## 👷 КОМАНДЫ РАБОЧЕГО

### Главное меню рабочего
```
📝 Добавить работу
📋 Мои записи
📊 Мой отчёт
🏗️ Выбрать проект
➕ Новый проект
✏️ Редактировать проект
📤 Экспорт данных
ℹ️ Инфо
```

### Возможности рабочего

| Возможность | Доступна? | Примечание |
|-------------|-----------|------------|
| Добавление записей о работе | ✅ | 7-шаговый FSM |
| Просмотр собственных записей | ✅ | С пагинацией и фильтрами |
| Редактирование записей | ✅ | Только собственных |
| Удаление записей | ✅ | Только собственных |
| Создание отчётов | ✅ | Личные отчёты |
| Экспорт данных | ✅ | Excel/PDF собственных данных |
| Управление проектами | ✅ | Создание и редактирование |
| Выбор активного проекта | ✅ | Из доступных проектов |

## 📝 ДЕТАЛЬНОЕ ОПИСАНИЕ КОМАНД

### 📝 Добавить работу (/addwork)
**FSM состояния (7 шагов):**
1. `waiting_for_date_choice` - выбор даты (сегодня/ввести дату)
2. `waiting_for_date_input` - ввод конкретной даты (формат: ДД.ММ.ГГГГ)
3. `waiting_for_work_type` - выбор типа работы из активного проекта
4. `waiting_for_description` - описание выполненной работы
5. `waiting_for_quantity` - количество (часы, м², шт и т.д.)
6. `confirming_entry` - подтверждение записи с расчётом суммы в евро
7. **Сохранение** - создание записи в БД

**Зависимости:**
- Требует установленный активный проект
- Требует типы работ в проекте
- Права: `can_add_work = True`

**Пример диалога:**
```
Бот: Выберите дату работы:
[📅 Сегодня] [📅 Ввести дату] [🔙 Отмена]

Пользователь: Сегодня

Бот: Выберите тип работы:
[🔨 Монтаж] [⚡ Электромонтаж] [🔧 Сантехника] [🔙 Отмена]

Пользователь: Монтаж

Бот: Опишите выполненную работу:
Пользователь: Установка окон на 2 этаже

Бот: Введите количество (час):
Пользователь: 8,5

Бот: Подтвердите запись:
📅 Дата: 28.06.2024
🔨 Тип: Монтаж
📝 Описание: Установка окон на 2 этаже
⏱️ Количество: 8,5 час
💰 Ставка: 25,00 €/час
💵 Сумма: 212,50 €

[✅ Да] [❌ Нет]
```

### 📋 Мои записи (/list)
**FSM состояния (2 шага):**
1. `selecting_filter` - выбор фильтра
2. `entering_date_range` - ввод диапазона дат (формат: ДД.ММ.ГГГГ-ДД.ММ.ГГГГ)

**Функции:**
- **Пагинация** - по 10 записей на страницу
- **Фильтрация** - все записи или за период
- **Inline-кнопки** для каждой записи: [✏️ Редактировать] [🗑️ Удалить]

**Пример вывода:**
```
📋 Записи о работе за всё время
Страница 1 из 3 (всего записей: 25)

1. 26.06.2024 - Монтаж
   Установка дверей в офисе
   6,0 час × 25,00 € = 150,00 €

2. 27.06.2024 - Электромонтаж
   Прокладка кабеля
   4,5 час × 30,00 € = 135,00 €

💰 Итого по странице: 285,00 €
💰 Общий итог: 2 150,00 €

[⬅️ Назад] [1/3] [Вперед ➡️]
[🔙 В главное меню]
```

### ➕ Новый проект (/newproject)
**FSM состояния (8 шагов):**
1. `creating_project_name` - ввод названия проекта
2. `creating_project_address` - ввод адреса проекта
3. `choosing_copy_source` - выбор источника копирования типов работ
4. `adding_work_type_name` - ввод названия типа работы
5. `adding_work_type_unit` - ввод единицы измерения
6. `adding_work_type_rate_type` - выбор типа ставки
7. `adding_work_type_value` - ввод значения ставки
8. `confirming_add_another_work_type` - подтверждение добавления еще типов

**Циклическое добавление типов работ:**
- Можно добавить неограниченное количество типов
- Каждый тип: название → единица → тип ставки → значение
- Валидация на каждом шаге
- Автоматическая установка как активного проекта

**Типы ставок:**
- **Фиксированная** - одна сумма за всю работу
- **За единицу** - умножается на количество

### ✏️ Редактирование записи (/edit)
**FSM состояния (3 шага):**
1. `selecting_entry_to_edit` - выбор записи из последних 10
2. `choosing_edit_field` - выбор поля (дата, тип, количество, описание)
3. `entering_edit_value` - ввод нового значения

**Ограничения:**
- Можно редактировать только свои записи
- Проверка доступа через `AccessControlService`

### 🗑️ Удаление записи (/delete)
**FSM состояния (2 шага):**
1. `selecting_entry_to_delete` - выбор записи
2. `confirming_delete` - подтверждение удаления

**Безопасность:**
- Двойное подтверждение
- Проверка прав доступа
- Логирование операций

### 📤 Экспорт данных (/export)
**FSM состояния (6 шагов):**
1. `choosing_format` - выбор формата (Excel/PDF)
2. `choosing_project` - выбор проекта (если несколько)
3. `choosing_period` - выбор периода
4. `entering_date_range` - ввод дат (если "За период")
5. `choosing_columns` - выбор столбцов (только Excel)
6. `confirming_export` - подтверждение и генерация

**📤 Экспорт Excel:**
- **Настраиваемые столбцы**: Дата, Тип работы, Описание, Количество, Ед. изм., Ставка, Сумма

**📤 Экспорт PDF:**
- **Фиксированный шаблон** с логотипом компании, информацией о рабочем, полями для подписей

## 🔐 СИСТЕМА БЕЗОПАСНОСТИ

### RBAC права рабочего
```python
worker_permissions = {
    "can_add_work": True,
    "can_view_own_reports": True,
    "can_edit_own_entries": True,
    "can_export_own_data": True,
    "can_manage_projects": True,  # Для создания/редактирования проектов
    "can_manage_workers": False,
    "is_admin": False
}
```

### Проверки безопасности
- **Изоляция данных** по компаниям
- **Доступ только к своим записям** для рабочих
- **Валидация токенов** регистрации
- **Логирование всех операций**
- **Двойное подтверждение** критических действий

## 🌍 ФИНЛЯНДСКАЯ ЛОКАЛИЗАЦИЯ

### Особенности
- **Валюта**: € (евро) вместо рублей
- **Формат даты**: ДД.ММ.ГГГГ (европейский стандарт)
- **Названия компаний**: Финские форматы (Oy, Ab, Tmi)
- **Адреса**: Финский формат адресов
- **Единицы измерения**: Метрическая система
- **Язык интерфейса**: Русский с финскими терминами

### Примеры
- Компания: "Rakennus Virtanen Oy"
- Адрес: "Mannerheimintie 15, 00100 Helsinki"
- Ставка: "32,50 €/час"
- Проект: "Asuntokohde Kallio"

---

*Документ содержит финальную спецификацию команд для точного воспроизведения в новом проекте.*
