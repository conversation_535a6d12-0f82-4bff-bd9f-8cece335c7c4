"""
Обработчики команд администратора (только Reply-клавиатуры)

Управление компаниями, пользователями, токенами и статистика.
"""
from aiogram import Router, types, F
from aiogram.filters import Command
from aiogram.fsm.context import FSMContext
from states import AdminStates
from services.company_service import CompanyService
from services.token_service import TokenService
from services.auth_service import AuthService
from middleware.rbac_middleware import require_permission, require_role
from keyboards.admin import (
    create_admin_menu, create_companies_management_keyboard,
    create_company_actions_keyboard, create_users_management_keyboard,
    create_user_actions_keyboard, create_tokens_management_keyboard,
    create_token_actions_keyboard, create_statistics_keyboard,
    create_system_settings_keyboard
)
from keyboards.common import create_back_keyboard, create_yes_no_keyboard

router = Router()


# ===== ФУНКЦИИ ДЛЯ REPLY-КЛАВИАТУР =====

async def show_companies_management(message: types.Message):
    """Показать меню управления компаниями"""
    keyboard = create_companies_management_keyboard()

    await message.answer(
        "🏢 <b>Управление компаниями</b>\n\n"
        "Выберите действие:",
        reply_markup=keyboard,
        parse_mode="HTML"
    )


async def show_users_management(message: types.Message):
    """Показать меню управления пользователями"""
    keyboard = create_users_management_keyboard()

    await message.answer(
        "👥 <b>Управление пользователями</b>\n\n"
        "Выберите действие:",
        reply_markup=keyboard,
        parse_mode="HTML"
    )


async def show_tokens_management(message: types.Message):
    """Показать меню управления токенами"""
    keyboard = create_tokens_management_keyboard()

    await message.answer(
        "🔗 <b>Управление токенами</b>\n\n"
        "Выберите действие:",
        reply_markup=keyboard,
        parse_mode="HTML"
    )


async def show_statistics(message: types.Message):
    """Показать статистику"""
    keyboard = create_statistics_keyboard()

    await message.answer(
        "📊 <b>Статистика системы</b>\n\n"
        "Выберите раздел:",
        reply_markup=keyboard,
        parse_mode="HTML"
    )


async def show_system_settings(message: types.Message):
    """Показать системные настройки"""
    keyboard = create_system_settings_keyboard()

    await message.answer(
        "🔧 <b>Системные настройки</b>\n\n"
        "Выберите раздел:",
        reply_markup=keyboard,
        parse_mode="HTML"
    )


async def show_admin_menu(message: types.Message):
    """Показать главное меню администратора"""
    keyboard = create_admin_menu()

    await message.answer(
        "👑 <b>Панель администратора</b>\n\n"
        "Добро пожаловать в административную панель!\n"
        "Выберите раздел для управления:",
        reply_markup=keyboard,
        parse_mode="HTML"
    )


# ===== ОБРАБОТЧИКИ REPLY-КНОПОК =====

@router.message(F.text == "➕ Создать компанию")
async def handle_create_company_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Создать компанию'"""
    await message.answer("🚧 Функция создания компании в разработке")


@router.message(F.text == "📋 Список компаний")
async def handle_list_companies_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Список компаний'"""
    companies = await CompanyService.get_all_companies()
    active_companies = [c for c in companies if not c['is_deleted']]
    
    if not active_companies:
        keyboard = create_companies_management_keyboard()
        await message.answer(
            "📋 <b>Список компаний</b>\n\n"
            "Компании не найдены.\n\n"
            "➕ Создайте первую компанию!",
            reply_markup=keyboard,
            parse_mode="HTML"
        )
        return

    companies_text = ""
    for i, company in enumerate(active_companies[:10], 1):
        companies_text += (
            f"{i}. 🏢 <b>{company['name']}</b>\n"
            f"   🆔 ID: <code>{company['id']}</code>\n"
            f"   📅 Создана: {company['created_at'].strftime('%d.%m.%Y')}\n\n"
        )

    keyboard = create_companies_management_keyboard()
    await message.answer(
        f"📋 <b>Список компаний</b>\n\n"
        f"📊 Всего активных: <b>{len(active_companies)}</b>\n\n"
        f"{companies_text}"
        f"{'...' if len(active_companies) > 10 else ''}",
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.message(F.text == "🗑️ Удаленные компании")
async def handle_deleted_companies_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Удаленные компании'"""
    companies = await CompanyService.get_all_companies()
    deleted_companies = [c for c in companies if c['is_deleted']]

    if not deleted_companies:
        keyboard = create_companies_management_keyboard()
        await message.answer(
            "🗂 <b>Удалённые компании</b>\n\n"
            "Удаленных компаний нет.",
            reply_markup=keyboard,
            parse_mode="HTML"
        )
        return

    deleted_text = ""
    for i, company in enumerate(deleted_companies[:10], 1):
        deleted_text += (
            f"{i}. 🗑️ <b>{company['name']}</b>\n"
            f"   🆔 ID: <code>{company['id']}</code>\n"
            f"   📅 Удалена: {company.get('deleted_at', 'Неизвестно')}\n\n"
        )

    keyboard = create_companies_management_keyboard()
    await message.answer(
        f"🗂 <b>Удалённые компании</b>\n\n"
        f"🗑️ Всего удаленных: <b>{len(deleted_companies)}</b>\n\n"
        f"{deleted_text}"
        f"{'...' if len(deleted_companies) > 10 else ''}",
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.message(F.text == "➕ Добавить пользователя")
async def handle_add_user_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Добавить пользователя'"""
    await message.answer("🚧 Функция добавления пользователя в разработке")


@router.message(F.text == "📋 Список директоров")
async def handle_directors_list_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Список директоров'"""
    # Получаем всех пользователей с компаниями
    users = await AuthService.get_all_users_with_companies()
    directors = [u for u in users if u['role'] == 'director']

    if not directors:
        keyboard = create_users_management_keyboard()
        await message.answer(
            "📋 <b>Список директоров</b>\n\n"
            "Директора не найдены.\n\n"
            "💡 Создайте токен для регистрации директора.",
            reply_markup=keyboard,
            parse_mode="HTML"
        )
        return

    directors_text = ""
    for i, director in enumerate(directors[:10], 1):
        company_name = director.get('company_name', 'Без компании')
        directors_text += (
            f"{i}. 👨‍💼 <b>{director.get('first_name', 'Неизвестно')} {director.get('last_name', '')}</b>\n"
            f"   🏢 Компания: <b>{company_name}</b>\n"
            f"   🆔 ID: <code>{director['user_id']}</code>\n"
            f"   📅 Регистрация: {director.get('created_at', 'Неизвестно')}\n\n"
        )

    keyboard = create_users_management_keyboard()
    await message.answer(
        f"📋 <b>Список директоров</b>\n\n"
        f"👨‍💼 Всего директоров: <b>{len(directors)}</b>\n\n"
        f"{directors_text}"
        f"{'...' if len(directors) > 10 else ''}",
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.message(F.text == "👷 Список рабочих")
async def handle_workers_list_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Список рабочих'"""
    # Получаем всех пользователей с компаниями
    users = await AuthService.get_all_users_with_companies()
    workers = [u for u in users if u['role'] == 'worker']

    if not workers:
        keyboard = create_users_management_keyboard()
        await message.answer(
            "👷 <b>Список рабочих</b>\n\n"
            "Рабочие не найдены.\n\n"
            "💡 Создайте токен для регистрации рабочих.",
            reply_markup=keyboard,
            parse_mode="HTML"
        )
        return

    workers_text = ""
    for i, worker in enumerate(workers[:10], 1):
        company_name = worker.get('company_name', 'Без компании')
        workers_text += (
            f"{i}. 👷 <b>{worker.get('first_name', 'Неизвестно')} {worker.get('last_name', '')}</b>\n"
            f"   🏢 Компания: <b>{company_name}</b>\n"
            f"   🆔 ID: <code>{worker['user_id']}</code>\n"
            f"   📅 Регистрация: {worker.get('created_at', 'Неизвестно')}\n\n"
        )

    keyboard = create_users_management_keyboard()
    await message.answer(
        f"👷 <b>Список рабочих</b>\n\n"
        f"👷‍♂️ Всего рабочих: <b>{len(workers)}</b>\n\n"
        f"{workers_text}"
        f"{'...' if len(workers) > 10 else ''}",
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.message(F.text == "📊 Общая статистика")
async def handle_general_statistics_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Общая статистика'"""
    companies = await CompanyService.get_all_companies()
    users = await AuthService.get_all_users_with_companies()
    tokens = await TokenService.get_all_active_tokens()

    active_companies = [c for c in companies if not c['is_deleted']]
    directors = [u for u in users if u['role'] == 'director']
    workers = [u for u in users if u['role'] == 'worker']

    keyboard = create_statistics_keyboard()
    await message.answer(
        f"📊 <b>Общая статистика системы</b>\n\n"
        f"🏢 <b>Компании:</b>\n"
        f"   ✅ Активных: <b>{len(active_companies)}</b>\n"
        f"   🗑️ Удаленных: <b>{len(companies) - len(active_companies)}</b>\n\n"
        f"👥 <b>Пользователи:</b>\n"
        f"   👨‍💼 Директоров: <b>{len(directors)}</b>\n"
        f"   👷 Рабочих: <b>{len(workers)}</b>\n"
        f"   📊 Всего: <b>{len(users)}</b>\n\n"
        f"🔗 <b>Токены:</b>\n"
        f"   ⏰ Активных: <b>{len(tokens)}</b>\n\n"
        f"📅 Обновлено: <b>сейчас</b>",
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.message(F.text == "ℹ️ Информационная панель")
async def handle_info_panel_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Информационная панель'"""
    # Получаем статистику
    companies = await CompanyService.get_all_companies()
    users = await AuthService.get_all_users_with_companies()
    tokens = await TokenService.get_all_active_tokens()

    active_companies = [c for c in companies if not c['is_deleted']]
    directors = [u for u in users if u['role'] == 'director']
    workers = [u for u in users if u['role'] == 'worker']

    keyboard = create_admin_menu()
    await message.answer(
        f"ℹ️ <b>Информационная панель</b>\n\n"
        f"🏢 <b>Компании:</b> {len(active_companies)} активных\n"
        f"👥 <b>Пользователи:</b> {len(users)} (👨‍💼 {len(directors)} + 👷 {len(workers)})\n"
        f"🔗 <b>Токены:</b> {len(tokens)} активных\n\n"
        f"📅 <b>Последнее действие:</b> сейчас\n"
        f"🤖 <b>Версия бота:</b> v2.0 MVP",
        reply_markup=keyboard,
        parse_mode="HTML"
    )
