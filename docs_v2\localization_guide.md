# 🇫🇮 РУКОВОДСТВО ПО ЛОКАЛИЗАЦИИ WORKLOG MVP v2.0 (ФИНЛЯНДИЯ)

## 🎯 Принципы локализации для Финляндии

### Философия текстов v2.0 (Финляндская адаптация)
1. **Единообразие** - одинаковый стиль во всём приложении
2. **Ясность** - понятные и краткие формулировки на русском языке
3. **Контекстность** - тексты соответствуют финляндским реалиям
4. **Эмодзи по смыслу** - иконки помогают понимать действия
5. **Профессиональность** - деловой, но дружелюбный тон
6. **Финляндская специфика** - учет местных особенностей и терминологии

### Целевая аудитория в Финляндии
- **Рабочие** - русскоязычные строители в Финляндии
- **Директора** - руководители строительных компаний
- **Администраторы** - IT-специалисты и системные администраторы

### Особенности финляндской локализации
- **Валюта**: Евро (€) вместо рублей
- **Формат даты**: ДД.ММ.ГГГГ (европейский стандарт)
- **Названия компаний**: Финские форматы (Oy, Ab, Tmi)
- **Адреса**: Финский формат адресов
- **Рабочее время**: Европейские стандарты (37.5-40 часов в неделю)
- **Единицы измерения**: Метрическая система

## 📚 Структура локализации

### Иерархическая организация
```python
TEXTS = {
    "common": {          # Общие тексты для всех ролей
        "confirm": "✅ Подтвердить",
        "cancel": "❌ Отменить",
        "back": "🔙 Назад",
        "save": "💾 Сохранить",
        "delete": "🗑️ Удалить",
        "edit": "✏️ Редактировать",
        "view": "👁️ Просмотреть",
        "add": "➕ Добавить",
        "main_menu": "🏠 Главное меню",
        "loading": "⏳ Загрузка...",
        "success": "✅ Успешно!",
        "error": "❌ Ошибка"
    },

    "roles": {           # Названия ролей
        "worker": "👷 Рабочий",
        "director": "👨‍💼 Директор",
        "admin": "👑 Администратор"
    },

    "worker": {          # Тексты для рабочих
        "menu_title": "👷 Меню рабочего",
        "add_work": "➕ Добавить работу",
        "my_work": "📋 Мои работы",
        "my_projects": "🏗️ Мои проекты",
        "notes": "📝 Заметки",
        "requests": "🛠️ Запросы",
        "profile": "👤 Профиль"
    },

    "director": {        # Тексты для директоров
        "menu_title": "👨‍💼 Меню директора",
        "manage_projects": "🏗️ Управление проектами",
        "manage_workers": "👷 Управление рабочими",
        "reports": "📊 Отчёты",
        "categories": "📋 Категории работ",
        "worker_requests": "🛠️ Запросы рабочих"
    },

    "admin": {           # Тексты для администраторов
        "menu_title": "👑 Меню администратора",
        "manage_companies": "🏢 Управление компаниями",
        "manage_users": "👥 Управление пользователями",
        "system_reports": "📊 Системные отчёты",
        "system_settings": "⚙️ Настройки системы",
        "technical_info": "🔧 Техническая информация"
    }
}
```

### Категории текстов

#### 1. Кнопки и действия
```python
BUTTONS = {
    # Основные действия
    "add_work": "➕ Добавить работу",
    "edit_work": "✏️ Редактировать работу",
    "delete_work": "🗑️ Удалить работу",
    "view_work": "👁️ Просмотреть работу",

    # Проекты
    "create_project": "🏗️ Создать проект",
    "edit_project": "✏️ Редактировать проект",
    "select_project": "🎯 Выбрать проект",
    "my_projects": "🏗️ Мои проекты",

    # Отчёты
    "create_report": "📊 Создать отчёт",
    "export_excel": "📄 Экспорт Excel",
    "export_pdf": "📋 Экспорт PDF",
    "daily_report": "📅 Дневной отчёт",
    "weekly_report": "📅 Недельный отчёт",
    "monthly_report": "📅 Месячный отчёт",

    # Навигация
    "back": "🔙 Назад",
    "main_menu": "🏠 Главное меню",
    "cancel": "❌ Отменить",
    "confirm": "✅ Подтвердить",
    "save": "💾 Сохранить",
    "next": "▶️ Далее",
    "previous": "◀️ Назад",

    # Фильтры и сортировка
    "filter_today": "📅 Сегодня",
    "filter_yesterday": "📅 Вчера",
    "filter_week": "📅 Эта неделя",
    "filter_month": "📅 Этот месяц",
    "filter_custom": "📅 Выбрать период",
    "sort_by_date": "📅 По дате",
    "sort_by_amount": "💰 По сумме",
    "sort_by_project": "🏗️ По проекту"
}
```

#### 2. Сообщения и инструкции
```python
MESSAGES = {
    # Приветствие и старт
    "welcome": {
        "title": "🎉 Добро пожаловать в WorkLog MVP!",
        "description": "Система учёта рабочего времени для строительных компаний.",
        "select_role": "Для начала работы выберите вашу роль:"
    },

    "welcome_back": {
        "title": "👋 С возвращением!",
        "role": "Ваша роль: {role}",
        "company": "Компания: {company_name}",
        "select_action": "Выберите действие:"
    },

    # Добавление работы
    "add_work": {
        "select_project": "🏗️ Выберите проект для записи о работе:",
        "no_projects": "❌ У вас нет доступных проектов. Обратитесь к директору.",
        "select_work_type": "⚙️ Выберите тип работы:\n\n🏗️ Проект: {project_name}",
        "enter_description": (
            "📝 Введите описание выполненной работы:\n\n"
            "🏗️ Проект: {project_name}\n"
            "⚙️ Тип: {work_type}\n\n"
            "Пример: \"Кладка кирпича на 2 этаже\""
        ),
        "enter_quantity": (
            "🔢 Введите количество {unit}:\n\n"
            "🏗️ Проект: {project_name}\n"
            "⚙️ Тип: {work_type}\n"
            "📝 Описание: {description}\n\n"
            "Введите число (например: 8 или 8.5):"
        ),
        "confirm_entry": (
            "✅ Подтвердите запись о работе:\n\n"
            "📅 Дата: {date}\n"
            "🏗️ Проект: {project_name}\n"
            "⚙️ Тип: {work_type} ({rate}₽/{unit})\n"
            "📝 Описание: {description}\n"
            "🔢 Количество: {quantity} {unit}\n"
            "💰 Сумма: {total_amount:,.0f}₽"
        ),
        "success": (
            "🎉 Запись о работе успешно добавлена!\n\n"
            "📊 Ваша статистика за сегодня:\n"
            "• Записей: {entries_count}\n"
            "• Часов: {total_hours}\n"
            "• Сумма: {total_amount:,.0f}₽"
        )
    },

    # Создание проекта
    "create_project": {
        "enter_name": "🏗️ Введите название проекта:\n\nПример: \"Жилой комплекс Солнечный\"",
        "enter_address": (
            "📍 Введите адрес проекта:\n\n"
            "Проект: {project_name}\n\n"
            "Пример: \"ул. Солнечная, д. 15\""
        ),
        "enter_hourly_rate": (
            "💰 Введите почасовую ставку (₽/час):\n\n"
            "Проект: {project_name}\n"
            "Адрес: {address}\n\n"
            "Пример: 600"
        ),
        "enter_piece_rate": (
            "💰 Введите базовую сдельную ставку (₽/единица):\n\n"
            "Проект: {project_name}\n"
            "Почасовая ставка: {hourly_rate}₽/час\n\n"
            "Пример: 100"
        ),
        "add_work_types": (
            "⚙️ Добавление типов работ\n\n"
            "Проект: {project_name}\n\n"
            "Текущие типы работ:\n{work_types_list}\n\n"
            "Хотите добавить ещё один тип работы?"
        ),
        "confirm_project": (
            "✅ Подтвердите создание проекта:\n\n"
            "🏗️ Название: {project_name}\n"
            "📍 Адрес: {address}\n"
            "💰 Почасовая ставка: {hourly_rate}₽/час\n"
            "💰 Сдельная ставка: {piece_rate}₽/ед\n\n"
            "⚙️ Типы работ:\n{work_types_list}\n\n"
            "👷 Назначенные рабочие: {workers_count}"
        ),
        "success": "🎉 Проект \"{project_name}\" успешно создан!"
    }
}
```

#### 3. Ошибки и валидация
```python
ERRORS = {
    # Общие ошибки
    "access_denied": "❌ Доступ запрещён",
    "insufficient_permissions": "❌ Недостаточно прав для выполнения этого действия",
    "invalid_data": "❌ Некорректные данные",
    "operation_failed": "❌ Операция не выполнена. Попробуйте ещё раз.",
    "network_error": "❌ Ошибка сети. Проверьте подключение.",
    "server_error": "❌ Ошибка сервера. Обратитесь к администратору.",

    # Валидация данных
    "required_field": "❌ Поле обязательно для заполнения",
    "invalid_number": "❌ Введите корректное число",
    "invalid_date": "❌ Введите корректную дату",
    "invalid_email": "❌ Введите корректный email",
    "invalid_phone": "❌ Введите корректный номер телефона",

    # Специфичные ошибки
    "work_entry": {
        "description_too_short": "❌ Описание должно содержать минимум 3 символа",
        "description_too_long": "❌ Описание не может быть длиннее 500 символов",
        "invalid_quantity": "❌ Количество должно быть от 0.1 до 24",
        "invalid_rate": "❌ Ставка должна быть больше 0",
        "project_not_found": "❌ Проект не найден",
        "project_not_accessible": "❌ У вас нет доступа к этому проекту"
    },

    "project": {
        "name_too_short": "❌ Название проекта должно содержать минимум 3 символа",
        "name_too_long": "❌ Название проекта не может быть длиннее 100 символов",
        "name_already_exists": "❌ Проект с таким названием уже существует",
        "invalid_rate": "❌ Ставка должна быть положительным числом"
    },

    "user": {
        "not_found": "❌ Пользователь не найден",
        "already_exists": "❌ Пользователь уже зарегистрирован",
        "blocked": "❌ Ваш аккаунт заблокирован. Обратитесь к администратору.",
        "invalid_token": "❌ Недействительный токен регистрации"
    }
}
```

## 🎨 Стилистические правила

### Тон и стиль
```python
# ✅ Правильно - дружелюбный деловой тон
"welcome_message": "👋 Добро пожаловать! Выберите действие:",
"success_message": "🎉 Операция выполнена успешно!",
"error_message": "❌ Произошла ошибка. Попробуйте ещё раз.",

# ❌ Неправильно - слишком формально или фамильярно
"welcome_message": "Приветствуем Вас в системе. Осуществите выбор функции.",
"success_message": "Супер! Всё получилось!",
"error_message": "Упс! Что-то пошло не так :("
```

### Использование эмодзи
```python
# ✅ Правильно - эмодзи по смыслу
EMOJI_RULES = {
    "actions": {
        "add": "➕",        # Добавить
        "edit": "✏️",       # Редактировать
        "delete": "🗑️",     # Удалить
        "view": "👁️",       # Просмотреть
        "save": "💾",       # Сохранить
        "confirm": "✅",    # Подтвердить
        "cancel": "❌"      # Отменить
    },
    "entities": {
        "user": "👤",       # Пользователь
        "project": "🏗️",    # Проект
        "work": "⚙️",       # Работа
        "money": "💰",      # Деньги
        "date": "📅",       # Дата
        "time": "⏰"        # Время
    },
    "status": {
        "success": "✅",    # Успех
        "error": "❌",      # Ошибка
        "warning": "⚠️",    # Предупреждение
        "info": "ℹ️",       # Информация
        "loading": "⏳"     # Загрузка
    }
}

# ❌ Неправильно - избыточные или неуместные эмодзи
"bad_examples": [
    "🎉🎊✨ Супер успех! 🚀🌟💫",  # Слишком много
    "😀 Добавить работу 😊",        # Неуместные лица
    "🔥💯 Крутой проект 🔥💯"       # Сленговые эмодзи
]
```

### Форматирование чисел и дат (Финляндия)
```python
FORMATTING_FINLAND = {
    # Деньги (ЕВРО)
    "money": {
        "format": "{amount:,.2f} €",
        "format_no_cents": "{amount:,.0f} €",
        "examples": ["1 000,00 €", "15 500,50 €", "125 000 €"]
    },

    # Даты (европейский формат)
    "dates": {
        "short": "%d.%m.%Y",           # 27.06.2025
        "long": "%d %B %Y г.",         # 27 июня 2025 г.
        "with_time": "%d.%m.%Y %H:%M", # 27.06.2025 15:30
        "finnish_style": "%d.%m.%Y"    # Финский стиль
    },

    # Время (европейские стандарты)
    "time": {
        "hours": "{hours} ч",          # 8 ч
        "hours_minutes": "{hours}:{minutes:02d}", # 8:30
        "duration": "{hours:.1f} часов", # 8.5 часов
        "decimal_separator": ","       # Европейский разделитель
    },

    # Количество (метрическая система)
    "quantity": {
        "pieces": "{qty} шт",          # 100 шт
        "square_meters": "{qty} м²",   # 50 м²
        "linear_meters": "{qty} м",    # 25 м
        "hours": "{qty} ч",            # 8 ч
        "kilograms": "{qty} кг",       # 100 кг
        "cubic_meters": "{qty} м³"     # 10 м³
    },

    # Финские специфичные форматы
    "finnish_specific": {
        "company_suffix": ["Oy", "Ab", "Tmi", "Ay"],
        "postal_code": "{code:05d}",   # 00100
        "phone": "+358 {area} {number}" # +358 40 1234567
    }
}
```

## 🔧 Функция локализации

### Основная функция get_text
```python
from typing import Optional, Dict, Any
import logging

logger = logging.getLogger(__name__)

def get_text(
    key: str,
    default: Optional[str] = None,
    **kwargs: Any
) -> str:
    """
    Получает локализованный текст по ключу.

    Args:
        key: Ключ текста в формате "category.subcategory.text"
        default: Значение по умолчанию, если ключ не найден
        **kwargs: Параметры для форматирования текста

    Returns:
        str: Локализованный текст

    Examples:
        get_text("common.confirm") -> "✅ Подтвердить"
        get_text("worker.menu_title") -> "👷 Меню рабочего"
        get_text("messages.add_work.success", entries_count=5) -> "🎉 Запись добавлена! Записей: 5"
    """
    try:
        # Разбираем ключ на части
        keys = key.split(".")
        current = TEXTS

        # Проходим по иерархии
        for k in keys:
            if isinstance(current, dict) and k in current:
                current = current[k]
            else:
                # Ключ не найден
                logger.warning(f"Текст не найден для ключа: {key}")
                return default or f"[{key}]"

        # Если найден текст, форматируем его
        if isinstance(current, str):
            try:
                return current.format(**kwargs)
            except KeyError as e:
                logger.error(f"Ошибка форматирования текста '{key}': отсутствует параметр {e}")
                return current
        else:
            logger.warning(f"Ключ '{key}' не указывает на строку")
            return default or f"[{key}]"

    except Exception as e:
        logger.error(f"Ошибка получения текста для ключа '{key}': {e}")
        return default or f"[{key}]"

# Вспомогательные функции
def get_button_text(action: str, entity: str = "") -> str:
    """Получает текст кнопки по действию."""
    key = f"buttons.{action}"
    if entity:
        key += f"_{entity}"
    return get_text(key, default=f"[{action}]")

def get_error_text(error_type: str, context: str = "") -> str:
    """Получает текст ошибки."""
    key = f"errors.{error_type}"
    if context:
        key = f"errors.{context}.{error_type}"
    return get_text(key, default="❌ Произошла ошибка")

def get_role_text(role: str) -> str:
    """Получает название роли."""
    role_key = role.lower().replace("role_", "")
    return get_text(f"roles.{role_key}", default=role)
```

### Использование в коде
```python
# В обработчиках
async def show_worker_menu(callback: CallbackQuery, user: User):
    """Показывает меню рабочего."""
    text = get_text("worker.menu_title")
    keyboard = create_worker_menu_keyboard()

    await callback.message.edit_text(
        text=text,
        reply_markup=keyboard
    )

# В сервисах с параметрами
async def create_work_entry_success(work_entry: WorkEntry, daily_stats: DailyStats):
    """Формирует сообщение об успешном создании записи."""
    return get_text(
        "messages.add_work.success",
        entries_count=daily_stats.entries_count,
        total_hours=daily_stats.total_hours,
        total_amount=daily_stats.total_amount
    )

# В валидаторах
def validate_work_description(description: str) -> None:
    """Валидирует описание работы."""
    if len(description) < 3:
        raise ValidationError(
            get_text("errors.work_entry.description_too_short")
        )

    if len(description) > 500:
        raise ValidationError(
            get_text("errors.work_entry.description_too_long")
        )
```

## 🌐 Подготовка к многоязычности

### Структура для будущих переводов
```python
# Будущая структура для поддержки нескольких языков
LOCALES = {
    "ru": {
        "common": {
            "confirm": "✅ Подтвердить",
            "cancel": "❌ Отменить"
        }
    },
    "en": {
        "common": {
            "confirm": "✅ Confirm",
            "cancel": "❌ Cancel"
        }
    }
}

def get_text(key: str, locale: str = "ru", **kwargs) -> str:
    """Получает текст с учётом локали."""
    # Реализация для многоязычности
    pass
```

### JSON файлы локализации
```json
// locales/ru.json
{
  "common": {
    "confirm": "✅ Подтвердить",
    "cancel": "❌ Отменить",
    "back": "🔙 Назад"
  },
  "worker": {
    "menu_title": "👷 Меню рабочего",
    "add_work": "➕ Добавить работу"
  }
}

// locales/en.json
{
  "common": {
    "confirm": "✅ Confirm",
    "cancel": "❌ Cancel",
    "back": "🔙 Back"
  },
  "worker": {
    "menu_title": "👷 Worker Menu",
    "add_work": "➕ Add Work"
  }
}
```

## ✅ Чек-лист локализации

### Перед релизом
- [ ] Все тексты вынесены в локализацию
- [ ] Нет хардкода текстов в коде
- [ ] Все кнопки имеют единый стиль
- [ ] Эмодзи используются по смыслу
- [ ] Тексты ошибок понятны пользователю
- [ ] Форматирование чисел и дат единообразно
- [ ] Тон сообщений соответствует аудитории
- [ ] Все ключи локализации документированы
- [ ] Fallback значения установлены
- [ ] Логирование отсутствующих ключей работает