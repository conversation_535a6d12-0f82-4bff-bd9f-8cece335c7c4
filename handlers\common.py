"""
Общие обработчики команд (/start, /cancel)

Реализует гибридную систему ролей для админа в режиме разработки.
"""
from aiogram import Router, types, F
from aiogram.filters import Command, CommandStart
from aiogram.fsm.context import FSMContext
# Удален импорт Inline-клавиатур - используем только Reply-клавиатуры

from states import RoleSelectionStates, RegistrationStates, DevModeStates
from services.token_service import TokenService
from services.auth_service import AuthService
from utils.dev_session import is_superuser
from keyboards.dev import get_dev_role_selection_keyboard, get_dev_role_confirmation_keyboard, get_dev_role_info_text, get_dev_success_text
from keyboards.admin import create_admin_menu
from keyboards.director import create_director_menu
from keyboards.worker import create_worker_menu
# from utils.navigation import get_role_from_middleware  # Не используется

router = Router()


@router.message(CommandStart())
async def cmd_start(message: types.Message, state: FSMContext, **kwargs):
    """
    Команда /start с гибридной системой ролей

    Логика:
    1. Проверка токена регистрации (если есть аргументы)
    2. Гибридная система для админа в режиме разработки
    3. Обычная логика для зарегистрированных пользователей
    """
    import logging
    logger = logging.getLogger(__name__)

    user_id = kwargs.get("user_id")
    is_admin = kwargs.get("is_admin", False)
    development_mode = kwargs.get("development_mode", False)

    logger.info(f"🚀 /start команда от пользователя {message.from_user.id}")
    logger.info(f"📊 kwargs: user_id={user_id}, is_admin={is_admin}, development_mode={development_mode}")

    # Если user_id не передан через kwargs, берем из message
    if user_id is None:
        user_id = message.from_user.id
        logger.info(f"🔧 Используем user_id из message: {user_id}")
    
    # Получаем аргументы команды (токен регистрации)
    args = message.text.split()[1:] if len(message.text.split()) > 1 else []
    
    # Проверка токена регистрации
    if args and args[0].startswith("reg_"):
        token = args[0][4:]  # Убираем префикс "reg_"
        await handle_token_registration(message, token, state)
        return
    
    # DEV MODE: Система выбора роли для суперпользователя
    if is_admin and development_mode and is_superuser(user_id):
        await show_dev_role_selection(message, state)
        return

    # Гибридная система для админа в режиме разработки (старая логика)
    elif is_admin and development_mode:
        await show_role_selection_menu(message, state)
        return

    # Обычная логика для зарегистрированных пользователей
    await show_user_main_menu(message, user_id)


async def show_role_selection_menu(message: types.Message, state: FSMContext):
    """Меню выбора роли для админа в режиме разработки"""
    from keyboards.dev import get_dev_role_selection_keyboard
    keyboard = get_dev_role_selection_keyboard()

    await message.answer(
        "🔧 <b>РЕЖИМ РАЗРАБОТКИ</b>\n"
        "Выберите роль для тестирования:",
        reply_markup=keyboard,
        parse_mode="HTML"
    )
    await state.set_state(RoleSelectionStates.waiting_role_selection)


# Удален callback_query обработчик - используется Reply-клавиатура


async def show_role_main_menu(message: types.Message, role: str):
    """Показать главное меню выбранной роли"""
    if role == "admin":
        await show_admin_menu(message)
    elif role == "director":
        await show_director_menu(message)
    elif role == "worker":
        await show_worker_menu(message)


async def show_admin_menu(message: types.Message):
    """Главное меню администратора"""
    keyboard = create_admin_menu()

    await message.answer(
        "👑 <b>Панель администратора</b>\n\n"
        "Добро пожаловать в панель управления системой!\n"
        "Выберите раздел для работы:\n\n"
        "💡 <i>Используйте кнопки меню для навигации</i>",
        reply_markup=keyboard,
        parse_mode="HTML"
    )


async def show_director_menu(message: types.Message):
    """Главное меню директора"""
    keyboard = create_director_menu()

    await message.answer(
        "📍 <b>Главное меню директора</b>\n"
        "👇 Выберите действие:",
        reply_markup=keyboard,
        parse_mode="HTML"
    )


async def show_worker_menu(message: types.Message):
    """Главное меню рабочего"""
    keyboard = create_worker_menu()

    await message.answer(
        "🔧 <b>Главное меню рабочего</b>\n"
        "👇 Выберите действие:",
        reply_markup=keyboard,
        parse_mode="HTML"
    )


async def show_user_main_menu(message: types.Message, user_id: int):
    """Показать главное меню для обычного пользователя"""
    from services.auth_service import AuthService

    # Получаем роль пользователя из базы данных
    user_role = await AuthService.get_user_role(user_id)

    if not user_role:
        # Пользователь не зарегистрирован
        await message.answer(
            "❌ <b>Вы не зарегистрированы в системе</b>\n\n"
            "Для регистрации используйте ссылку-приглашение от администратора или директора компании.\n\n"
            "Формат: <code>/start reg_ТОКЕН</code>",
            parse_mode="HTML"
        )
        return

    # Показываем меню в зависимости от роли
    if user_role == "admin":
        await show_admin_menu(message)
    elif user_role == "director":
        await show_director_menu(message)
    elif user_role == "worker":
        await show_worker_menu(message)
    else:
        await message.answer(
            f"❌ <b>Неизвестная роль: {user_role}</b>\n\n"
            "Обратитесь к администратору для исправления.",
            parse_mode="HTML"
        )


async def handle_token_registration(message: types.Message, token: str, state: FSMContext):
    """Обработка регистрации по токену"""
    user_id = message.from_user.id

    # Проверяем, не зарегистрирован ли уже пользователь
    existing_user = await AuthService.get_user(user_id)
    if existing_user:
        await message.answer(
            "✅ Вы уже зарегистрированы в системе!\n"
            "Используйте /start для доступа к функциям бота."
        )
        return

    # Валидируем токен
    token_data = await TokenService.validate_token(token)
    if not token_data:
        await message.answer(
            "❌ Токен регистрации недействителен или истек.\n"
            "Обратитесь к администратору за новым токеном."
        )
        return

    # Запрашиваем отображаемое имя
    await message.answer(
        f"🎉 Добро пожаловать в <b>{token_data['company_name']}</b>!\n\n"
        f"Ваша роль: <b>{token_data['role']}</b>\n\n"
        f"Пожалуйста, введите ваше имя для отображения в системе:",
        parse_mode="HTML"
    )

    # Сохраняем данные токена в состоянии
    await state.update_data(
        token_value=token,
        token_data=token_data
    )
    await state.set_state(RegistrationStates.waiting_display_name)


@router.message(RegistrationStates.waiting_display_name)
async def handle_display_name(message: types.Message, state: FSMContext):
    """Обработка ввода отображаемого имени при регистрации"""
    display_name = message.text.strip()

    if not display_name or len(display_name) < 2:
        await message.answer(
            "❌ Имя должно содержать минимум 2 символа.\n"
            "Пожалуйста, введите корректное имя:"
        )
        return

    if len(display_name) > 50:
        await message.answer(
            "❌ Имя слишком длинное (максимум 50 символов).\n"
            "Пожалуйста, введите более короткое имя:"
        )
        return

    # Получаем данные из состояния
    data = await state.get_data()
    token_value = data.get("token_value")
    token_data = data.get("token_data")

    if not token_value or not token_data:
        await message.answer(
            "❌ Ошибка регистрации. Попробуйте еще раз с новым токеном."
        )
        await state.clear()
        return

    user_id = message.from_user.id

    # Создаем пользователя
    success = await AuthService.create_user(
        user_id=user_id,
        display_name=display_name,
        role=token_data["role"],
        company_id=token_data["company_id"]
    )

    if not success:
        await message.answer(
            "❌ Ошибка при создании пользователя. Возможно, вы уже зарегистрированы."
        )
        await state.clear()
        return

    # Отмечаем токен как использованный
    await TokenService.use_token(token_value, user_id)

    # Очищаем состояние
    await state.clear()

    # Отправляем приветственное сообщение
    role_names = {
        "director": "👨‍💼 Директор",
        "worker": "👷 Рабочий"
    }

    await message.answer(
        f"🎉 <b>Регистрация завершена!</b>\n\n"
        f"👤 Имя: <b>{display_name}</b>\n"
        f"🏢 Компания: <b>{token_data['company_name']}</b>\n"
        f"👔 Роль: <b>{role_names.get(token_data['role'], token_data['role'])}</b>\n\n"
        f"Теперь вы можете использовать все функции бота!\n"
        f"Используйте /start для доступа к главному меню.",
        parse_mode="HTML"
    )


@router.message(Command("cancel"))
async def cmd_cancel(message: types.Message, state: FSMContext):
    """Команда отмены текущего действия"""
    current_state = await state.get_state()
    
    if current_state is None:
        await message.answer("❌ Нет активных действий для отмены.")
        return
    
    await state.clear()
    await message.answer(
        "✅ Действие отменено.\n"
        "Используйте /start для возврата в главное меню."
    )


# ==================== DEV MODE HANDLERS ====================

async def show_dev_role_selection(message: types.Message, state: FSMContext):
    """Показывает меню выбора роли для суперпользователя"""
    await state.set_state(DevModeStates.selecting_role)

    text = (
        "🧪 <b>DEV MODE - Выбор роли для тестирования</b>\n\n"
        "Выберите роль, которую хотите протестировать:\n\n"
        "🧱 <b>Рабочий</b> - добавление записей, просмотр своих данных\n"
        "📋 <b>Директор</b> - управление проектами, рабочими, отчеты\n"
        "👑 <b>Администратор</b> - полный доступ к системе\n\n"
        "⚠️ <i>Используются виртуальные права и фиктивные данные</i>"
    )

    await message.answer(
        text,
        reply_markup=get_dev_role_selection_keyboard(),
        parse_mode="HTML"
    )


# Удалены все callback_query обработчики - используются только Reply-клавиатуры


# ===== ОБРАБОТЧИКИ REPLY-КНОПОК ДЛЯ DEV-РЕЖИМА =====

@router.message(F.text == "🧱 Рабочий", DevModeStates.selecting_role)
async def handle_dev_worker_button(message: types.Message, state: FSMContext, **kwargs):
    """Обработчик кнопки 'Рабочий' в dev-режиме"""
    await state.update_data(selected_role="worker")

    text = get_dev_role_info_text("worker")
    await message.answer(
        text,
        reply_markup=get_dev_role_confirmation_keyboard("worker"),
        parse_mode="HTML"
    )
    await state.set_state(DevModeStates.role_selected)


@router.message(F.text == "📋 Директор", DevModeStates.selecting_role)
async def handle_dev_director_button(message: types.Message, state: FSMContext, **kwargs):
    """Обработчик кнопки 'Директор' в dev-режиме"""
    await state.update_data(selected_role="director")

    text = get_dev_role_info_text("director")
    await message.answer(
        text,
        reply_markup=get_dev_role_confirmation_keyboard("director"),
        parse_mode="HTML"
    )
    await state.set_state(DevModeStates.role_selected)


@router.message(F.text == "👑 Администратор", DevModeStates.selecting_role)
async def handle_dev_admin_button(message: types.Message, state: FSMContext, **kwargs):
    """Обработчик кнопки 'Администратор' в dev-режиме"""
    await state.update_data(selected_role="admin")

    text = get_dev_role_info_text("admin")
    await message.answer(
        text,
        reply_markup=get_dev_role_confirmation_keyboard("admin"),
        parse_mode="HTML"
    )
    await state.set_state(DevModeStates.role_selected)


@router.message(F.text == "✅ Подтвердить", DevModeStates.role_selected)
async def handle_dev_confirm_button(message: types.Message, state: FSMContext, **kwargs):
    """Обработчик кнопки 'Подтвердить' в dev-режиме"""
    data = await state.get_data()
    role = data.get("selected_role")

    if not role:
        await message.answer("❌ Ошибка: роль не выбрана")
        return

    user_id = message.from_user.id

    # Получаем middleware из kwargs
    rbac_middleware = kwargs.get('rbac_middleware')
    if rbac_middleware and hasattr(rbac_middleware, 'set_selected_role'):
        rbac_middleware.set_selected_role(user_id, role)

    # Сохраняем в состоянии
    await state.update_data(dev_mode=True, role=role)
    await state.clear()

    # Показываем успешное сообщение
    text = get_dev_success_text(role)
    await message.answer(text, parse_mode="HTML")

    # Показываем главное меню выбранной роли
    await show_role_main_menu(message, role)


@router.message(F.text == "🔄 Выбрать другую роль", DevModeStates.role_selected)
async def handle_dev_select_again_button(message: types.Message, state: FSMContext):
    """Обработчик кнопки 'Выбрать другую роль'"""
    await show_dev_role_selection(message, state)


@router.message(F.text == "❌ Отмена", DevModeStates.selecting_role)
@router.message(F.text == "❌ Отмена", DevModeStates.role_selected)
async def handle_dev_cancel_button(message: types.Message, state: FSMContext):
    """Обработчик кнопки 'Отмена' в dev-режиме"""
    await state.clear()
    await message.answer("❌ Выбор роли отменен")


# ===== ОБРАБОТЧИКИ REPLY-КНОПОК =====

@router.message(F.text == "👑 Админ панель")
async def handle_admin_panel_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Админ панель'"""
    await show_admin_menu(message)


@router.message(F.text == "🏢 Управление компаниями")
async def handle_companies_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Управление компаниями'"""
    from handlers.admin import show_companies_management
    await show_companies_management(message)


@router.message(F.text == "👥 Управление пользователями")
async def handle_users_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Управление пользователями'"""
    from handlers.admin import show_users_management
    await show_users_management(message)


@router.message(F.text == "🔗 Управление токенами")
async def handle_tokens_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Управление токенами'"""
    from handlers.admin import show_tokens_management
    await show_tokens_management(message)


@router.message(F.text == "📊 Статистика")
async def handle_statistics_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Статистика'"""
    from handlers.admin import show_statistics
    await show_statistics(message)


@router.message(F.text == "🔧 Системные настройки")
async def handle_settings_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Системные настройки'"""
    from handlers.admin import show_system_settings
    await show_system_settings(message)


# Обработчики для директора
@router.message(F.text == "👷 Управление рабочими")
async def handle_workers_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Управление рабочими'"""
    from handlers.director import show_workers_management
    await show_workers_management(message)


@router.message(F.text == "📊 Просмотр отчётов")
async def handle_reports_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Просмотр отчётов'"""
    from handlers.director import show_reports
    await show_reports(message)


@router.message(F.text == "📁 Экспорт / Импорт данных")
async def handle_export_import_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Экспорт / Импорт данных'"""
    from handlers.director import show_export_import
    await show_export_import(message)


@router.message(F.text == "🛠️ Редактирование типов работ")
async def handle_work_types_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Редактирование типов работ'"""
    from handlers.director import show_work_types
    await show_work_types(message)


@router.message(F.text == "ℹ️ Информация")
async def handle_info_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Информация'"""
    from handlers.director import show_info
    await show_info(message)


# Обработчики для рабочего
@router.message(F.text == "➕ Добавить работу")
async def handle_add_work_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Добавить работу'"""
    # TODO: Реализовать добавление работы
    await message.answer("🚧 Функция добавления работы в разработке")


@router.message(F.text == "📋 Мои записи")
async def handle_my_entries_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Мои записи'"""
    # TODO: Реализовать просмотр записей
    await message.answer("🚧 Функция просмотра записей в разработке")


@router.message(F.text == "✏️ Редактировать")
async def handle_edit_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Редактировать'"""
    # TODO: Реализовать редактирование
    await message.answer("🚧 Функция редактирования в разработке")


@router.message(F.text == "🗑️ Удалить")
async def handle_delete_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Удалить'"""
    # TODO: Реализовать удаление
    await message.answer("🚧 Функция удаления в разработке")


@router.message(F.text == "📊 Отчеты")
async def handle_worker_reports_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Отчеты' для рабочего"""
    # TODO: Реализовать отчеты
    await message.answer("🚧 Функция отчетов в разработке")


@router.message(F.text == "📤 Экспорт")
async def handle_export_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Экспорт'"""
    from handlers.export import show_export_menu
    await show_export_menu(message)


@router.message(F.text == "🏗️ Проекты")
async def handle_projects_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Проекты'"""
    from handlers.project import show_projects_menu
    await show_projects_menu(message)


@router.message(F.text == "👤 Профиль")
async def handle_profile_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Профиль'"""
    # TODO: Реализовать профиль
    await message.answer("🚧 Функция профиля в разработке")


@router.message(F.text == "ℹ️ Помощь")
async def handle_help_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Помощь'"""
    await message.answer(
        "❓ <b>Помощь по боту</b>\n\n"
        "🔧 Используйте кнопки меню для навигации\n"
        "📝 Для добавления работы нажмите 'Добавить работу'\n"
        "📋 Для просмотра записей нажмите 'Мои записи'\n"
        "📊 Для отчетов нажмите 'Отчеты'\n\n"
        "💡 Если возникли проблемы, обратитесь к администратору",
        parse_mode="HTML"
    )


# Общие обработчики
@router.message(F.text == "🔙 Назад")
async def handle_back_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Назад'"""
    user_id = message.from_user.id

    # Простая логика: всегда возвращаемся в главное меню роли
    if is_superuser(user_id):
        # Для суперпользователя получаем выбранную роль из middleware
        rbac_middleware = kwargs.get('rbac_middleware')
        role = None
        if rbac_middleware and hasattr(rbac_middleware, 'get_selected_role'):
            role = rbac_middleware.get_selected_role(user_id)
        if role:
            await show_role_main_menu(message, role)
        else:
            # Если роль не выбрана, показываем выбор роли
            state = kwargs.get('state')
            await cmd_start(message, state, **{k: v for k, v in kwargs.items() if k != 'state'})
    else:
        # Для обычных пользователей получаем роль из базы данных
        from services.auth_service import AuthService
        user_role = await AuthService.get_user_role(user_id)
        if user_role:
            await show_role_main_menu(message, user_role)
        else:
            await message.answer(
                "🔙 Возврат в главное меню\n\n"
                "💡 Используйте команду /start для перехода в главное меню",
                parse_mode="HTML"
            )


@router.message(F.text == "🔙 Назад в меню")
async def handle_back_to_menu_button(message: types.Message, **kwargs):
    """Обработчик кнопки 'Назад в меню'"""
    await handle_back_button(message, **kwargs)


# ===== ВСЕ CALLBACK-ОБРАБОТЧИКИ УДАЛЕНЫ =====
# Используются только Reply-клавиатуры согласно требованиям проекта