"""
Обработчики для рабочего (адаптировано из examples под aiogram v3).

Главное меню рабочего с Reply-клавиатурами согласно role_worker.md:
- 📝 Добавить работу
- 📋 Мои записи  
- 📊 Мой отчёт
- 🏗️ Выбрать проект
- ➕ Новый проект
- ✏️ Редактировать проект
- 📤 Экспорт данных
- ℹ️ Инфо

Все обработчики используют только Reply-клавиатуры и интегрированы с RBAC.
"""
import logging
from aiogram import Router, F, types
from aiogram.fsm.context import FSMContext
from aiogram.filters import Command

from db.session import get_session
from db.models import User, Project, WorkType
from services.work_entry_service import WorkEntryService
from services.worker_report_service import WorkerReportService
from services.project_service import ProjectService
from services.calculator import format_currency
from keyboards.worker import create_worker_menu, create_list_filter_keyboard
from keyboards.common import create_main_menu, create_back_keyboard
from middleware.rbac_middleware import require_permission
from states import WorkerListStates, WorkerProjectStates
from utils.exceptions import ValidationError, NotFoundError

logger = logging.getLogger(__name__)

# Создаем роутер для aiogram v3
router = Router()


@router.message(F.text == "📝 Добавить работу")
@require_permission("can_add_work")
async def handle_add_work_button(message: types.Message, state: FSMContext, **kwargs):
    """
    Обработка кнопки "📝 Добавить работу".
    Запускает FSM сценарий добавления работы.
    """
    user_id = message.from_user.id
    
    try:
        async with get_session() as session:
            # Проверяем наличие активного проекта
            user = await session.get(User, user_id)
            if not user or not user.active_project_id:
                await message.answer(
                    "❌ У вас нет активного проекта.\n"
                    "Выберите проект через кнопку '🏗️ Выбрать проект' или создайте новый.",
                    reply_markup=create_worker_menu()
                )
                return
        
        # Переходим к обработчику добавления работы
        from handlers.work_entry import start_add_work_flow
        await start_add_work_flow(message, state)
        
    except Exception as e:
        logger.error(f"Ошибка при запуске добавления работы для user {user_id}: {e}")
        await message.answer(
            "❌ Произошла ошибка. Попробуйте позже.",
            reply_markup=create_worker_menu()
        )


@router.message(F.text == "📋 Мои записи")
@require_permission("can_list_own_entries")
async def handle_list_work_button(message: types.Message, state: FSMContext, **kwargs):
    """
    Обработка кнопки "📋 Мои записи".
    Показывает фильтры для просмотра записей.
    """
    await state.clear()

    keyboard = create_list_filter_keyboard()

    await message.answer(
        "📋 Выберите фильтр для просмотра записей:",
        reply_markup=keyboard
    )
    await state.set_state(WorkerListStates.selecting_filter)


@router.message(F.text == "📊 Мой отчёт")
@require_permission("can_view_own_reports")
async def handle_worker_report_button(message: types.Message, state: FSMContext, **kwargs):
    """
    Обработка кнопки "📊 Мой отчёт".
    Генерирует отчет рабочего.
    """
    user_id = message.from_user.id
    
    try:
        await message.answer("⏳ Генерирую отчет...")
        
        async with get_session() as session:
            # Получаем компанию пользователя
            user = await session.get(User, user_id)
            company_id = user.active_company_id if user else None
            
            # Генерируем отчет
            report = await WorkerReportService.generate_worker_report(
                session=session,
                user_id=user_id,
                company_id=company_id
            )
            
            # Формируем текст отчета
            stats = report["statistics"]
            text_lines = [
                "📊 **МОЙ ОТЧЁТ**",
                "",
                f"📈 **Общая статистика:**",
                f"• Всего записей: {stats['total_entries']}",
                f"• Общая сумма: {stats['total_sum_formatted']}",
                f"• Средняя сумма за запись: {stats['average_sum_formatted']}",
                f"• Типов работ: {stats['work_types_count']}",
                ""
            ]
            
            # Добавляем топ проектов
            if report["projects"]:
                text_lines.append("🏗️ **Топ проектов:**")
                for i, project in enumerate(report["projects"][:3], 1):
                    text_lines.append(
                        f"{i}. {project['project_name']}: {project['total_sum_formatted']} "
                        f"({project['entries_count']} записей)"
                    )
                text_lines.append("")
            
            # Добавляем топ типов работ
            if report["work_types"]:
                text_lines.append("🔧 **Топ типов работ:**")
                for i, work_type in enumerate(report["work_types"][:3], 1):
                    text_lines.append(
                        f"{i}. {work_type['work_type_name']}: {work_type['total_sum_formatted']} "
                        f"({work_type['total_quantity_formatted']})"
                    )
            
            await message.answer(
                "\n".join(text_lines),
                reply_markup=create_worker_menu(),
                parse_mode="Markdown"
            )
            
    except Exception as e:
        logger.error(f"Ошибка генерации отчета для user {user_id}: {e}")
        await message.answer(
            "❌ Ошибка при генерации отчета. Попробуйте позже.",
            reply_markup=create_worker_menu()
        )


@router.message(F.text == "🏗️ Выбрать проект")
@require_permission("can_manage_projects")
async def handle_select_project_button(message: types.Message, state: FSMContext, **kwargs):
    """
    Обработка кнопки "🏗️ Выбрать проект".
    Показывает список доступных проектов.
    """
    user_id = message.from_user.id
    
    try:
        async with get_session() as session:
            # Получаем проекты пользователя
            user = await session.get(User, user_id)
            if not user or not user.active_company_id:
                await message.answer(
                    "❌ Вы не привязаны к компании.",
                    reply_markup=create_worker_menu()
                )
                return
            
            projects = await ProjectService.get_company_projects(
                session=session,
                company_id=user.active_company_id
            )
            
            if not projects:
                await message.answer(
                    "📁 У вашей компании пока нет проектов.\n"
                    "Создайте новый проект через кнопку '➕ Новый проект'.",
                    reply_markup=create_worker_menu()
                )
                return
            
            # Создаем клавиатуру с проектами
            from keyboards.worker import create_project_selection_keyboard
            keyboard = create_project_selection_keyboard(projects, user.active_project_id)
            
            await message.answer(
                "🏗️ Выберите проект:\n\n"
                "✅ - текущий активный проект",
                reply_markup=keyboard
            )
            
            # Сохраняем проекты в состояние для обработки выбора
            projects_map = {project.name: project.project_id for project in projects}
            await state.update_data(projects_map=projects_map)
            await state.set_state(WorkerProjectStates.selecting_project)
            
    except Exception as e:
        logger.error(f"Ошибка получения проектов для user {user_id}: {e}")
        await message.answer(
            "❌ Ошибка при загрузке проектов. Попробуйте позже.",
            reply_markup=create_worker_menu()
        )


@router.message(F.text == "➕ Новый проект")
@require_permission("can_manage_projects")
async def handle_new_project_button(message: types.Message, state: FSMContext, **kwargs):
    """
    Обработка кнопки "➕ Новый проект".
    Запускает FSM создания проекта.
    """
    # Переходим к обработчику создания проекта
    from handlers.worker_project import start_new_project_flow
    await start_new_project_flow(message, state)


@router.message(F.text == "✏️ Редактировать проект")
@require_permission("can_manage_projects")
async def handle_edit_project_button(message: types.Message, state: FSMContext, **kwargs):
    """
    Обработка кнопки "✏️ Редактировать проект".
    Показывает проекты для редактирования.
    """
    await message.answer(
        "🚧 Функция редактирования проектов будет доступна в следующем обновлении.",
        reply_markup=create_worker_menu()
    )


@router.message(F.text == "📤 Экспорт данных")
@require_permission("can_export_own_data")
async def handle_export_button(message: types.Message, state: FSMContext, **kwargs):
    """
    Обработка кнопки "📤 Экспорт данных".
    Запускает экспорт данных рабочего.
    """
    # Переходим к обработчику экспорта
    from handlers.export import start_worker_export_flow
    await start_worker_export_flow(message, state)


@router.message(F.text == "ℹ️ Инфо")
async def handle_info_button(message: types.Message, state: FSMContext, **kwargs):
    """
    Обработка кнопки "ℹ️ Инфо".
    Показывает информацию о боте и пользователе.
    """
    user_id = message.from_user.id
    
    try:
        async with get_session() as session:
            user = await session.get(User, user_id)
            
            # Получаем статистику
            stats = await WorkerReportService.get_worker_statistics(
                session=session,
                user_id=user_id,
                company_id=user.active_company_id if user else None
            )
            
            # Формируем информационное сообщение
            text_lines = [
                "ℹ️ **ИНФОРМАЦИЯ**",
                "",
                f"👤 **Пользователь:** {user.display_name or 'Не указано'}",
                f"🏢 **Компания:** {user.active_company.name if user and user.active_company else 'Не указана'}",
                f"🏗️ **Активный проект:** {user.active_project.name if user and user.active_project else 'Не выбран'}",
                "",
                "📊 **Ваша статистика:**",
                f"• Всего записей: {stats['total_entries']}",
                f"• Общая сумма: {stats['total_sum_formatted']}",
                f"• Проектов: {stats['projects_count']}",
                f"• За этот месяц: {stats['month_sum_formatted']}",
                "",
                "🤖 **Worklog Bot v2.0** (Финляндия)",
                "Система учета рабочего времени"
            ]
            
            await message.answer(
                "\n".join(text_lines),
                reply_markup=create_worker_menu(),
                parse_mode="Markdown"
            )
            
    except Exception as e:
        logger.error(f"Ошибка получения информации для user {user_id}: {e}")
        await message.answer(
            "ℹ️ **ИНФОРМАЦИЯ**\n\n"
            "🤖 **Worklog Bot v2.0** (Финляндия)\n"
            "Система учета рабочего времени\n\n"
            "❌ Не удалось загрузить статистику.",
            reply_markup=create_worker_menu(),
            parse_mode="Markdown"
        )


@router.message(F.text == "🔙 Назад")
async def handle_back_button(message: types.Message, state: FSMContext, **kwargs):
    """
    Обработка кнопки "🔙 Назад".
    Возвращает в главное меню рабочего.
    """
    await state.clear()
    await message.answer(
        "👷 Главное меню рабочего:",
        reply_markup=create_worker_menu()
    )


# Обработчики состояний для списка записей
@router.message(WorkerListStates.selecting_filter)
async def process_list_filter_selection(message: types.Message, state: FSMContext, **kwargs):
    """Обработка выбора фильтра для списка записей."""
    choice = message.text
    user_id = message.from_user.id
    
    if choice == "🔙 Назад":
        await state.clear()
        await message.answer(
            "👷 Главное меню рабочего:",
            reply_markup=create_worker_menu()
        )
        return
    
    try:
        async with get_session() as session:
            if choice == "Все записи":
                # Показываем все записи
                entries = await WorkEntryService.get_user_work_entries(
                    session=session,
                    user_id=user_id,
                    limit=10
                )
                await show_entries_list(message, entries, "за всё время")
                
            elif choice == "За период":
                # Запрашиваем период
                keyboard = create_back_keyboard()

                await message.answer(
                    "📅 Введите период в формате ДД.ММ.ГГГГ-ДД.ММ.ГГГГ\n"
                    "Например: 01.01.2024-31.01.2024",
                    reply_markup=keyboard
                )
                await state.set_state(WorkerListStates.entering_date_range)
                return
            else:
                await message.answer(
                    "❌ Неверный выбор. Попробуйте еще раз.",
                    reply_markup=create_worker_menu()
                )
        
        await state.clear()
        
    except Exception as e:
        logger.error(f"Ошибка обработки фильтра списка для user {user_id}: {e}")
        await message.answer(
            "❌ Ошибка при загрузке записей.",
            reply_markup=create_worker_menu()
        )
        await state.clear()


async def show_entries_list(message: types.Message, entries: list, period_text: str):
    """Показывает список записей пользователю."""
    if not entries:
        await message.answer(
            f"📋 Записи {period_text} не найдены.",
            reply_markup=create_worker_menu()
        )
        return
    
    text_lines = [
        f"📋 **Записи {period_text}**",
        f"Найдено записей: {len(entries)}",
        ""
    ]
    
    total_sum = 0
    for i, entry in enumerate(entries[:10], 1):  # Показываем первые 10
        date_str = entry.date.strftime("%d.%m.%Y")
        work_type_name = entry.work_type.name if entry.work_type else "Неизвестно"
        amount_str = format_currency(entry.calculated_amount)
        
        text_lines.append(
            f"**{i}.** {date_str} - {work_type_name}\n"
            f"   {entry.description[:50]}{'...' if len(entry.description) > 50 else ''}\n"
            f"   {amount_str}"
        )
        total_sum += float(entry.calculated_amount)
    
    if len(entries) > 10:
        text_lines.append(f"\n... и еще {len(entries) - 10} записей")
    
    text_lines.append(f"\n💰 **Итого:** {format_currency(total_sum)}")
    
    await message.answer(
        "\n".join(text_lines),
        reply_markup=create_worker_menu(),
        parse_mode="Markdown"
    )
