"""
Модели базы данных для Worklog Bot.

Содержит все SQLAlchemy модели для работы с пользователями, проектами,
записями о работе, компаниями и системой RBAC.

Основные таблицы:
- users - пользователи Telegram
- companies - компании (мультикомпании)
- user_company_roles - роли пользователей в компаниях (RBAC)
- projects - проекты работ
- work_types - типы работ в проектах
- work_entries - записи о выполненной работе
- tokens - токены регистрации (24 часа)

Связи:
- User.active_project → Project
- Project.company → Company
- WorkType.project → Project
- WorkEntry.work_type → WorkType
- WorkEntry.project → Project
- UserCompanyRole.user → User
- UserCompanyRole.company → Company
"""
from sqlalchemy import String, Integer, Float, ForeignKey, UniqueConstraint, Date, Numeric, Boolean, DateTime, BigInteger, select
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column, relationship
from typing import Optional, List
import datetime
import secrets


def utc_now() -> datetime.datetime:
    """Возвращает текущее время в UTC с timezone."""
    return datetime.datetime.now(datetime.timezone.utc)


# Базовый класс для всех моделей
class Base(DeclarativeBase):
    """Базовый класс для всех моделей."""
    pass


# Модель пользователя
class User(Base):
    """
    Пользователи Telegram бота.
    
    Поля:
    - user_id: Telegram ID (первичный ключ)
    - active_project_id: ID активного проекта (может быть NULL)
    - created_at: Дата регистрации
    
    Связи:
    - active_project: Активный проект пользователя
    - roles: Роли в компаниях через UserCompanyRole
    """
    __tablename__ = "users"
    
    user_id: Mapped[int] = mapped_column(BigInteger, primary_key=True)  # Telegram ID
    active_project_id: Mapped[Optional[int]] = mapped_column(ForeignKey("projects.project_id"), nullable=True)
    created_at: Mapped[datetime.datetime] = mapped_column(DateTime, default=utc_now)

    # Связи
    active_project: Mapped[Optional["Project"]] = relationship(foreign_keys=[active_project_id])
    roles: Mapped[List["UserCompanyRole"]] = relationship(back_populates="user", cascade="all, delete-orphan")

    # Методы для работы с ролями
    async def get_roles_in_companies(self, session) -> List["UserCompanyRole"]:
        """Получает все роли пользователя в компаниях."""
        result = await session.execute(
            select(UserCompanyRole).where(UserCompanyRole.user_id == self.user_id)
        )
        return list(result.scalars().all())

    async def has_role_in_company(self, session, company_id: int, role: str) -> bool:
        """Проверяет, есть ли у пользователя роль в компании."""
        result = await session.execute(
            select(UserCompanyRole).where(
                UserCompanyRole.user_id == self.user_id,
                UserCompanyRole.company_id == company_id,
                UserCompanyRole.role == role
            )
        )
        return result.scalar_one_or_none() is not None


# Модель компании
class Company(Base):
    """
    Компании для мультикомпанийной системы.
    
    Поля:
    - id: Автоинкремент ID
    - name: Название компании (уникальное)
    - created_at: Дата создания
    
    Связи:
    - user_roles: Роли пользователей в компании
    - projects: Проекты компании
    - tokens: Токены регистрации компании
    """
    __tablename__ = "companies"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    name: Mapped[str] = mapped_column(String(200), nullable=False, unique=True)
    created_at: Mapped[datetime.datetime] = mapped_column(DateTime, default=utc_now)

    # Связи
    user_roles: Mapped[List["UserCompanyRole"]] = relationship(back_populates="company", cascade="all, delete-orphan")
    projects: Mapped[List["Project"]] = relationship(back_populates="company")
    tokens: Mapped[List["Token"]] = relationship(back_populates="company", cascade="all, delete-orphan")


# Модель ролей пользователей в компаниях (RBAC)
class UserCompanyRole(Base):
    """
    Роли пользователей в компаниях (RBAC система).
    
    Поля:
    - id: Автоинкремент ID
    - user_id: Telegram ID пользователя
    - company_id: ID компании
    - role: Роль ('director' | 'worker')
    - created_at: Дата назначения роли
    
    Ограничения:
    - Уникальная пара (user_id, company_id, role)
    """
    __tablename__ = "user_company_roles"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    user_id: Mapped[int] = mapped_column(BigInteger, ForeignKey("users.user_id", ondelete="CASCADE"), nullable=False)
    company_id: Mapped[int] = mapped_column(ForeignKey("companies.id", ondelete="CASCADE"), nullable=False)
    role: Mapped[str] = mapped_column(String(50), nullable=False)  # 'director' | 'worker'
    created_at: Mapped[datetime.datetime] = mapped_column(DateTime, default=utc_now)

    # Связи
    user: Mapped["User"] = relationship(back_populates="roles")
    company: Mapped["Company"] = relationship(back_populates="user_roles")

    __table_args__ = (UniqueConstraint("user_id", "company_id", "role", name="unique_user_company_role"),)


# Модель проекта
class Project(Base):
    """
    Проекты работ.
    
    Поля:
    - project_id: Автоинкремент ID
    - created_by: Telegram ID создателя
    - company_id: ID компании (для изоляции данных)
    - name: Название проекта
    - address: Адрес проекта (опционально)
    - created_at: Дата создания
    
    Связи:
    - company: Компания проекта
    - creator: Создатель проекта
    - work_types: Типы работ в проекте
    - work_entries: Записи о работе в проекте
    
    Ограничения:
    - Уникальное название в рамках компании
    """
    __tablename__ = "projects"
    
    project_id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    created_by: Mapped[int] = mapped_column(BigInteger, ForeignKey("users.user_id"))
    company_id: Mapped[int] = mapped_column(ForeignKey("companies.id", ondelete="CASCADE"), nullable=False)
    name: Mapped[str] = mapped_column(String(100))
    address: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    created_at: Mapped[datetime.datetime] = mapped_column(default=utc_now)

    # Связи
    company: Mapped["Company"] = relationship(back_populates="projects")
    creator: Mapped["User"] = relationship(foreign_keys=[created_by])
    work_types: Mapped[List["WorkType"]] = relationship(back_populates="project", cascade="all, delete-orphan")
    work_entries: Mapped[List["WorkEntry"]] = relationship(back_populates="project", cascade="all, delete-orphan")

    __table_args__ = (UniqueConstraint("company_id", "name", name="unique_project_name_per_company"),)


# Модель типов работ
class WorkType(Base):
    """
    Типы работ в проектах.
    
    Поля:
    - id: Автоинкремент ID
    - project_id: ID проекта
    - name: Название типа работы
    - unit: Единица измерения (час, м², шт и т.д.)
    - rate_type: Тип ставки ('fixed' | 'per_unit')
    - value: Значение ставки
    - hourly_rate: Почасовая ставка (опционально)
    - created_at: Дата создания
    
    Связи:
    - project: Проект типа работы
    - work_entries: Записи с этим типом работы
    
    Ограничения:
    - Уникальное название в рамках проекта
    """
    __tablename__ = "work_types"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    project_id: Mapped[int] = mapped_column(ForeignKey("projects.project_id", ondelete="CASCADE"))
    name: Mapped[str] = mapped_column(String(100))
    unit: Mapped[str] = mapped_column(String(20))
    rate_type: Mapped[str] = mapped_column(String(50))  # 'fixed' | 'per_unit'
    value: Mapped[float] = mapped_column(Numeric(10, 2))
    hourly_rate: Mapped[Optional[float]] = mapped_column(Numeric(10, 2), nullable=True)
    created_at: Mapped[datetime.datetime] = mapped_column(default=utc_now)

    # Связи
    project: Mapped["Project"] = relationship(back_populates="work_types")
    work_entries: Mapped[List["WorkEntry"]] = relationship(back_populates="work_type", cascade="all, delete-orphan")

    __table_args__ = (UniqueConstraint("project_id", "name"),)


# Модель записи о работе
class WorkEntry(Base):
    """
    Записи о выполненной работе.
    
    Поля:
    - entry_id: Автоинкремент ID
    - project_id: ID проекта
    - work_type_id: ID типа работы
    - created_by: Telegram ID создателя записи
    - company_id: ID компании (для изоляции данных)
    - date: Дата выполнения работы
    - quantity: Количество выполненной работы
    - description: Описание работы
    - sum_total: Рассчитанная сумма
    - created_at: Дата создания записи
    
    Связи:
    - project: Проект записи
    - work_type: Тип работы
    - company: Компания (для изоляции)
    """
    __tablename__ = "work_entries"
    
    entry_id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    project_id: Mapped[int] = mapped_column(ForeignKey("projects.project_id"))
    work_type_id: Mapped[int] = mapped_column(ForeignKey("work_types.id"))
    created_by: Mapped[int] = mapped_column(BigInteger, nullable=False)  # Telegram ID создателя
    company_id: Mapped[int] = mapped_column(ForeignKey("companies.id", ondelete="CASCADE"), nullable=False)
    date: Mapped[datetime.date] = mapped_column(Date)
    quantity: Mapped[float] = mapped_column(Numeric(10, 2))
    description: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)
    sum_total: Mapped[float] = mapped_column(Numeric(10, 2))
    created_at: Mapped[datetime.datetime] = mapped_column(DateTime, default=utc_now)

    # Связи
    project: Mapped["Project"] = relationship(back_populates="work_entries")
    work_type: Mapped["WorkType"] = relationship(back_populates="work_entries")
    company: Mapped["Company"] = relationship()


# Модель токенов регистрации
class Token(Base):
    """
    Токены для регистрации пользователей (24 часа).
    
    Поля:
    - id: Автоинкремент ID
    - token: Уникальный токен (64 символа)
    - role: Роль для регистрации ('director' | 'worker')
    - company_id: ID компании (NULL для токенов создания компании)
    - created_by_user_id: Telegram ID создателя токена
    - expires_at: Время истечения токена
    - used: Флаг использования токена
    - created_at: Дата создания
    
    Связи:
    - company: Компания токена (может быть NULL)
    """
    __tablename__ = "tokens"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    token: Mapped[str] = mapped_column(String(64), unique=True, nullable=False)
    role: Mapped[str] = mapped_column(String(50), nullable=False)  # 'director' | 'worker'
    company_id: Mapped[Optional[int]] = mapped_column(ForeignKey("companies.id", ondelete="CASCADE"), nullable=True)
    created_by_user_id: Mapped[int] = mapped_column(BigInteger, nullable=False)
    expires_at: Mapped[datetime.datetime] = mapped_column(DateTime, nullable=False)
    used: Mapped[bool] = mapped_column(Boolean, default=False)
    created_at: Mapped[datetime.datetime] = mapped_column(DateTime, default=utc_now)

    # Связи
    company: Mapped[Optional["Company"]] = relationship(back_populates="tokens")

    @staticmethod
    def generate_token() -> str:
        """Генерирует уникальный токен."""
        return secrets.token_urlsafe(48)

    @staticmethod
    def create_expires_at() -> datetime.datetime:
        """Создает время истечения токена (24 часа)."""
        return utc_now() + datetime.timedelta(hours=24)

    def is_expired(self) -> bool:
        """Проверяет, истек ли токен."""
        return utc_now() > self.expires_at

# Примеры использования:
"""
# Создание пользователя
user = User(user_id=*********)

# Создание компании
company = Company(name="ООО Компания")

# Назначение роли
role = UserCompanyRole(
    user_id=*********,
    company_id=1,
    role="director"
)

# Создание проекта
project = Project(
    created_by=*********,
    company_id=1,
    name="Проект Альфа",
    address="ул. Примерная, 1"
)

# Создание типа работы
work_type = WorkType(
    project_id=1,
    name="Монтаж",
    unit="час",
    rate_type="per_unit",
    value=1000.0
)

# Создание записи о работе
entry = WorkEntry(
    project_id=1,
    work_type_id=1,
    created_by=*********,
    company_id=1,
    date=datetime.date.today(),
    quantity=8.0,
    description="Установка оборудования",
    sum_total=8000.0
)

# Создание токена
token = Token(
    token=Token.generate_token(),
    role="worker",
    company_id=1,
    created_by_user_id=*********,
    expires_at=Token.create_expires_at()
)
"""
