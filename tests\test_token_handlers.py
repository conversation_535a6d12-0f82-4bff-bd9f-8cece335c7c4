"""
Тесты для обработчиков создания токенов
"""
import pytest
from unittest.mock import AsyncMock, MagicMock, patch

from handlers.director import create_worker_token as director_create_worker_token
from handlers.admin import create_worker_token as admin_create_worker_token, create_director_token
from services.token_service import TokenService
from services.company_service import CompanyService


class TestDirectorTokenHandlers:
    """Тесты для обработчиков токенов директора"""
    
    @pytest.fixture
    def mock_callback(self):
        """Мок callback query"""
        callback = MagicMock()
        callback.from_user.id = *********
        callback.data = "create_worker_token_1"
        callback.message.edit_text = AsyncMock()
        return callback
    
    @pytest.mark.asyncio
    async def test_director_create_worker_token_success(self, mock_callback):
        """Тест успешного создания токена рабочего директором"""
        # Мокаем сервисы
        mock_token = "test_token_123"
        mock_company = {"id": 1, "name": "Test Company"}
        
        with patch.object(TokenService, 'create_registration_token', return_value=mock_token), \
             patch.object(CompanyService, 'get_company', return_value=mock_company):
            
            # Вызываем обработчик
            await director_create_worker_token(
                mock_callback,
                user_permissions={'can_generate_worker_tokens': True}
            )
            
            # Проверяем вызовы сервисов
            TokenService.create_registration_token.assert_called_once_with(
                role="worker",
                company_id=1,
                created_by_user_id=*********,
                expires_hours=24
            )
            CompanyService.get_company.assert_called_once_with(1)
            
            # Проверяем что сообщение отредактировано
            assert mock_callback.message.edit_text.called
            call_args = mock_callback.message.edit_text.call_args
            
            # Проверяем содержимое сообщения
            message_text = call_args[0][0]
            assert "🔗 Токен для рабочего создан!" in message_text
            assert "Test Company" in message_text
            assert "test_token_123" in message_text
            assert "https://t.me/WorkLog_v3Bot?start=reg_test_token_123" in message_text
            
            # Проверяем что есть клавиатура
            assert 'reply_markup' in call_args[1]
    
    @pytest.mark.asyncio
    async def test_director_create_worker_token_extracts_company_id(self, mock_callback):
        """Тест правильного извлечения company_id из callback_data"""
        # Тестируем разные форматы callback_data
        test_cases = [
            ("create_worker_token_1", 1),
            ("create_worker_token_42", 42),
            ("create_worker_token_999", 999)
        ]
        
        for callback_data, expected_company_id in test_cases:
            mock_callback.data = callback_data
            
            with patch.object(TokenService, 'create_registration_token', return_value="token"), \
                 patch.object(CompanyService, 'get_company', return_value={"id": expected_company_id, "name": "Test"}):
                
                await director_create_worker_token(
                    mock_callback,
                    user_permissions={'can_generate_worker_tokens': True}
                )
                
                # Проверяем что правильный company_id передан в сервис
                TokenService.create_registration_token.assert_called_with(
                    role="worker",
                    company_id=expected_company_id,
                    created_by_user_id=*********,
                    expires_hours=24
                )


class TestAdminTokenHandlers:
    """Тесты для обработчиков токенов администратора"""
    
    @pytest.fixture
    def mock_callback(self):
        """Мок callback query"""
        callback = MagicMock()
        callback.from_user.id = *********
        callback.data = "create_director_token_1"
        callback.message.edit_text = AsyncMock()
        return callback
    
    @pytest.mark.asyncio
    async def test_admin_create_director_token_success(self, mock_callback):
        """Тест успешного создания токена директора админом"""
        # Мокаем сервисы
        mock_token = "director_token_456"
        mock_company = {"id": 1, "name": "Admin Company"}
        
        with patch.object(TokenService, 'create_registration_token', return_value=mock_token), \
             patch.object(CompanyService, 'get_company', return_value=mock_company):
            
            # Вызываем обработчик
            await create_director_token(
                mock_callback,
                user_permissions={'can_generate_tokens': True}
            )
            
            # Проверяем вызовы сервисов
            TokenService.create_registration_token.assert_called_once_with(
                role="director",
                company_id=1,
                created_by_user_id=*********,
                expires_hours=24
            )
            CompanyService.get_company.assert_called_once_with(1)
            
            # Проверяем содержимое сообщения
            call_args = mock_callback.message.edit_text.call_args
            message_text = call_args[0][0]
            assert "🔗 Токен для директора создан!" in message_text
            assert "Admin Company" in message_text
            assert "director_token_456" in message_text
    
    @pytest.mark.asyncio
    async def test_admin_create_worker_token_success(self, mock_callback):
        """Тест успешного создания токена рабочего админом"""
        mock_callback.data = "create_worker_token_2"
        
        # Мокаем сервисы
        mock_token = "worker_token_789"
        mock_company = {"id": 2, "name": "Worker Company"}
        
        with patch.object(TokenService, 'create_registration_token', return_value=mock_token), \
             patch.object(CompanyService, 'get_company', return_value=mock_company):
            
            # Вызываем обработчик админа для создания токена рабочего
            await admin_create_worker_token(
                mock_callback,
                user_permissions={'can_generate_worker_tokens': True}
            )
            
            # Проверяем вызовы сервисов
            TokenService.create_registration_token.assert_called_once_with(
                role="worker",
                company_id=2,
                created_by_user_id=*********,
                expires_hours=24
            )


class TestTokenHandlerPermissions:
    """Тесты проверки прав доступа в обработчиках токенов"""
    
    @pytest.fixture
    def mock_callback_with_answer(self):
        """Мок callback query с методом answer"""
        callback = MagicMock()
        callback.from_user.id = *********
        callback.data = "create_worker_token_1"
        callback.answer = AsyncMock()
        return callback
    
    @pytest.mark.asyncio
    async def test_director_handler_requires_worker_token_permission(self, mock_callback_with_answer):
        """Тест что обработчик директора требует права can_generate_worker_tokens"""
        # Вызываем без нужного права
        result = await director_create_worker_token(
            mock_callback_with_answer,
            user_permissions={'can_manage_workers': True}  # Нет can_generate_worker_tokens
        )
        
        # Должен быть отказ в доступе
        assert result is None
        assert mock_callback_with_answer.answer.called
    
    @pytest.mark.asyncio
    async def test_admin_director_handler_requires_tokens_permission(self, mock_callback_with_answer):
        """Тест что обработчик админа для директоров требует права can_generate_tokens"""
        mock_callback_with_answer.data = "create_director_token_1"
        
        # Вызываем без нужного права
        result = await create_director_token(
            mock_callback_with_answer,
            user_permissions={'can_generate_worker_tokens': True}  # Нет can_generate_tokens
        )
        
        # Должен быть отказ в доступе
        assert result is None
        assert mock_callback_with_answer.answer.called


if __name__ == "__main__":
    pytest.main([__file__])
