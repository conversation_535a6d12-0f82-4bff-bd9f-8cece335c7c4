"""
Обработчики для создания нового проекта.

Команда: /newproject
FSM: NewProjectStates (8 шагов)
Права: Права на создание проектов

Логика:
1. creating_project_name - ввод названия проекта
2. creating_project_address - ввод адреса проекта
3. choosing_copy_source - выбор источника для копирования типов работ
4. adding_work_type_name - ввод названия типа работы
5. adding_work_type_unit - ввод единицы измерения
6. adding_work_type_rate_type - выбор типа ставки
7. adding_work_type_value - ввод значения ставки
8. confirming_add_another_work_type - подтверждение добавления еще одного типа

Особенности:
- Циклическое добавление типов работ
- Копирование типов работ из существующих проектов
- Автоматическая установка как активного проекта
- Транзакционное создание
"""
import logging
from aiogram import types, Dispatcher
from aiogram.dispatcher import FSMContext
from typing import List

from db.dao.project_dao import ProjectDAO
from db.dao.work_type_dao import WorkTypeDAO
from db.dao.user_dao import UserDAO
from db.session import async_session
from keyboards.common import create_main_menu, create_cancel_keyboard, yes_no_keyboard
from localization.texts import get_text
from services.worktype_wizard import WorkTypeWizardService
from services.calculator import validate_positive_number
from states import NewProjectStates
from middleware import require_permission

logger = logging.getLogger(__name__)

# Константы валидации
MAX_PROJECT_NAME_LENGTH = 100
MAX_ADDRESS_LENGTH = 255
MAX_WORK_TYPE_NAME_LENGTH = 100
MAX_UNIT_LENGTH = 20


def register_handlers_new_project(dp: Dispatcher):
    """Регистрирует обработчики для команды /newproject."""
    dp.register_message_handler(cmd_new_project, commands=["newproject"], state="*")
    dp.register_message_handler(process_project_name, state=NewProjectStates.creating_project_name)
    dp.register_message_handler(process_project_address, state=NewProjectStates.creating_project_address)
    dp.register_message_handler(process_copy_source_choice, state=NewProjectStates.choosing_copy_source)
    dp.register_message_handler(process_work_type_name, state=NewProjectStates.adding_work_type_name)
    dp.register_message_handler(process_work_type_unit, state=NewProjectStates.adding_work_type_unit)
    dp.register_message_handler(process_rate_type, state=NewProjectStates.adding_work_type_rate_type)
    dp.register_message_handler(process_rate_value, state=NewProjectStates.adding_work_type_value)
    dp.register_message_handler(process_add_another_confirmation, state=NewProjectStates.confirming_add_another_work_type)


@require_permission("can_manage_projects")
async def cmd_new_project(message: types.Message, state: FSMContext, **kwargs):
    """
    Начало сценария создания нового проекта.
    
    Запрашивает название проекта.
    """
    await state.finish()
    
    await message.answer(
        get_text("new_project_enter_name"),
        reply_markup=create_cancel_keyboard()
    )
    await NewProjectStates.creating_project_name.set()


async def process_project_name(message: types.Message, state: FSMContext):
    """
    Обработка ввода названия проекта.
    
    Валидирует:
    - Непустое название
    - Длина не превышает MAX_PROJECT_NAME_LENGTH
    - Уникальность в рамках компании пользователя
    """
    if message.text.lower() == get_text("cancel").lower():
        await message.answer(get_text("operation_cancelled"), reply_markup=create_main_menu())
        await state.finish()
        return
    
    project_name = message.text.strip()
    
    # Валидация названия
    if not project_name:
        await message.answer(get_text("project_name_empty"))
        return
    
    if len(project_name) > MAX_PROJECT_NAME_LENGTH:
        await message.answer(get_text("project_name_too_long", max_length=MAX_PROJECT_NAME_LENGTH))
        return
    
    # Проверка уникальности (в рамках компании пользователя)
    user_id = message.from_user.id
    try:
        async with async_session() as session:
            # Получаем компанию пользователя
            user_company_id = await get_user_company_id(session, user_id)
            if not user_company_id:
                await message.answer(get_text("user_not_in_company"), reply_markup=create_main_menu())
                await state.finish()
                return
            
            # Проверяем уникальность названия
            existing_project = await ProjectDAO.get_by_name_and_company(
                session, project_name, user_company_id
            )
            if existing_project:
                await message.answer(get_text("project_name_exists"))
                return
        
        await state.update_data(
            project_name=project_name,
            company_id=user_company_id
        )
        
        await message.answer(
            get_text("new_project_enter_address"),
            reply_markup=create_cancel_keyboard()
        )
        await NewProjectStates.creating_project_address.set()
    
    except Exception as e:
        logger.exception(f"Ошибка при проверке названия проекта для user {user_id}: {e}")
        await message.answer(get_text("error_try_again"), reply_markup=create_main_menu())
        await state.finish()


async def process_project_address(message: types.Message, state: FSMContext):
    """
    Обработка ввода адреса проекта.
    
    Валидирует:
    - Длина не превышает MAX_ADDRESS_LENGTH
    - Может быть пустым
    """
    if message.text.lower() == get_text("cancel").lower():
        await message.answer(get_text("operation_cancelled"), reply_markup=create_main_menu())
        await state.finish()
        return
    
    address = message.text.strip()
    
    # Валидация адреса
    if len(address) > MAX_ADDRESS_LENGTH:
        await message.answer(get_text("address_too_long", max_length=MAX_ADDRESS_LENGTH))
        return
    
    await state.update_data(project_address=address if address else None)
    
    # Предлагаем скопировать типы работ из существующего проекта
    user_id = message.from_user.id
    try:
        async with async_session() as session:
            user_projects = await ProjectDAO.get_by_user_id(session, user_id)
        
        if user_projects:
            # Есть проекты - предлагаем копирование
            keyboard = types.ReplyKeyboardMarkup(resize_keyboard=True, one_time_keyboard=True)
            keyboard.add(types.KeyboardButton("Скопировать из существующего"))
            keyboard.add(types.KeyboardButton("Создать новые"))
            keyboard.add(types.KeyboardButton(get_text("cancel")))
            
            await message.answer(
                get_text("new_project_copy_work_types"),
                reply_markup=keyboard
            )
            await NewProjectStates.choosing_copy_source.set()
        else:
            # Нет проектов - сразу к созданию типов работ
            await start_work_type_creation(message, state)
    
    except Exception as e:
        logger.exception(f"Ошибка при обработке адреса проекта для user {user_id}: {e}")
        await message.answer(get_text("error_try_again"), reply_markup=create_main_menu())
        await state.finish()


async def process_copy_source_choice(message: types.Message, state: FSMContext):
    """
    Обработка выбора источника копирования типов работ.
    
    Варианты:
    - "Скопировать из существующего" → показывает список проектов
    - "Создать новые" → переходит к созданию типов работ
    """
    choice = message.text
    
    if choice.lower() == get_text("cancel").lower():
        await message.answer(get_text("operation_cancelled"), reply_markup=create_main_menu())
        await state.finish()
        return
    
    if choice == "Скопировать из существующего":
        await show_projects_for_copying(message, state)
    elif choice == "Создать новые":
        await start_work_type_creation(message, state)
    else:
        await message.answer(get_text("invalid_choice"))


async def show_projects_for_copying(message: types.Message, state: FSMContext):
    """Показывает список проектов для копирования типов работ."""
    user_id = message.from_user.id
    
    try:
        async with async_session() as session:
            projects = await ProjectDAO.get_by_user_id(session, user_id)
        
        if not projects:
            await start_work_type_creation(message, state)
            return
        
        keyboard = types.ReplyKeyboardMarkup(resize_keyboard=True, one_time_keyboard=True)
        project_map = {}
        
        for project in projects:
            keyboard.add(types.KeyboardButton(project.name))
            project_map[project.name] = project.project_id
        
        keyboard.add(types.KeyboardButton(get_text("cancel")))
        
        await state.update_data(copy_projects_map=project_map)
        await message.answer(
            get_text("new_project_choose_source"),
            reply_markup=keyboard
        )
        # Остаемся в том же состоянии для обработки выбора проекта
    
    except Exception as e:
        logger.exception(f"Ошибка при показе проектов для копирования для user {user_id}: {e}")
        await start_work_type_creation(message, state)


async def start_work_type_creation(message: types.Message, state: FSMContext):
    """Начинает процесс создания типов работ."""
    await state.update_data(work_types=[])  # Инициализируем список типов работ
    
    await message.answer(
        get_text("new_project_add_work_type_name"),
        reply_markup=create_cancel_keyboard()
    )
    await NewProjectStates.adding_work_type_name.set()


async def process_work_type_name(message: types.Message, state: FSMContext):
    """
    Обработка ввода названия типа работы.
    
    Валидирует:
    - Непустое название
    - Длина не превышает MAX_WORK_TYPE_NAME_LENGTH
    """
    if message.text.lower() == get_text("cancel").lower():
        await message.answer(get_text("operation_cancelled"), reply_markup=create_main_menu())
        await state.finish()
        return
    
    work_type_name = message.text.strip()
    
    if not work_type_name:
        await message.answer(get_text("work_type_name_empty"))
        return
    
    if len(work_type_name) > MAX_WORK_TYPE_NAME_LENGTH:
        await message.answer(get_text("work_type_name_too_long", max_length=MAX_WORK_TYPE_NAME_LENGTH))
        return
    
    await state.update_data(current_work_type_name=work_type_name)
    
    await message.answer(
        get_text("new_project_add_work_type_unit"),
        reply_markup=create_cancel_keyboard()
    )
    await NewProjectStates.adding_work_type_unit.set()


async def process_work_type_unit(message: types.Message, state: FSMContext):
    """
    Обработка ввода единицы измерения.
    
    Валидирует:
    - Непустая единица
    - Длина не превышает MAX_UNIT_LENGTH
    """
    if message.text.lower() == get_text("cancel").lower():
        await message.answer(get_text("operation_cancelled"), reply_markup=create_main_menu())
        await state.finish()
        return
    
    unit = message.text.strip()
    
    if not unit:
        await message.answer(get_text("unit_empty"))
        return
    
    if len(unit) > MAX_UNIT_LENGTH:
        await message.answer(get_text("unit_too_long", max_length=MAX_UNIT_LENGTH))
        return
    
    await state.update_data(current_work_type_unit=unit)
    
    # Выбор типа ставки
    keyboard = types.ReplyKeyboardMarkup(resize_keyboard=True, one_time_keyboard=True)
    keyboard.add(
        types.KeyboardButton("Фиксированная ставка"),
        types.KeyboardButton("Ставка за единицу")
    )
    keyboard.add(types.KeyboardButton(get_text("cancel")))
    
    await message.answer(
        get_text("new_project_choose_rate_type"),
        reply_markup=keyboard
    )
    await NewProjectStates.adding_work_type_rate_type.set()


async def process_rate_type(message: types.Message, state: FSMContext):
    """Обработка выбора типа ставки."""
    choice = message.text
    
    if choice.lower() == get_text("cancel").lower():
        await message.answer(get_text("operation_cancelled"), reply_markup=create_main_menu())
        await state.finish()
        return
    
    if choice == "Фиксированная ставка":
        rate_type = "fixed"
        prompt = get_text("new_project_enter_fixed_rate")
    elif choice == "Ставка за единицу":
        rate_type = "per_unit"
        data = await state.get_data()
        unit = data.get("current_work_type_unit", "единицу")
        prompt = get_text("new_project_enter_per_unit_rate", unit=unit)
    else:
        await message.answer(get_text("invalid_choice"))
        return
    
    await state.update_data(current_work_type_rate_type=rate_type)
    
    await message.answer(prompt, reply_markup=create_cancel_keyboard())
    await NewProjectStates.adding_work_type_value.set()


async def process_rate_value(message: types.Message, state: FSMContext):
    """
    Обработка ввода значения ставки.
    
    Валидирует:
    - Положительное число
    - Корректный формат
    """
    if message.text.lower() == get_text("cancel").lower():
        await message.answer(get_text("operation_cancelled"), reply_markup=create_main_menu())
        await state.finish()
        return
    
    try:
        rate_value = validate_positive_number(message.text, "ставка")
    except ValueError as e:
        await message.answer(str(e))
        return
    
    # Сохраняем тип работы
    data = await state.get_data()
    work_types = data.get("work_types", [])
    
    new_work_type = {
        "name": data["current_work_type_name"],
        "unit": data["current_work_type_unit"],
        "rate_type": data["current_work_type_rate_type"],
        "value": rate_value
    }
    work_types.append(new_work_type)
    
    await state.update_data(work_types=work_types)
    
    # Спрашиваем о добавлении еще одного типа работы
    await message.answer(
        get_text("new_project_work_type_added", 
                name=new_work_type["name"],
                unit=new_work_type["unit"],
                value=rate_value),
        reply_markup=yes_no_keyboard()
    )
    await NewProjectStates.confirming_add_another_work_type.set()


async def process_add_another_confirmation(message: types.Message, state: FSMContext):
    """
    Обработка подтверждения добавления еще одного типа работы.
    
    Варианты:
    - "Да" → возвращается к вводу названия типа работы
    - "Нет" → создает проект со всеми типами работ
    """
    choice = message.text.lower()
    
    if choice == get_text("yes").lower():
        # Добавляем еще один тип работы
        await message.answer(
            get_text("new_project_add_work_type_name"),
            reply_markup=create_cancel_keyboard()
        )
        await NewProjectStates.adding_work_type_name.set()
    elif choice == get_text("no").lower():
        # Создаем проект
        await create_project_with_work_types(message, state)
    else:
        await message.answer(get_text("invalid_choice"))


async def create_project_with_work_types(message: types.Message, state: FSMContext):
    """
    Создает проект со всеми типами работ в транзакции.
    
    Устанавливает созданный проект как активный для пользователя.
    """
    data = await state.get_data()
    user_id = message.from_user.id
    
    try:
        async with async_session() as session:
            # Создаем проект
            project = await ProjectDAO.create(
                session=session,
                name=data["project_name"],
                address=data.get("project_address"),
                created_by=user_id,
                company_id=data["company_id"]
            )
            
            # Создаем типы работ
            work_types = data.get("work_types", [])
            for work_type_data in work_types:
                await WorkTypeDAO.create(
                    session=session,
                    project_id=project.project_id,
                    name=work_type_data["name"],
                    unit=work_type_data["unit"],
                    rate_type=work_type_data["rate_type"],
                    value=work_type_data["value"]
                )
            
            # Устанавливаем проект как активный
            await UserDAO.set_active_project(session, user_id, project.project_id)
            
            await session.commit()
        
        success_text = get_text(
            "new_project_created_success",
            name=data["project_name"],
            work_types_count=len(work_types)
        )
        
        await message.answer(success_text, reply_markup=create_main_menu())
        await state.finish()
    
    except Exception as e:
        logger.exception(f"Ошибка при создании проекта для user {user_id}: {e}")
        await message.answer(get_text("error_creating_project"), reply_markup=create_main_menu())
        await state.finish()


async def get_user_company_id(session, user_id: int) -> int:
    """Получает ID компании пользователя."""
    # Здесь должна быть логика получения компании пользователя
    # Для примера возвращаем 1
    return 1


# Примеры использования:
"""
# Регистрация обработчиков
register_handlers_new_project(dp)

# Команда /newproject
# Пользователь вводит: /newproject
# Бот запрашивает: название проекта
# Пользователь вводит: "Проект Альфа"
# Бот запрашивает: адрес проекта
# Пользователь вводит: "ул. Примерная, 1"
# Бот предлагает: скопировать типы работ или создать новые
# Пользователь выбирает: "Создать новые"
# Цикл добавления типов работ:
#   - Название: "Монтаж"
#   - Единица: "час"
#   - Тип ставки: "Ставка за единицу"
#   - Значение: "1000"
#   - Добавить еще? "Да"
#   - ... (повторяется)
#   - Добавить еще? "Нет"
# Бот создает проект и устанавливает как активный
"""
