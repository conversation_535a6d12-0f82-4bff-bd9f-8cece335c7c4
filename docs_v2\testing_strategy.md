# 🧪 СТРАТЕГИЯ ТЕСТИРОВАНИЯ WORKLOG MVP v2.0

## 🎯 Цели тестирования

### Основные задачи
1. **Функциональность** - все функции работают согласно ТЗ
2. **Безопасность** - RBAC система защищает данные
3. **Производительность** - система выдерживает нагрузку
4. **Надёжность** - корректная обработка ошибок
5. **Пользовательский опыт** - интуитивный интерфейс

### Критерии качества
- **Покрытие кода**: минимум 85%
- **Время отклика**: максимум 2 секунды
- **Доступность**: 99.9% uptime
- **Безопасность**: 0 критических уязвимостей

## 🏗️ Пирамида тестирования

```mermaid
graph TD
    A[E2E Tests<br/>10%] --> B[Integration Tests<br/>20%]
    B --> C[Unit Tests<br/>70%]
    
    style A fill:#ff6b6b
    style B fill:#4ecdc4
    style C fill:#45b7d1
```

### Распределение тестов
- **70% Unit Tests** - быстрые, изолированные тесты компонентов
- **20% Integration Tests** - тесты взаимодействия компонентов
- **10% E2E Tests** - полные пользовательские сценарии

## 🔬 Уровни тестирования

### 1. Unit Tests (Модульные тесты)

#### Что тестируем:
- **Services** - бизнес-логика
- **DAO** - доступ к данным
- **Utils** - вспомогательные функции
- **Validators** - валидация данных
- **Formatters** - форматирование

#### Структура unit-тестов:
```
tests/unit/
├── test_services/
│   ├── test_work_service.py
│   ├── test_project_service.py
│   ├── test_director_service.py
│   └── test_report_service.py
├── test_dao/
│   ├── test_work_entry_dao.py
│   ├── test_project_dao.py
│   └── test_user_dao.py
├── test_utils/
│   ├── test_validators.py
│   ├── test_formatters.py
│   └── test_decorators.py
└── test_models/
    ├── test_user_model.py
    ├── test_project_model.py
    └── test_work_entry_model.py
```

#### Пример unit-теста:
```python
class TestWorkService:
    """Тесты для сервиса работы с записями."""
    
    async def test_create_work_entry_success(
        self,
        work_service: WorkService,
        test_user: User,
        test_project: Project
    ):
        """Тест успешного создания записи о работе."""
        # Arrange
        work_data = {
            "project_id": test_project.id,
            "description": "Тестовая работа",
            "quantity": 8.0,
            "work_type": "hourly",
            "rate": 500.0,
            "date": date.today()
        }
        
        # Act
        work_entry = await work_service.create_work_entry(
            user=test_user,
            **work_data
        )
        
        # Assert
        assert work_entry is not None
        assert work_entry.user_id == test_user.id
        assert work_entry.project_id == test_project.id
        assert work_entry.description == "Тестовая работа"
        assert float(work_entry.quantity) == 8.0
        assert float(work_entry.total_amount) == 4000.0
    
    async def test_create_work_entry_invalid_quantity_raises_error(
        self,
        work_service: WorkService,
        test_user: User,
        test_project: Project
    ):
        """Тест создания записи с некорректным количеством."""
        # Arrange
        work_data = {
            "project_id": test_project.id,
            "description": "Тестовая работа",
            "quantity": -1.0,  # Некорректное количество
            "work_type": "hourly",
            "rate": 500.0,
            "date": date.today()
        }
        
        # Act & Assert
        with pytest.raises(ValidationError) as exc_info:
            await work_service.create_work_entry(
                user=test_user,
                **work_data
            )
        
        assert "Количество должно быть больше 0" in str(exc_info.value)
    
    async def test_get_user_work_entries_returns_only_user_entries(
        self,
        work_service: WorkService,
        test_user: User,
        other_user: User,
        test_project: Project
    ):
        """Тест получения записей возвращает только записи пользователя."""
        # Arrange - создаём записи для разных пользователей
        await work_service.create_work_entry(
            user=test_user,
            project_id=test_project.id,
            description="Работа пользователя 1",
            quantity=8.0,
            work_type="hourly",
            rate=500.0,
            date=date.today()
        )
        
        await work_service.create_work_entry(
            user=other_user,
            project_id=test_project.id,
            description="Работа пользователя 2",
            quantity=6.0,
            work_type="hourly",
            rate=500.0,
            date=date.today()
        )
        
        # Act
        user_entries = await work_service.get_user_work_entries(test_user)
        
        # Assert
        assert len(user_entries) == 1
        assert user_entries[0].user_id == test_user.id
        assert user_entries[0].description == "Работа пользователя 1"
```

### 2. Integration Tests (Интеграционные тесты)

#### Что тестируем:
- **Handler + Service** - обработчики команд
- **Service + DAO** - бизнес-логика с БД
- **FSM flows** - пользовательские потоки
- **RBAC** - система прав доступа

#### Структура интеграционных тестов:
```
tests/integration/
├── test_workflows/
│   ├── test_worker_flow.py
│   ├── test_director_flow.py
│   └── test_admin_flow.py
├── test_handlers/
│   ├── test_work_handlers.py
│   ├── test_project_handlers.py
│   └── test_report_handlers.py
├── test_fsm/
│   ├── test_add_work_flow.py
│   ├── test_create_project_flow.py
│   └── test_report_flow.py
└── test_security/
    ├── test_rbac.py
    ├── test_data_isolation.py
    └── test_authentication.py
```

#### Пример интеграционного теста:
```python
class TestWorkerFlow:
    """Интеграционные тесты для рабочего."""
    
    async def test_complete_add_work_flow(
        self,
        bot_client: BotClient,
        test_worker: User,
        test_project: Project
    ):
        """Тест полного потока добавления работы."""
        # Arrange
        await bot_client.login_as(test_worker)
        
        # Act & Assert - пошаговое тестирование
        
        # 1. Начинаем добавление работы
        response = await bot_client.send_callback("add:work")
        assert "Выберите проект" in response.text
        assert f"project:{test_project.id}" in response.keyboard_callbacks
        
        # 2. Выбираем проект
        response = await bot_client.send_callback(f"select:project:{test_project.id}")
        assert "Выберите тип работы" in response.text
        assert "select:work_type:hourly" in response.keyboard_callbacks
        
        # 3. Выбираем тип работы
        response = await bot_client.send_callback("select:work_type:hourly")
        assert "Введите описание" in response.text
        
        # 4. Вводим описание
        response = await bot_client.send_message("Кладка кирпича")
        assert "Введите количество часов" in response.text
        
        # 5. Вводим количество
        response = await bot_client.send_message("8")
        assert "Подтвердите запись" in response.text
        assert "8.0 часов" in response.text
        assert "4,000₽" in response.text
        
        # 6. Подтверждаем
        response = await bot_client.send_callback("confirm:work_entry")
        assert "успешно добавлена" in response.text
        
        # Проверяем, что запись создалась в БД
        work_entries = await WorkService(bot_client.session).get_user_work_entries(test_worker)
        assert len(work_entries) == 1
        assert work_entries[0].description == "Кладка кирпича"
    
    async def test_worker_cannot_access_director_functions(
        self,
        bot_client: BotClient,
        test_worker: User
    ):
        """Тест что рабочий не может получить доступ к функциям директора."""
        # Arrange
        await bot_client.login_as(test_worker)
        
        # Act & Assert
        response = await bot_client.send_callback("create:project")
        assert response.status == "error"
        assert "Недостаточно прав" in response.error_message
        
        response = await bot_client.send_callback("manage:workers")
        assert response.status == "error"
        assert "Недостаточно прав" in response.error_message
```

### 3. E2E Tests (End-to-End тесты)

#### Что тестируем:
- **Полные пользовательские сценарии**
- **Интеграция с Telegram API**
- **Производительность системы**
- **Восстановление после сбоев**

#### Пример E2E теста:
```python
class TestCompleteWorkflow:
    """E2E тесты полных рабочих процессов."""
    
    async def test_full_project_lifecycle(
        self,
        telegram_client: TelegramClient,
        test_director: User,
        test_worker: User
    ):
        """Тест полного жизненного цикла проекта."""
        
        # 1. Директор создаёт проект
        await telegram_client.login_as(test_director)
        await telegram_client.send_command("/start")
        await telegram_client.click_button("🏗️ Управление проектами")
        await telegram_client.click_button("➕ Новый проект")
        
        await telegram_client.send_message("Тестовый проект E2E")
        await telegram_client.send_message("ул. Тестовая, 1")
        await telegram_client.send_message("600")  # Почасовая ставка
        await telegram_client.send_message("100")  # Сдельная ставка
        await telegram_client.click_button("✅ Создать проект")
        
        # 2. Директор приглашает рабочего
        await telegram_client.click_button("👷 Управление рабочими")
        await telegram_client.click_button("📨 Пригласить рабочего")
        await telegram_client.send_message("Для тестирования E2E")
        
        # Получаем токен из ответа
        response = await telegram_client.get_last_message()
        token = extract_token_from_message(response.text)
        
        # 3. Рабочий регистрируется по токену
        await telegram_client.login_as(test_worker)
        await telegram_client.send_command(f"/start {token}")
        await telegram_client.click_button("👷 Рабочий")
        
        # 4. Рабочий добавляет работу
        await telegram_client.click_button("➕ Добавить работу")
        await telegram_client.click_button("🏗️ Тестовый проект E2E")
        await telegram_client.click_button("⏰ Почасовая работа")
        await telegram_client.send_message("Тестовая работа E2E")
        await telegram_client.send_message("8")
        await telegram_client.click_button("✅ Подтвердить")
        
        # 5. Директор создаёт отчёт
        await telegram_client.login_as(test_director)
        await telegram_client.click_button("📊 Отчёты")
        await telegram_client.click_button("🏗️ По проектам")
        await telegram_client.click_button("📅 Сегодня")
        await telegram_client.click_button("📄 Экспорт PDF")
        
        # Проверяем результат
        report_file = await telegram_client.get_last_file()
        assert report_file.name.endswith(".pdf")
        assert "Тестовый проект E2E" in report_file.content
        assert "Тестовая работа E2E" in report_file.content
        assert "4,800₽" in report_file.content  # 8 часов * 600₽
```

## 🛠️ Инструменты тестирования

### Основной стек
```python
# pytest.ini
[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    --strict-markers
    --strict-config
    --cov=src
    --cov-report=html
    --cov-report=term-missing
    --cov-fail-under=85
asyncio_mode = auto

markers =
    unit: Unit tests
    integration: Integration tests
    e2e: End-to-end tests
    slow: Slow tests
    security: Security tests
```

### Фикстуры и моки
```python
# tests/conftest.py
import pytest
from unittest.mock import AsyncMock, MagicMock
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from src.models import User, Company, Project

@pytest.fixture
async def test_session():
    """Создаёт тестовую сессию БД."""
    engine = create_async_engine("sqlite+aiosqlite:///:memory:")
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    async with AsyncSession(engine) as session:
        yield session

@pytest.fixture
async def test_company(test_session):
    """Создаёт тестовую компанию."""
    company = Company(
        name="Тестовая компания",
        legal_name="ООО Тест",
        phone="****** 123-45-67",
        email="<EMAIL>"
    )
    test_session.add(company)
    await test_session.commit()
    return company

@pytest.fixture
async def test_worker(test_session, test_company):
    """Создаёт тестового рабочего."""
    user = User(
        telegram_id=*********,
        username="test_worker",
        first_name="Тест",
        last_name="Рабочий",
        role="ROLE_WORKER",
        company_id=test_company.id
    )
    test_session.add(user)
    await test_session.commit()
    return user

@pytest.fixture
async def test_director(test_session, test_company):
    """Создаёт тестового директора."""
    user = User(
        telegram_id=*********,
        username="test_director",
        first_name="Тест",
        last_name="Директор",
        role="ROLE_DIRECTOR",
        company_id=test_company.id
    )
    test_session.add(user)
    await test_session.commit()
    return user

@pytest.fixture
def mock_bot():
    """Мок Telegram бота."""
    bot = AsyncMock()
    bot.send_message = AsyncMock()
    bot.edit_message_text = AsyncMock()
    bot.answer_callback_query = AsyncMock()
    return bot

@pytest.fixture
def mock_callback_query():
    """Мок callback query."""
    callback = MagicMock()
    callback.data = "test:callback"
    callback.from_user.id = *********
    callback.message.edit_text = AsyncMock()
    callback.answer = AsyncMock()
    return callback
```

## 📊 Метрики и покрытие

### Целевые метрики
- **Unit Tests**: 90% покрытие
- **Integration Tests**: 80% покрытие критических путей
- **E2E Tests**: 100% основных пользовательских сценариев
- **Performance**: < 2 сек время отклика
- **Security**: 0 критических уязвимостей

### Отчёты о покрытии
```bash
# Генерация отчёта о покрытии
pytest --cov=src --cov-report=html --cov-report=term

# Проверка качества кода
flake8 src/
black --check src/
mypy src/

# Проверка безопасности
bandit -r src/
safety check
```

## 🚀 CI/CD Pipeline

### GitHub Actions
```yaml
name: Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        pip install -r requirements-dev.txt
    
    - name: Run unit tests
      run: pytest tests/unit/ -v --cov=src
    
    - name: Run integration tests
      run: pytest tests/integration/ -v
      env:
        DATABASE_URL: postgresql+asyncpg://postgres:test@localhost:5432/test_db
    
    - name: Run security checks
      run: |
        bandit -r src/
        safety check
    
    - name: Upload coverage
      uses: codecov/codecov-action@v3
```

## 🔒 Тестирование безопасности

### Security Tests
```python
class TestSecurity:
    """Тесты безопасности."""
    
    async def test_rbac_isolation_between_companies(
        self,
        company1_worker: User,
        company2_worker: User,
        company1_project: Project,
        work_service: WorkService
    ):
        """Тест изоляции данных между компаниями."""
        # Рабочий из компании 1 не должен видеть проекты компании 2
        projects = await work_service.get_available_projects(company1_worker)
        project_ids = [p.id for p in projects]
        
        assert company1_project.id in project_ids
        # Проекты компании 2 не должны быть доступны
        company2_projects = await ProjectService(session).get_company_projects(2)
        for project in company2_projects:
            assert project.id not in project_ids
    
    async def test_sql_injection_protection(
        self,
        work_service: WorkService,
        test_user: User
    ):
        """Тест защиты от SQL инъекций."""
        malicious_input = "'; DROP TABLE users; --"
        
        # Попытка SQL инъекции через описание работы
        with pytest.raises(ValidationError):
            await work_service.create_work_entry(
                user=test_user,
                project_id=1,
                description=malicious_input,
                quantity=8.0,
                work_type="hourly",
                rate=500.0,
                date=date.today()
            )
    
    async def test_unauthorized_access_blocked(
        self,
        bot_client: BotClient,
        test_worker: User
    ):
        """Тест блокировки неавторизованного доступа."""
        # Рабочий не должен иметь доступ к функциям директора
        await bot_client.login_as(test_worker)
        
        director_endpoints = [
            "create:project",
            "manage:workers",
            "create:invitation_token",
            "view:company_reports"
        ]
        
        for endpoint in director_endpoints:
            response = await bot_client.send_callback(endpoint)
            assert response.status == "error"
            assert "Недостаточно прав" in response.error_message
```

## 📈 Performance Testing

### Нагрузочные тесты
```python
import asyncio
import time
from concurrent.futures import ThreadPoolExecutor

class TestPerformance:
    """Тесты производительности."""
    
    async def test_concurrent_work_entry_creation(
        self,
        work_service: WorkService,
        test_users: List[User],
        test_project: Project
    ):
        """Тест создания записей о работе под нагрузкой."""
        start_time = time.time()
        
        # Создаём 100 записей одновременно
        tasks = []
        for i, user in enumerate(test_users[:100]):
            task = work_service.create_work_entry(
                user=user,
                project_id=test_project.id,
                description=f"Нагрузочный тест {i}",
                quantity=8.0,
                work_type="hourly",
                rate=500.0,
                date=date.today()
            )
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        end_time = time.time()
        
        # Проверяем результаты
        successful_creates = [r for r in results if not isinstance(r, Exception)]
        assert len(successful_creates) == 100
        
        # Проверяем время выполнения (должно быть < 5 секунд)
        execution_time = end_time - start_time
        assert execution_time < 5.0
    
    async def test_report_generation_performance(
        self,
        report_service: ReportService,
        test_director: User,
        large_dataset: List[WorkEntry]  # 10000+ записей
    ):
        """Тест производительности генерации отчётов."""
        start_time = time.time()
        
        report = await report_service.generate_company_report(
            user=test_director,
            start_date=date.today() - timedelta(days=30),
            end_date=date.today(),
            report_type="detailed"
        )
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # Отчёт должен генерироваться менее чем за 3 секунды
        assert execution_time < 3.0
        assert report is not None
        assert len(report.entries) > 0
```

## 📋 Чек-лист тестирования

### Перед релизом
- [ ] Все unit-тесты проходят
- [ ] Все integration-тесты проходят
- [ ] E2E тесты основных сценариев проходят
- [ ] Покрытие кода > 85%
- [ ] Нет критических уязвимостей безопасности
- [ ] Performance тесты проходят
- [ ] Документация обновлена
- [ ] Миграции БД протестированы
- [ ] Rollback план готов

### Регрессионное тестирование
- [ ] Все существующие функции работают
- [ ] Новые функции не ломают старые
- [ ] RBAC система работает корректно
- [ ] Данные между компаниями изолированы
- [ ] Экспорт отчётов работает
- [ ] Уведомления отправляются
