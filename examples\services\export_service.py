"""
Сервис экспорта данных в Excel и PDF форматы.

Функции:
- export_to_excel() - экспорт в Excel с настраиваемыми столбцами
- export_to_pdf() - экспорт в PDF с фиксированным шаблоном
- _prepare_excel_data() - подготовка данных для Excel
- _prepare_pdf_data() - подготовка данных для PDF

Особенности:
- Excel: настраиваемые столбцы, фильтры, форматирование
- PDF: фиксированный шаблон, логотип, подписи, итоги
"""
import os
import tempfile
from typing import List, Dict, Optional
import datetime
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.utils import get_column_letter

from ..templates.work_report_template import WorkReportPDF


class ExportService:
    """Сервис для экспорта данных в различные форматы."""

    # Доступные столбцы для Excel экспорта
    AVAILABLE_COLUMNS = {
        "Дата": "date",
        "Тип работы": "work_type", 
        "Описание": "description",
        "Количество": "quantity",
        "Ед. изм.": "unit",
        "Ставка": "rate",
        "Сумма": "sum"
    }

    @staticmethod
    async def export_to_excel(
        entries: List[Dict],
        project_name: str,
        selected_columns: List[str] = None,
        start_date: Optional[datetime.date] = None,
        end_date: Optional[datetime.date] = None
    ) -> str:
        """
        Экспортирует данные в Excel файл.
        
        Args:
            entries: Список записей для экспорта
            project_name: Название проекта
            selected_columns: Выбранные столбцы (если None - все)
            start_date: Начальная дата периода
            end_date: Конечная дата периода
            
        Returns:
            Путь к созданному файлу
        """
        # Если столбцы не выбраны, используем все
        if not selected_columns:
            selected_columns = list(ExportService.AVAILABLE_COLUMNS.keys())
        
        # Создаем временный файл
        temp_dir = tempfile.gettempdir()
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"worklog_{project_name}_{timestamp}.xlsx"
        file_path = os.path.join(temp_dir, filename)
        
        # Создаем книгу Excel
        wb = Workbook()
        ws = wb.active
        ws.title = "Отчет по работам"
        
        # Заголовок отчета
        ws.merge_cells('A1:G1')
        ws['A1'] = f"ОТЧЕТ ПО РАБОТАМ - {project_name}"
        ws['A1'].font = Font(size=16, bold=True)
        ws['A1'].alignment = Alignment(horizontal='center')
        
        # Информация о периоде
        row = 2
        if start_date and end_date:
            ws.merge_cells(f'A{row}:G{row}')
            ws[f'A{row}'] = f"Период: {start_date.strftime('%d.%m.%Y')} - {end_date.strftime('%d.%m.%Y')}"
        elif start_date:
            ws.merge_cells(f'A{row}:G{row}')
            ws[f'A{row}'] = f"Период: с {start_date.strftime('%d.%m.%Y')}"
        elif end_date:
            ws.merge_cells(f'A{row}:G{row}')
            ws[f'A{row}'] = f"Период: до {end_date.strftime('%d.%m.%Y')}"
        else:
            ws.merge_cells(f'A{row}:G{row}')
            ws[f'A{row}'] = "Период: За всё время"
        
        ws[f'A{row}'].alignment = Alignment(horizontal='center')
        row += 2
        
        # Заголовки столбцов
        header_row = row
        for col_idx, column_name in enumerate(selected_columns, 1):
            cell = ws.cell(row=header_row, column=col_idx, value=column_name)
            cell.font = Font(bold=True, color="FFFFFF")
            cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            cell.alignment = Alignment(horizontal='center')
            cell.border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )
        
        # Данные
        total_sum = 0
        for entry_idx, entry in enumerate(entries, 1):
            data_row = header_row + entry_idx
            
            for col_idx, column_name in enumerate(selected_columns, 1):
                field_name = ExportService.AVAILABLE_COLUMNS[column_name]
                value = entry.get(field_name, "")
                
                # Форматирование значений
                if field_name == "date" and value:
                    value = value.strftime("%d.%m.%Y")
                elif field_name in ["quantity", "rate", "sum"] and value:
                    value = float(value)
                
                cell = ws.cell(row=data_row, column=col_idx, value=value)
                cell.border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )
                
                # Выравнивание для числовых значений
                if field_name in ["quantity", "rate", "sum"]:
                    cell.alignment = Alignment(horizontal='right')
                    if field_name in ["rate", "sum"]:
                        cell.number_format = '#,##0.00'
            
            # Подсчет общей суммы
            if "sum" in entry:
                total_sum += entry["sum"]
        
        # Итоговая строка
        if "Сумма" in selected_columns:
            total_row = header_row + len(entries) + 1
            sum_col_idx = selected_columns.index("Сумма") + 1
            
            # Подпись "ИТОГО"
            if sum_col_idx > 1:
                cell = ws.cell(row=total_row, column=sum_col_idx - 1, value="ИТОГО:")
                cell.font = Font(bold=True)
                cell.alignment = Alignment(horizontal='right')
            
            # Сумма
            cell = ws.cell(row=total_row, column=sum_col_idx, value=total_sum)
            cell.font = Font(bold=True)
            cell.number_format = '#,##0.00'
            cell.alignment = Alignment(horizontal='right')
            cell.fill = PatternFill(start_color="E6E6E6", end_color="E6E6E6", fill_type="solid")
        
        # Автоширина столбцов
        for col_idx in range(1, len(selected_columns) + 1):
            column_letter = get_column_letter(col_idx)
            ws.column_dimensions[column_letter].auto_size = True
            # Минимальная ширина
            if ws.column_dimensions[column_letter].width < 12:
                ws.column_dimensions[column_letter].width = 12
        
        # Сохраняем файл
        wb.save(file_path)
        return file_path

    @staticmethod
    async def export_to_pdf(
        entries: List[Dict],
        project_name: str,
        start_date: Optional[datetime.date] = None,
        end_date: Optional[datetime.date] = None,
        # PDF параметры бланка
        include_logo: bool = True,
        include_signatures: bool = True,
        include_totals: bool = True,
        company_info: bool = True,
        company_name: str = ""
    ) -> str:
        """
        Экспортирует данные в PDF файл с настраиваемым бланком.
        
        Args:
            entries: Список записей для экспорта
            project_name: Название проекта
            start_date: Начальная дата периода
            end_date: Конечная дата периода
            include_logo: Включать логотип компании
            include_signatures: Включать поля для подписей
            include_totals: Включать итоговые суммы
            company_info: Включать информацию о компании
            company_name: Название компании
            
        Returns:
            Путь к созданному файлу
        """
        # Создаем временный файл
        temp_dir = tempfile.gettempdir()
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"worklog_{project_name}_{timestamp}.pdf"
        file_path = os.path.join(temp_dir, filename)
        
        # Создаем PDF генератор
        pdf_generator = WorkReportPDF(file_path)
        
        # Настраиваем поля отчета
        from ..templates.work_report_template import REPORT_FIELDS
        REPORT_FIELDS["company_info"] = company_info
        REPORT_FIELDS["signatures"] = include_signatures
        REPORT_FIELDS["totals"] = include_totals
        
        # Добавляем заголовок
        pdf_generator.add_header(company_name, project_name)
        
        # Добавляем информацию о периоде
        pdf_generator.add_period_info(start_date, end_date)
        
        # Подготавливаем данные для PDF
        pdf_entries = []
        for entry in entries:
            pdf_entries.append({
                "date": entry.get("date"),
                "work_type": entry.get("work_type", ""),
                "description": entry.get("description", ""),
                "quantity": entry.get("quantity", 0),
                "unit": entry.get("unit", ""),
                "rate": entry.get("rate", 0),
                "sum": entry.get("sum", 0)
            })
        
        # Добавляем таблицу записей
        pdf_generator.add_work_entries_table(pdf_entries)
        
        # Подготавливаем сводку по типам работ
        summary_data = ExportService._prepare_summary_data(entries)
        pdf_generator.add_summary_table(summary_data)
        
        # Добавляем поля для подписей
        if include_signatures:
            pdf_generator.add_signatures()
        
        # Генерируем PDF
        pdf_generator.generate()
        return file_path

    @staticmethod
    def _prepare_summary_data(entries: List[Dict]) -> List[Dict]:
        """
        Подготавливает сводные данные по типам работ.
        
        Args:
            entries: Список записей
            
        Returns:
            Список сводных данных
        """
        summary = {}
        
        for entry in entries:
            work_type = entry.get("work_type", "")
            unit = entry.get("unit", "")
            quantity = entry.get("quantity", 0)
            sum_value = entry.get("sum", 0)
            
            key = (work_type, unit)
            
            if key not in summary:
                summary[key] = {
                    "work_type": work_type,
                    "unit": unit,
                    "total_quantity": 0,
                    "total_sum": 0
                }
            
            summary[key]["total_quantity"] += quantity
            summary[key]["total_sum"] += sum_value
        
        return list(summary.values())

    @staticmethod
    def cleanup_temp_files(file_path: str):
        """
        Удаляет временные файлы после отправки.
        
        Args:
            file_path: Путь к файлу для удаления
        """
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
        except Exception:
            pass  # Игнорируем ошибки удаления

# Примеры использования:
"""
# Экспорт в Excel
excel_path = await ExportService.export_to_excel(
    entries=entries_data,
    project_name="Проект Альфа",
    selected_columns=["Дата", "Тип работы", "Количество", "Сумма"],
    start_date=datetime.date(2023, 1, 1),
    end_date=datetime.date(2023, 1, 31)
)

# Экспорт в PDF с настройками бланка
pdf_path = await ExportService.export_to_pdf(
    entries=entries_data,
    project_name="Проект Альфа",
    start_date=datetime.date(2023, 1, 1),
    end_date=datetime.date(2023, 1, 31),
    include_logo=True,
    include_signatures=True,
    include_totals=True,
    company_info=True,
    company_name="ООО Компания"
)

# Очистка временных файлов
ExportService.cleanup_temp_files(excel_path)
ExportService.cleanup_temp_files(pdf_path)
"""
