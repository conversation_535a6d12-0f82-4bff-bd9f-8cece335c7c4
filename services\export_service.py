"""
ExportService для экспорта данных в Excel и PDF форматы

Адаптирован из examples под aiogram v3.x и новую архитектуру.
Предоставляет функции экспорта отчетов в различные форматы.
"""
import os
import tempfile
import logging
from typing import List, Dict, Optional, Any
from datetime import datetime, date
from pathlib import Path

logger = logging.getLogger(__name__)

try:
    from openpyxl import Workbook
    from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
    from openpyxl.utils import get_column_letter
    EXCEL_AVAILABLE = True
except ImportError:
    logger.warning("openpyxl не установлен. Excel экспорт недоступен.")
    EXCEL_AVAILABLE = False

try:
    from reportlab.lib.pagesizes import A4
    from reportlab.lib import colors
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import mm
    from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    PDF_AVAILABLE = True
except ImportError:
    logger.warning("reportlab не установлен. PDF экспорт недоступен.")
    PDF_AVAILABLE = False


class ExportService:
    """Сервис для экспорта данных в различные форматы"""

    # Доступные столбцы для Excel экспорта
    AVAILABLE_COLUMNS = {
        "Дата": "date",
        "Рабочий": "worker",
        "Проект": "project",
        "Тип работы": "work_type",
        "Описание": "description",
        "Часы": "hours",
        "Ставка": "rate",
        "Сумма": "amount"
    }

    @staticmethod
    async def export_to_excel(
        report_data: Dict[str, Any],
        filename_prefix: str = "worklog_report",
        selected_columns: Optional[List[str]] = None
    ) -> Optional[str]:
        """
        Экспорт отчета в Excel файл
        
        Args:
            report_data: Данные отчета из ReportService
            filename_prefix: Префикс имени файла
            selected_columns: Выбранные столбцы (если None - все)
            
        Returns:
            Путь к созданному файлу или None при ошибке
        """
        if not EXCEL_AVAILABLE:
            logger.error("Excel экспорт недоступен - openpyxl не установлен")
            return None
        
        try:
            # Если столбцы не выбраны, используем все
            if not selected_columns:
                selected_columns = list(ExportService.AVAILABLE_COLUMNS.keys())
            
            # Создаем временный файл
            temp_dir = tempfile.gettempdir()
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{filename_prefix}_{timestamp}.xlsx"
            file_path = os.path.join(temp_dir, filename)
            
            # Создаем книгу Excel
            wb = Workbook()
            ws = wb.active
            ws.title = "Отчет по работам"
            
            # Заголовок отчета
            ws.merge_cells('A1:H1')
            ws['A1'] = f"ОТЧЕТ ПО РАБОТАМ"
            ws['A1'].font = Font(size=16, bold=True)
            ws['A1'].alignment = Alignment(horizontal='center')
            
            # Информация о периоде
            row = 2
            if 'period' in report_data:
                period = report_data['period']
                start_date = period.get('start_date')
                end_date = period.get('end_date')
                
                if start_date and end_date:
                    period_text = f"Период: {start_date.strftime('%d.%m.%Y')} - {end_date.strftime('%d.%m.%Y')}"
                else:
                    period_text = "Период: За всё время"
                
                ws.merge_cells(f'A{row}:H{row}')
                ws[f'A{row}'] = period_text
                ws[f'A{row}'].alignment = Alignment(horizontal='center')
                row += 1
            
            # Сводная информация
            if 'summary' in report_data:
                summary = report_data['summary']
                ws.merge_cells(f'A{row}:H{row}')
                ws[f'A{row}'] = (f"Записей: {summary.get('total_entries', 0)}, "
                               f"Часов: {summary.get('total_hours', 0):.1f}, "
                               f"Сумма: {summary.get('total_amount', 0):.2f} €")
                ws[f'A{row}'].alignment = Alignment(horizontal='center')
                row += 2
            
            # Заголовки столбцов
            header_row = row
            for col_idx, column_name in enumerate(selected_columns, 1):
                cell = ws.cell(row=header_row, column=col_idx, value=column_name)
                cell.font = Font(bold=True, color="FFFFFF")
                cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                cell.alignment = Alignment(horizontal='center')
                cell.border = Border(
                    left=Side(style='thin'), right=Side(style='thin'),
                    top=Side(style='thin'), bottom=Side(style='thin')
                )
            
            # Данные
            entries = report_data.get('entries', [])
            total_amount = 0
            
            for entry_idx, entry in enumerate(entries, 1):
                data_row = header_row + entry_idx
                
                for col_idx, column_name in enumerate(selected_columns, 1):
                    field_name = ExportService.AVAILABLE_COLUMNS[column_name]
                    value = entry.get(field_name, "")
                    
                    # Форматирование значений
                    if field_name == "date" and value:
                        if isinstance(value, str):
                            value = value
                        else:
                            value = value.strftime("%d.%m.%Y")
                    elif field_name in ["hours", "rate", "amount"] and value:
                        value = float(value)
                    
                    cell = ws.cell(row=data_row, column=col_idx, value=value)
                    cell.border = Border(
                        left=Side(style='thin'), right=Side(style='thin'),
                        top=Side(style='thin'), bottom=Side(style='thin')
                    )
                    
                    # Выравнивание для числовых значений
                    if field_name in ["hours", "rate", "amount"]:
                        cell.alignment = Alignment(horizontal='right')
                        if field_name in ["rate", "amount"]:
                            cell.number_format = '#,##0.00'
                
                # Подсчет общей суммы
                if "amount" in entry:
                    total_amount += float(entry["amount"])
            
            # Итоговая строка
            if "Сумма" in selected_columns and entries:
                total_row = header_row + len(entries) + 1
                amount_col_idx = selected_columns.index("Сумма") + 1
                
                # Подпись "ИТОГО"
                if amount_col_idx > 1:
                    cell = ws.cell(row=total_row, column=amount_col_idx - 1, value="ИТОГО:")
                    cell.font = Font(bold=True)
                    cell.alignment = Alignment(horizontal='right')
                
                # Сумма
                cell = ws.cell(row=total_row, column=amount_col_idx, value=total_amount)
                cell.font = Font(bold=True)
                cell.number_format = '#,##0.00'
                cell.alignment = Alignment(horizontal='right')
                cell.fill = PatternFill(start_color="E6E6E6", end_color="E6E6E6", fill_type="solid")
            
            # Автоширина столбцов
            for col_idx in range(1, len(selected_columns) + 1):
                column_letter = get_column_letter(col_idx)
                ws.column_dimensions[column_letter].auto_size = True
                # Минимальная ширина
                if ws.column_dimensions[column_letter].width < 12:
                    ws.column_dimensions[column_letter].width = 12
            
            # Сохраняем файл
            wb.save(file_path)
            logger.info(f"Excel файл создан: {file_path}")
            return file_path
            
        except Exception as e:
            logger.error(f"Ошибка при создании Excel файла: {e}")
            return None

    @staticmethod
    async def export_to_pdf(
        report_data: Dict[str, Any],
        filename_prefix: str = "worklog_report",
        company_name: str = "Компания",
        include_signatures: bool = True
    ) -> Optional[str]:
        """
        Экспорт отчета в PDF файл
        
        Args:
            report_data: Данные отчета из ReportService
            filename_prefix: Префикс имени файла
            company_name: Название компании
            include_signatures: Включать поля для подписей
            
        Returns:
            Путь к созданному файлу или None при ошибке
        """
        if not PDF_AVAILABLE:
            logger.error("PDF экспорт недоступен - reportlab не установлен")
            return None
        
        try:
            # Создаем временный файл
            temp_dir = tempfile.gettempdir()
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{filename_prefix}_{timestamp}.pdf"
            file_path = os.path.join(temp_dir, filename)
            
            # Создаем PDF документ
            doc = SimpleDocTemplate(file_path, pagesize=A4)
            story = []
            
            # Стили
            styles = getSampleStyleSheet()
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=16,
                spaceAfter=30,
                alignment=1  # CENTER
            )
            
            # Заголовок
            story.append(Paragraph(f"ОТЧЕТ ПО РАБОТАМ", title_style))
            story.append(Paragraph(f"<b>{company_name}</b>", styles['Normal']))
            story.append(Spacer(1, 20))
            
            # Период
            if 'period' in report_data:
                period = report_data['period']
                start_date = period.get('start_date')
                end_date = period.get('end_date')
                
                if start_date and end_date:
                    period_text = f"Период: {start_date.strftime('%d.%m.%Y')} - {end_date.strftime('%d.%m.%Y')}"
                else:
                    period_text = "Период: За всё время"
                
                story.append(Paragraph(period_text, styles['Normal']))
                story.append(Spacer(1, 20))
            
            # Сводная информация
            if 'summary' in report_data:
                summary = report_data['summary']
                summary_text = (f"Записей работ: {summary.get('total_entries', 0)}<br/>"
                              f"Общее время: {summary.get('total_hours', 0):.1f} часов<br/>"
                              f"Общая сумма: {summary.get('total_amount', 0):.2f} €")
                story.append(Paragraph(summary_text, styles['Normal']))
                story.append(Spacer(1, 20))
            
            # Таблица с данными
            entries = report_data.get('entries', [])
            if entries:
                # Заголовки таблицы
                table_data = [['Дата', 'Рабочий', 'Проект', 'Тип работы', 'Часы', 'Ставка', 'Сумма']]
                
                # Данные
                for entry in entries:
                    row = [
                        entry.get('date', ''),
                        entry.get('worker', ''),
                        entry.get('project', ''),
                        entry.get('work_type', ''),
                        f"{entry.get('hours', 0):.1f}",
                        f"{entry.get('rate', 0):.2f}",
                        f"{entry.get('amount', 0):.2f}"
                    ]
                    table_data.append(row)
                
                # Создаем таблицу
                table = Table(table_data)
                table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 10),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))
                
                story.append(table)
            
            # Поля для подписей
            if include_signatures:
                story.append(Spacer(1, 40))
                story.append(Paragraph("Подписи:", styles['Normal']))
                story.append(Spacer(1, 20))
                story.append(Paragraph("Директор: ________________", styles['Normal']))
                story.append(Spacer(1, 10))
                story.append(Paragraph("Дата: ________________", styles['Normal']))
            
            # Создаем PDF
            doc.build(story)
            logger.info(f"PDF файл создан: {file_path}")
            return file_path
            
        except Exception as e:
            logger.error(f"Ошибка при создании PDF файла: {e}")
            return None

    @staticmethod
    def get_available_formats() -> Dict[str, bool]:
        """
        Получение доступных форматов экспорта
        
        Returns:
            Словарь с доступными форматами
        """
        return {
            'excel': EXCEL_AVAILABLE,
            'pdf': PDF_AVAILABLE
        }
