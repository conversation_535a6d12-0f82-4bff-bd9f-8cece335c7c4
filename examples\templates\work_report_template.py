"""
Шаблон PDF отчета для экспорта данных Worklog Bot.

Настраиваемые параметры бланка:
- Размер страницы и отступы
- Шрифты и цвета
- Логотип компании
- Поля для подписей
- Структура таблиц
- Итоговые суммы

Используется библиотека ReportLab для генерации PDF.
"""
from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import cm
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import os
from datetime import datetime
from typing import List, Dict, Optional

# Конфигурация PDF бланка
PDF_CONFIG = {
    "page_size": A4,
    "margins": {
        "top": 2.5 * cm,
        "bottom": 2.5 * cm,
        "left": 2.0 * cm,
        "right": 2.0 * cm
    },
    "fonts": {
        "regular": "DejaVuSans.ttf",
        "bold": "DejaVuSans-Bold.ttf"
    },
    "colors": {
        "header": colors.Color(0.18, 0.53, 0.67),  # #2E86AB
        "text": colors.Color(0.2, 0.2, 0.2),        # #333333
        "border": colors.Color(0.8, 0.8, 0.8),     # #CCCCCC
        "background": colors.Color(0.95, 0.95, 0.95)  # #F5F5F5
    },
    "logo": {
        "width": 3 * cm,
        "height": 1.5 * cm,
        "path": "assets/company_logo.png"  # Путь к логотипу
    }
}

# Поля отчета (настраиваемые)
REPORT_FIELDS = {
    "company_info": True,      # Информация о компании
    "project_details": True,   # Детали проекта
    "work_summary": True,      # Сводка по работам
    "detailed_entries": True,  # Детальные записи
    "signatures": True,        # Поля для подписей
    "totals": True,           # Итоговые суммы
    "qr_code": False          # QR код для верификации
}

class WorkReportPDF:
    """Генератор PDF отчетов по работам."""
    
    def __init__(self, output_path: str):
        """
        Инициализация генератора PDF.
        
        Args:
            output_path: Путь для сохранения PDF файла
        """
        self.output_path = output_path
        self.doc = SimpleDocTemplate(
            output_path,
            pagesize=PDF_CONFIG["page_size"],
            topMargin=PDF_CONFIG["margins"]["top"],
            bottomMargin=PDF_CONFIG["margins"]["bottom"],
            leftMargin=PDF_CONFIG["margins"]["left"],
            rightMargin=PDF_CONFIG["margins"]["right"]
        )
        self.styles = getSampleStyleSheet()
        self.story = []
        
        # Регистрация шрифтов (если доступны)
        self._register_fonts()
        
        # Создание пользовательских стилей
        self._create_custom_styles()
    
    def _register_fonts(self):
        """Регистрация пользовательских шрифтов."""
        try:
            font_dir = "fonts/"  # Путь к папке со шрифтами
            if os.path.exists(os.path.join(font_dir, PDF_CONFIG["fonts"]["regular"])):
                pdfmetrics.registerFont(TTFont('DejaVuSans', 
                    os.path.join(font_dir, PDF_CONFIG["fonts"]["regular"])))
                pdfmetrics.registerFont(TTFont('DejaVuSans-Bold', 
                    os.path.join(font_dir, PDF_CONFIG["fonts"]["bold"])))
        except Exception:
            # Используем стандартные шрифты если пользовательские недоступны
            pass
    
    def _create_custom_styles(self):
        """Создание пользовательских стилей."""
        self.custom_styles = {
            'Title': ParagraphStyle(
                'CustomTitle',
                parent=self.styles['Title'],
                fontSize=18,
                textColor=PDF_CONFIG["colors"]["header"],
                spaceAfter=20,
                alignment=1  # Центрирование
            ),
            'Heading': ParagraphStyle(
                'CustomHeading',
                parent=self.styles['Heading1'],
                fontSize=14,
                textColor=PDF_CONFIG["colors"]["header"],
                spaceAfter=12
            ),
            'Normal': ParagraphStyle(
                'CustomNormal',
                parent=self.styles['Normal'],
                fontSize=10,
                textColor=PDF_CONFIG["colors"]["text"]
            )
        }
    
    def add_header(self, company_name: str = "", project_name: str = ""):
        """
        Добавляет заголовок отчета.
        
        Args:
            company_name: Название компании
            project_name: Название проекта
        """
        # Логотип компании (если включен)
        if REPORT_FIELDS["company_info"] and os.path.exists(PDF_CONFIG["logo"]["path"]):
            try:
                logo = Image(
                    PDF_CONFIG["logo"]["path"],
                    width=PDF_CONFIG["logo"]["width"],
                    height=PDF_CONFIG["logo"]["height"]
                )
                self.story.append(logo)
                self.story.append(Spacer(1, 0.5 * cm))
            except Exception:
                pass  # Игнорируем ошибки загрузки логотипа
        
        # Заголовок
        title = f"ОТЧЕТ ПО РАБОТАМ"
        if project_name:
            title += f"<br/>Проект: {project_name}"
        
        self.story.append(Paragraph(title, self.custom_styles['Title']))
        
        # Информация о компании
        if REPORT_FIELDS["company_info"] and company_name:
            company_info = f"<b>Компания:</b> {company_name}"
            self.story.append(Paragraph(company_info, self.custom_styles['Normal']))
            self.story.append(Spacer(1, 0.3 * cm))
        
        # Дата генерации
        date_str = datetime.now().strftime("%d.%m.%Y %H:%M")
        date_info = f"<b>Дата формирования:</b> {date_str}"
        self.story.append(Paragraph(date_info, self.custom_styles['Normal']))
        self.story.append(Spacer(1, 0.5 * cm))
    
    def add_period_info(self, start_date: Optional[datetime] = None, end_date: Optional[datetime] = None):
        """
        Добавляет информацию о периоде отчета.
        
        Args:
            start_date: Начальная дата
            end_date: Конечная дата
        """
        if start_date and end_date:
            period = f"<b>Период:</b> {start_date.strftime('%d.%m.%Y')} - {end_date.strftime('%d.%m.%Y')}"
        elif start_date:
            period = f"<b>Период:</b> с {start_date.strftime('%d.%m.%Y')}"
        elif end_date:
            period = f"<b>Период:</b> до {end_date.strftime('%d.%m.%Y')}"
        else:
            period = "<b>Период:</b> За всё время"
        
        self.story.append(Paragraph(period, self.custom_styles['Normal']))
        self.story.append(Spacer(1, 0.5 * cm))
    
    def add_work_entries_table(self, entries: List[Dict]):
        """
        Добавляет таблицу с записями о работах.
        
        Args:
            entries: Список записей о работах
                Формат: [{"date": date, "work_type": str, "description": str, 
                         "quantity": float, "unit": str, "rate": float, "sum": float}]
        """
        if not REPORT_FIELDS["detailed_entries"] or not entries:
            return
        
        # Заголовок таблицы
        self.story.append(Paragraph("ДЕТАЛЬНЫЕ ЗАПИСИ", self.custom_styles['Heading']))
        
        # Данные таблицы
        table_data = [
            ["№", "Дата", "Тип работы", "Описание", "Кол-во", "Ед.изм.", "Ставка", "Сумма"]
        ]
        
        total_sum = 0
        for i, entry in enumerate(entries, 1):
            row = [
                str(i),
                entry["date"].strftime("%d.%m.%Y") if entry["date"] else "",
                entry["work_type"] or "",
                entry["description"] or "",
                f"{entry['quantity']:.2f}" if entry["quantity"] else "",
                entry["unit"] or "",
                f"{entry['rate']:.2f}" if entry["rate"] else "",
                f"{entry['sum']:.2f}" if entry["sum"] else ""
            ]
            table_data.append(row)
            total_sum += entry.get("sum", 0)
        
        # Итоговая строка
        if REPORT_FIELDS["totals"]:
            table_data.append([
                "", "", "", "", "", "", "ИТОГО:", f"{total_sum:.2f}"
            ])
        
        # Создание таблицы
        table = Table(table_data, colWidths=[
            1*cm, 2*cm, 3*cm, 4*cm, 1.5*cm, 1.5*cm, 2*cm, 2*cm
        ])
        
        # Стиль таблицы
        table.setStyle(TableStyle([
            # Заголовок
            ('BACKGROUND', (0, 0), (-1, 0), PDF_CONFIG["colors"]["header"]),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 9),
            
            # Данные
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 8),
            ('GRID', (0, 0), (-1, -1), 1, PDF_CONFIG["colors"]["border"]),
            
            # Итоговая строка
            ('BACKGROUND', (0, -1), (-1, -1), PDF_CONFIG["colors"]["background"]),
            ('FONTNAME', (0, -1), (-1, -1), 'Helvetica-Bold'),
            
            # Выравнивание
            ('ALIGN', (0, 0), (0, -1), 'CENTER'),  # Номера
            ('ALIGN', (4, 1), (7, -1), 'RIGHT'),   # Числовые значения
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ]))
        
        self.story.append(table)
        self.story.append(Spacer(1, 0.5 * cm))
    
    def add_summary_table(self, summary_data: List[Dict]):
        """
        Добавляет сводную таблицу по типам работ.
        
        Args:
            summary_data: Сводные данные по типам работ
                Формат: [{"work_type": str, "total_quantity": float, 
                         "unit": str, "total_sum": float}]
        """
        if not REPORT_FIELDS["work_summary"] or not summary_data:
            return
        
        self.story.append(Paragraph("СВОДКА ПО ТИПАМ РАБОТ", self.custom_styles['Heading']))
        
        table_data = [["Тип работы", "Общее количество", "Ед. изм.", "Общая сумма"]]
        
        total_amount = 0
        for item in summary_data:
            row = [
                item["work_type"],
                f"{item['total_quantity']:.2f}",
                item["unit"],
                f"{item['total_sum']:.2f}"
            ]
            table_data.append(row)
            total_amount += item["total_sum"]
        
        # Итоговая строка
        if REPORT_FIELDS["totals"]:
            table_data.append(["", "", "ИТОГО:", f"{total_amount:.2f}"])
        
        table = Table(table_data, colWidths=[6*cm, 3*cm, 2*cm, 3*cm])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), PDF_CONFIG["colors"]["header"]),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, PDF_CONFIG["colors"]["border"]),
            ('BACKGROUND', (0, -1), (-1, -1), PDF_CONFIG["colors"]["background"]),
            ('FONTNAME', (0, -1), (-1, -1), 'Helvetica-Bold'),
            ('ALIGN', (1, 1), (-1, -1), 'RIGHT'),
        ]))
        
        self.story.append(table)
        self.story.append(Spacer(1, 0.5 * cm))
    
    def add_signatures(self):
        """Добавляет поля для подписей."""
        if not REPORT_FIELDS["signatures"]:
            return
        
        self.story.append(Spacer(1, 1 * cm))
        
        # Поля для подписей
        signature_data = [
            ["Исполнитель:", "_" * 30, "Дата:", "_" * 15],
            ["", "", "", ""],
            ["Руководитель:", "_" * 30, "Дата:", "_" * 15]
        ]
        
        signature_table = Table(signature_data, colWidths=[3*cm, 5*cm, 2*cm, 3*cm])
        signature_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('ALIGN', (0, 0), (0, -1), 'LEFT'),
            ('ALIGN', (2, 0), (2, -1), 'LEFT'),
            ('VALIGN', (0, 0), (-1, -1), 'BOTTOM'),
        ]))
        
        self.story.append(signature_table)
    
    def generate(self):
        """Генерирует PDF файл."""
        self.doc.build(self.story)
        return self.output_path

# Пример использования:
"""
# Создание PDF отчета
pdf_generator = WorkReportPDF("output/work_report.pdf")

# Добавление заголовка
pdf_generator.add_header("ООО Компания", "Проект Альфа")

# Добавление периода
pdf_generator.add_period_info(start_date, end_date)

# Добавление записей
entries = [
    {
        "date": datetime(2023, 1, 15),
        "work_type": "Монтаж",
        "description": "Установка оборудования",
        "quantity": 8.0,
        "unit": "час",
        "rate": 1000.0,
        "sum": 8000.0
    }
]
pdf_generator.add_work_entries_table(entries)

# Добавление сводки
summary = [
    {
        "work_type": "Монтаж",
        "total_quantity": 40.0,
        "unit": "час",
        "total_sum": 40000.0
    }
]
pdf_generator.add_summary_table(summary)

# Добавление подписей
pdf_generator.add_signatures()

# Генерация файла
pdf_generator.generate()
"""
